/**
 * Supabase Client
 * This is the main Supabase client instance used throughout the application.
 * All Supabase interactions should use this client to ensure consistency.
 */
import { createClient } from '@supabase/supabase-js'

// Get Supabase credentials from environment variables
// For Vite, environment variables must be prefixed with VITE_
// If environment variables are not available, fall back to hardcoded values
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_KEY

// Check if environment variables are available
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase configuration missing. Please set VITE_SUPABASE_URL and VITE_SUPABASE_KEY environment variables.')
}

// Log which values are being used (for debugging)
console.log(`Supabase URL: ${supabaseUrl.substring(0, 20)}...`)
console.log(`Using ${import.meta.env.VITE_SUPABASE_URL ? 'environment' : 'hardcoded'} Supabase URL`)

// Create and export the Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  }
})

// Export default for convenience
export default supabase