# Backend Server for GT Rebuild

This Express.js server provides API endpoints for interacting with the Stability AI API and other services.

## Stability AI Image Processing

### Handling Image Generation Responses

The server includes functionality to handle responses from the Stability AI API, specifically for image generation results that return Base64-encoded image data.

#### Endpoint: `/api/ai/handle-image-generation`

This endpoint processes Stability AI image generation responses, which typically include a Base64-encoded image in the `result` field and a `seed` value.

**Request:**

```json
{
  "apiResponse": {
    "result": "UklGRoKGBQBXRUJQVlA4IHaGBQBQKAGdASqAAIAAPm0uk0WkIqGhLBgABIJQADdMoR3VT9P8...",
    "seed": 12345
  },
  "saveToFile": true,
  "filename": "my_generated_image"
}
```

Parameters:
- `apiResponse`: The response from the Stability AI API (can be the full JSON object or a string)
- `saveToFile` (optional): <PERSON><PERSON><PERSON> indicating whether to save the image to a file (default: false)
- `filename` (optional): The name to use when saving the file (required if saveToFile is true)

**Response:**

```json
{
  "success": true,
  "contentType": "image/webp",
  "imageSize": 123456,
  "seed": 12345,
  "dataUrl": "data:image/webp;base64,UklGRoKGBQBXRUJQVlA4IHaGBQBQKAGdASqAAIAAPm0uk0WkIqGhLBgABIJQADdMoR3VT9P8...",
  "savedFilePath": "/path/to/generated_images/my_generated_image.webp",
  "message": "Image processed successfully"
}
```

The response includes:
- `contentType`: The detected MIME type of the image (usually image/webp)
- `imageSize`: Size of the decoded image in bytes
- `seed`: The seed value from the original API response
- `dataUrl`: A data URL that can be used directly in HTML img tags or CSS
- `savedFilePath`: Path where the file was saved (if saveToFile was true)

### Example Usage

See the example HTML file in `examples/handle-stability-response.html` for a complete demonstration of how to use this endpoint.

#### Client-Side Code Example

```javascript
async function processStabilityResponse(apiResponse) {
  try {
    const response = await fetch('/api/ai/handle-image-generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        apiResponse: apiResponse,
        saveToFile: true,
        filename: 'my_generated_image'
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Display the image
      const imageElement = document.createElement('img');
      imageElement.src = result.dataUrl;
      document.getElementById('imageContainer').appendChild(imageElement);
      
      // Show other information
      console.log(`Image processed successfully! Size: ${result.imageSize} bytes, Seed: ${result.seed}`);
      
      if (result.savedFilePath) {
        console.log(`File saved at: ${result.savedFilePath}`);
      }
    } else {
      console.error(`Error: ${result.error}`);
    }
  } catch (error) {
    console.error('Error processing image:', error);
  }
}
```

### Working with the Generated Images

#### Display in Browser

Use the `dataUrl` from the response to display the image:

```html
<img src="data:image/webp;base64,UklGRoKGBQBXRUJQVlA4IHaGBQBQKAGdASqAAIAAPm0uk0WkIqGhLBgABIJQADdMoR3VT9P8...">
```

#### Save to Database

For database storage, you have several options:
1. Store the Base64 string directly (takes more space)
2. Store the binary data as a BLOB
3. Save the file to disk and store the file path in the database

#### Send to Frontend

The response from the `/api/ai/handle-image-generation` endpoint includes a `dataUrl` that can be used directly in your frontend application.

## Test Endpoints

### `/api/test-image-generation-handler`

This endpoint provides information about how to use the image generation handler. It returns example usage and steps for processing Stability AI responses.

## Running the Server

1. Install dependencies: `npm install`
2. Set up environment variables in `.env` file (see `.env.example`)
3. Start the server: `npm start`

The server will run on the port specified in the `.env` file or default to port 3001.