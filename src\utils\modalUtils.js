/**
 * Utility functions for handling modals and scrollbar-related layout issues
 */

/**
 * Calculates the width of the scrollbar and sets it as a CSS variable
 * This helps prevent layout shifts when modals open and the scrollbar disappears
 */
export function calculateScrollbarWidth() {
  // Create a temporary div to measure scrollbar width
  const outer = document.createElement('div');
  outer.style.visibility = 'hidden';
  outer.style.overflow = 'scroll';
  document.body.appendChild(outer);
  
  // Create an inner div to measure the difference
  const inner = document.createElement('div');
  outer.appendChild(inner);
  
  // Calculate the scrollbar width
  const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
  
  // Clean up
  outer.parentNode.removeChild(outer);
  
  // Set the scrollbar width as a CSS variable
  document.documentElement.style.setProperty('--scrollbar-width', `${scrollbarWidth}px`);
  
  return scrollbarWidth;
}

/**
 * Handles body and html classes when opening a modal
 * Prevents scrolling on the main page and adjusts for scrollbar disappearance
 */
export function openModal() {
  // Calculate scrollbar width
  calculateScrollbarWidth();
  
  // Add classes to prevent scrolling and adjust for scrollbar
  document.documentElement.classList.add('modal-open');
}

/**
 * Handles body and html classes when closing a modal
 * Restores scrolling on the main page
 */
export function closeModal() {
  // Remove classes
  document.documentElement.classList.remove('modal-open');
}