#!/bin/bash

# This script is used by Render.com to start the background worker
# It installs dependencies and runs the worker script

# Print environment info
echo "Starting CarPages export worker on Render.com"
echo "Node version: $(node -v)"
echo "NPM version: $(npm -v)"

# Make sure we're in the project root directory
cd "$(dirname "$0")/.."
echo "Current directory: $(pwd)"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install
else
  echo "Dependencies already installed."
fi

# Run the worker script
echo "Starting worker script..."
node scripts/render-export-worker.js