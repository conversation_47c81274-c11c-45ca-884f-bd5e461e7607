<template>
  <div class="about-page-3d">
    <div ref="canvasContainer" class="canvas-container"></div>

    <!-- Loading Overlay -->
    <div v-if="isLoading" class="loading-overlay">
      {{ loadingProgress }}% Loaded...
    </div>

    <!-- Arrow Key Prompt -->
    <div
      v-if="showArrowPrompt"
      class="arrow-key-prompt"
      :class="{ 'fade-out': isFadingArrowPrompt }"
    >
      <!-- Arrow keys display -->
      <div class="arrow-key up">↑</div>
      <div class="arrow-row">
        <div class="arrow-key left">←</div>
        <div class="arrow-key down">↓</div>
        <div class="arrow-key right">→</div>
      </div>
      <div class="prompt-text">Use Arrow Keys to Drive</div>
    </div>

    <!-- Mobile Touch Controls -->
    <div class="mobile-controls">
       <!-- Buttons -->
      <div class="mobile-button left" @touchstart.prevent="handleTouchStart('ArrowLeft')" @touchend.prevent="handleTouchEnd('ArrowLeft')" @touchcancel.prevent="handleTouchEnd('ArrowLeft')">←</div>
      <div class="mobile-button right" @touchstart.prevent="handleTouchStart('ArrowRight')" @touchend.prevent="handleTouchEnd('ArrowRight')" @touchcancel.prevent="handleTouchEnd('ArrowRight')">→</div>
      <div class="mobile-button up" @touchstart.prevent="handleTouchStart('ArrowUp')" @touchend.prevent="handleTouchEnd('ArrowUp')" @touchcancel.prevent="handleTouchEnd('ArrowUp')">↑</div>
      <div class="mobile-button down" @touchstart.prevent="handleTouchStart('ArrowDown')" @touchend.prevent="handleTouchEnd('ArrowDown')" @touchcancel.prevent="handleTouchEnd('ArrowDown')">↓</div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, reactive, watch, computed } from 'vue';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import businessStore from '../store/business';

export default {
name: 'AboutPageShowroomStyledOptimizedV3FixedV3TimelineOrderFixAttempt2SquareRoom',
setup() {
  // --- Refs & State --- (Unchanged)
  const canvasContainer = ref(null);
  const isLoading = ref(true);
  const loadingProgress = ref(0);
  const showArrowPrompt = ref(false);
  const isFadingArrowPrompt = ref(false);
  let promptFadeTimeoutId = null;
  let promptRemoveTimeoutId = null;

  // --- Timeline Content for Plaques --- (Now from business store)
  // Computed property to get timeline events from business store or use fallback
  const timelineEvents = computed(() => {
    // Initialize business store if not already initialized
    if (!businessStore.isInitialized.value) {
      businessStore.initStore();
    }
    
    // Get timeline events from business store
    const storeTimeline = businessStore.getBusinessTimeline.value;
    
    // If timeline events exist in the store and are not empty, use them
    if (storeTimeline && storeTimeline.length > 0) {
      return storeTimeline;
    }
    
    // Otherwise, use fallback timeline events
    return [
      { year: "2018", event: "Community Choice Award: Voted 'Best Independent Performance Shop' by local enthusiasts.", imageUrl: '' },
      { year: "2019", event: "Dyno Tuning Launched: Introduced state-of-the-art dynamometer services.", imageUrl: '' },
      { year: "2020", event: "Official Tuning Partner: Partnered with [Performance Brand] for certified upgrades.", imageUrl: '' },
      { year: "2021", event: "GT Motor Sports Founded: A passion for performance sparks the beginning of our journey.", imageUrl: '' },
      { year: "2022", event: "First Major Project Completed: Successfully tuned and restored a classic [Make Model].", imageUrl: '' },
      { year: "2023", event: "Showroom Expansion: Moved into our current, larger facility to better serve our clients.", imageUrl: '' },
      { year: "2024", event: "Inaugural GT Track Day: Hosted our first successful customer appreciation track event.", imageUrl: '' },
      { year: "2025", event: "7th Anniversary Celebration: Marked seven years of dedication to speed and precision.", imageUrl: '' },
      { year: "2026", event: "Advanced Diagnostics Suite: Upgraded our tools for comprehensive vehicle health analysis.", imageUrl: '' },
    ];
  });

  // --- NEW: Define the desired VISUAL display order for the plaques ---
  // This maps the plaque position index (0-8) to the YEAR that should be displayed there.
  // This preserves the original visual layout seen in the game.
  const plaqueDisplayOrderMap = {
    0: "2021", // First plaque (back wall left)
    1: "2022", // Second plaque (back wall middle)
    2: "2023", // Third plaque (back wall right)
    3: "2020", // Fourth plaque (left wall back)
    4: "2019", // Fifth plaque (left wall middle)
    5: "2018", // Sixth plaque (left wall front)
    6: "2024", // Seventh plaque (right wall back)
    7: "2025", // Eighth plaque (right wall middle)
    8: "2026", // Ninth plaque (right wall front)
  };


  // --- Three.js Variables --- (Unchanged)
  let scene, camera, renderer, carModel, clock, textureLoader, loadingManager;
  let animationFrameId = null;
  let currentLookAtTarget = new THREE.Vector3();
  const collidableObjects = [];
  let landscapeTexture = null;
  let placeholderPlaqueTexture = null;
  let groundPlane = null;
  let gridHelper = null;
  let landscapePlane = null;
  let ceilingPlane = null;
  const textMeshes = []; // Still used to track meshes for disposal

  // --- Reusable Three.js Objects --- (Unchanged)
  const _vector3_1 = new THREE.Vector3(); const _vector3_2 = new THREE.Vector3(); const _vector3_3 = new THREE.Vector3(); const _box3_1 = new THREE.Box3(); const _box3_2 = new THREE.Box3(); const _matrix = new THREE.Matrix4(); const _position = new THREE.Vector3(); const _quaternion = new THREE.Quaternion(); const _scale = new THREE.Vector3(1, 1, 1); const _hideMatrix = new THREE.Matrix4().makeScale(0,0,0); const _euler = new THREE.Euler(0, 0, 0, 'YXZ'); const vecForward = new THREE.Vector3(); const vecRight = new THREE.Vector3(); const wheelPosLeft = new THREE.Vector3(); const wheelPosRight = new THREE.Vector3(); const _cameraTarget = new THREE.Vector3(); const _cameraOffset = new THREE.Vector3();

  // --- Car State & Control --- (Unchanged)
  const carState = reactive({ speed: 0, acceleration: 0.02, braking: 0.03, maxSpeed: 0.2, maxReverseSpeed: -0.1, turnSpeed: 0.03, friction: 0.98, });
  const keyState = reactive({ ArrowUp: false, ArrowDown: false, ArrowLeft: false, ArrowRight: false, });

  // --- Tire Track Variables --- (Unchanged)
  const MAX_TRACKS = 1500; const TRACK_LIFETIME = 7.0; const TRACK_WIDTH = 0.04; const TRACK_LENGTH = 0.1; const TRACK_OFFSET_Y = 0.02; const TRACK_SPAWN_THRESHOLD = 0.001; let trackSpawnTimer = 0; const TRACK_SPAWN_INTERVAL = 0.01; let tireTrackMaterial = null; let trackGeometry = null; const WHEEL_OFFSET_X = 0.1; const WHEEL_OFFSET_Z = 0.15; let tireTrackInstancedMesh = null; const trackInstanceData = []; let nextTrackInstanceIndex = 0;

  // --- Collision Detection Variables --- (Unchanged)
  const carBox = new THREE.Box3(); const carSize = new THREE.Vector3(); let carSizeInitialized = false; const collidableBoxes = [];

  // --- Helper: Create Text Texture with Image --- (MODIFIED: Uses imageUrl from event data)
  function createTextTextureWithImage(text, imageTexture, options = {}, eventData = null) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const resolution = options.resolution || 1024;
      const textPadding = options.padding || 60;
      const fontFace = options.fontFace || 'Verdana, sans-serif';
      const fontSize = options.fontSize || 30;
      const fontColor = options.fontColor || 'rgba(30, 30, 30, 1)';
      const backgroundColor = options.backgroundColor || 'rgba(255, 255, 255, 0.95)';
      const lineHeight = options.lineHeight || fontSize * 1.3;
      const imagePadding = options.imagePadding || 35;
      
      canvas.width = resolution;
      ctx.font = `${fontSize}px ${fontFace}`;
      
      const maxTextWidth = resolution - (textPadding * 2);
      const words = text.split(' ');
      let lines = [];
      let currentLine = words[0] || '';
      
      for (let i = 1; i < words.length; i++) {
          const word = words[i];
          const testLine = currentLine + ' ' + word;
          const metrics = ctx.measureText(testLine);
          const testWidth = metrics.width;
          if (testWidth > maxTextWidth && i > 0) {
              lines.push(currentLine);
              currentLine = word;
          } else {
              currentLine = testLine;
          }
      }
      
      lines.push(currentLine);
      const textHeight = lines.length * lineHeight;
      
      let imageDrawHeight = 0;
      let imageDrawWidth = 0;
      const availableImageWidth = resolution - (textPadding * 2);
      
      // Check if we have a custom image URL from the event data
      const hasCustomImage = eventData && eventData.imageUrl && eventData.imageUrl.trim() !== '';
      
      if (imageTexture && imageTexture.image && imageTexture.image.naturalWidth > 0) {
          const img = imageTexture.image;
          const aspectRatio = img.naturalWidth / img.naturalHeight;
          imageDrawWidth = availableImageWidth;
          imageDrawHeight = imageDrawWidth / aspectRatio;
      } else {
          imageDrawHeight = resolution * 0.25;
          imageDrawWidth = availableImageWidth;
      }
      
      canvas.height = textPadding + imageDrawHeight + imagePadding + textHeight + textPadding;
      
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      const imageDrawX = textPadding;
      const imageDrawY = textPadding;
      
      // If we have a custom image URL, load and draw it
      if (hasCustomImage) {
          const customImg = new Image();
          customImg.crossOrigin = "Anonymous"; // Enable CORS if needed
          
          // Set up a promise to handle the image loading
          const loadImagePromise = new Promise((resolve, reject) => {
              customImg.onload = () => {
                  try {
                      const aspectRatio = customImg.width / customImg.height;
                      const drawWidth = availableImageWidth;
                      const drawHeight = drawWidth / aspectRatio;
                      
                      ctx.drawImage(customImg, imageDrawX, imageDrawY, drawWidth, drawHeight);
                      resolve();
                  } catch (e) {
                      console.error("Error drawing custom image onto canvas:", e);
                      drawPlaceholderImage();
                      resolve();
                  }
              };
              
              customImg.onerror = () => {
                  console.error("Failed to load custom image:", eventData.imageUrl);
                  drawPlaceholderImage();
                  resolve();
              };
          });
          
          // Start loading the image
          customImg.src = eventData.imageUrl;
          
          // We'll return the texture immediately and update it when the image loads
          // This is asynchronous but necessary for the 3D scene to continue rendering
          loadImagePromise.then(() => {
              texture.needsUpdate = true;
          });
      } else {
          drawPlaceholderImage();
      }
      
      function drawPlaceholderImage() {
          if (imageTexture && imageTexture.image && imageTexture.image.naturalWidth > 0) {
              try {
                  ctx.drawImage(imageTexture.image, imageDrawX, imageDrawY, imageDrawWidth, imageDrawHeight);
              } catch (e) {
                  console.error("Error drawing image onto canvas:", e);
                  ctx.fillStyle = 'rgba(150, 150, 150, 0.5)';
                  ctx.fillRect(imageDrawX, imageDrawY, imageDrawWidth, imageDrawHeight);
                  ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                  ctx.textAlign = 'center';
                  ctx.fillText("[Image Error]", imageDrawX + imageDrawWidth / 2, imageDrawY + imageDrawHeight / 2);
              }
          } else {
              ctx.fillStyle = 'rgba(200, 200, 200, 0.7)';
              ctx.fillRect(imageDrawX, imageDrawY, imageDrawWidth, imageDrawHeight);
              ctx.fillStyle = 'rgba(50, 50, 50, 0.8)';
              ctx.textAlign = 'center';
              ctx.fillText("[Placeholder Image]", imageDrawX + imageDrawWidth / 2, imageDrawY + imageDrawHeight / 2);
          }
      }
      
      ctx.font = `${fontSize}px ${fontFace}`;
      ctx.fillStyle = fontColor;
      ctx.textAlign = options.textAlign || 'left';
      ctx.textBaseline = 'top';
      
      let textX = textPadding;
      if (ctx.textAlign === 'center') textX = canvas.width / 2;
      else if (ctx.textAlign === 'right') textX = canvas.width - textPadding;
      
      let textY = imageDrawY + imageDrawHeight + imagePadding;
      
      lines.forEach((line, index) => {
          ctx.fillText(line, textX, textY + (index * lineHeight));
      });
      
      const texture = new THREE.CanvasTexture(canvas);
      texture.needsUpdate = true;
      texture.colorSpace = THREE.SRGBColorSpace;
      return texture;
   }

  // --- Three.js Scene Setup --- (Unchanged)
  function initThreeScene(container) {
      loadingManager = new THREE.LoadingManager( () => { console.log("All assets loaded."); isLoading.value = false; }, (url, itemsLoaded, itemsTotal) => { loadingProgress.value = Math.round((itemsLoaded / itemsTotal) * 100); }, (url) => { console.error(`Error loading ${url}`); isLoading.value = false; } );
      textureLoader = new THREE.TextureLoader(loadingManager);
      scene = new THREE.Scene(); scene.background = new THREE.Color(0x454545); clock = new THREE.Clock(); const aspect = container.clientWidth / container.clientHeight; camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000); camera.position.set(0, 5, 22); renderer = new THREE.WebGLRenderer({ antialias: true }); renderer.setSize(container.clientWidth, container.clientHeight); const maxPixelRatio = 1.5; renderer.setPixelRatio(Math.min(window.devicePixelRatio, maxPixelRatio)); renderer.shadowMap.enabled = true; renderer.shadowMap.type = THREE.PCFSoftShadowMap; renderer.outputColorSpace = THREE.SRGBColorSpace; container.appendChild(renderer.domElement);
      scene.add(new THREE.AmbientLight(0xffffff, 0.4));
      const dirLight = new THREE.DirectionalLight(0xffffff, 1.2);
      const windowWallZ = (40 / 2) + (1 / 2);
      dirLight.position.set(10, 10, windowWallZ + 30);
      dirLight.castShadow = true; dirLight.shadow.mapSize.width = 1024; dirLight.shadow.mapSize.height = 1024; dirLight.shadow.camera.near = 0.5; dirLight.shadow.camera.far = 100; dirLight.shadow.camera.left = -30; dirLight.shadow.camera.right = 30; dirLight.shadow.camera.top = 30; dirLight.shadow.camera.bottom = -30; scene.add(dirLight); dirLight.target.position.set(0, 2, 0); scene.add(dirLight.target);
      landscapeTexture = textureLoader.load('/LANDSCAPE.png', (tex) => { tex.colorSpace = THREE.SRGBColorSpace; console.log("Landscape texture loaded."); });
      placeholderPlaqueTexture = textureLoader.load('/LANDSCAPE.png', (tex) => { tex.colorSpace = THREE.SRGBColorSpace; console.log("Placeholder plaque texture loaded."); if (textMeshes.length > 0) { console.warn("Plaque texture loaded late, attempting to redraw plaque textures."); redrawPlaqueTextures(); } }, undefined, (err) => { console.error("Failed to load placeholder plaque texture:", err); });
   }

  // --- Create Showroom Environment --- (MODIFIED: Plaque creation logic updated)
  function createEnvironment() {
      collidableObjects.length = 0; collidableBoxes.length = 0;
      // Clear textMeshes array here if it wasn't already cleared elsewhere
      textMeshes.length = 0;

      function addCollidable(object) { scene.add(object); collidableObjects.push(object); const box = new THREE.Box3(); object.updateMatrixWorld(true); box.setFromObject(object); if (!box.isEmpty()) { collidableBoxes.push(box); } else { console.warn("Failed to create bounding box for:", object); } }

      let roomWidth = 40; let roomDepth = 50; const roomHeight = 10; const wallThickness = 1; const ceilingThickness = 0.5;
      const squareSize = Math.min(roomWidth, roomDepth); roomWidth = squareSize; roomDepth = squareSize;
      console.log(`Room dimensions adjusted to square: ${roomWidth}x${roomDepth}`);

      const groundMaterial = new THREE.MeshStandardMaterial({ color: 0xEAEAEA, roughness: 0.8, metalness: 0.1, side: THREE.DoubleSide });
      const ceilingMaterial = new THREE.MeshStandardMaterial({ color: 0xD8D8D8, roughness: 0.9, metalness: 0.05 });
      const wallMaterial = new THREE.MeshStandardMaterial({ color: 0xC0C0C0, roughness: 0.9, metalness: 0.05 });
      const windowFrameMaterial = new THREE.MeshStandardMaterial({ color: 0x505050, roughness: 0.8, metalness: 0.1 });

      const groundGeometry = new THREE.PlaneGeometry(roomWidth, roomDepth);
      groundPlane = new THREE.Mesh(groundGeometry, groundMaterial);
      groundPlane.rotation.x = -Math.PI / 2; groundPlane.receiveShadow = true; groundPlane.position.y = -0.005; groundPlane.renderOrder = -1; scene.add(groundPlane);
      const gridSize = Math.max(roomWidth, roomDepth); const gridDivisions = gridSize / 1; const gridColor = 0x666666;
      gridHelper = new THREE.GridHelper(gridSize, gridDivisions, gridColor, gridColor); gridHelper.position.y = 0; gridHelper.renderOrder = 0; scene.add(gridHelper);
      const ceilingGeometry = new THREE.BoxGeometry(roomWidth, ceilingThickness, roomDepth);
      ceilingPlane = new THREE.Mesh(ceilingGeometry, ceilingMaterial); ceilingPlane.position.y = roomHeight + ceilingThickness / 2; ceilingPlane.receiveShadow = true; ceilingPlane.castShadow = true; scene.add(ceilingPlane);

      const backWallZ = -roomDepth / 2 - wallThickness / 2;
      const leftWallX = -roomWidth / 2 - wallThickness / 2;
      const rightWallX = roomWidth / 2 + wallThickness / 2;
      const backWallGeo = new THREE.BoxGeometry(roomWidth, roomHeight, wallThickness);
      const backWall = new THREE.Mesh(backWallGeo, wallMaterial); backWall.position.set(0, roomHeight / 2, backWallZ); backWall.receiveShadow = true; backWall.castShadow = true; addCollidable(backWall);
      const sideWallGeo = new THREE.BoxGeometry(roomDepth + wallThickness * 2, roomHeight, wallThickness);
      const leftWall = new THREE.Mesh(sideWallGeo, wallMaterial); leftWall.position.set(leftWallX, roomHeight / 2, 0); leftWall.rotation.y = Math.PI / 2; leftWall.receiveShadow = true; leftWall.castShadow = true; addCollidable(leftWall);
      const rightWall = new THREE.Mesh(sideWallGeo, wallMaterial); rightWall.position.set(rightWallX, roomHeight / 2, 0); rightWall.rotation.y = Math.PI / 2; rightWall.receiveShadow = true; rightWall.castShadow = true; addCollidable(rightWall);

      const frameThickness = 1.0; const windowWallZ = roomDepth / 2 + wallThickness / 2;
      const bottomFrameGeo = new THREE.BoxGeometry(roomWidth, frameThickness, wallThickness);
      const bottomFrame = new THREE.Mesh(bottomFrameGeo, windowFrameMaterial); bottomFrame.position.set(0, frameThickness / 2, windowWallZ); bottomFrame.castShadow = true; bottomFrame.receiveShadow = true; addCollidable(bottomFrame);
      const topFrameGeo = new THREE.BoxGeometry(roomWidth, frameThickness, wallThickness);
      const topFrame = new THREE.Mesh(topFrameGeo, windowFrameMaterial); topFrame.position.set(0, roomHeight - frameThickness / 2, windowWallZ); topFrame.castShadow = true; topFrame.receiveShadow = true; addCollidable(topFrame);
      const pillarHeight = roomHeight - frameThickness * 2; const sideFrameGeo = new THREE.BoxGeometry(frameThickness, pillarHeight, wallThickness);
      const leftPillar = new THREE.Mesh(sideFrameGeo, windowFrameMaterial); leftPillar.position.set(-roomWidth / 2 + frameThickness / 2, frameThickness + pillarHeight / 2, windowWallZ); leftPillar.castShadow = true; leftPillar.receiveShadow = true; addCollidable(leftPillar);
      const rightPillar = new THREE.Mesh(sideFrameGeo, windowFrameMaterial); rightPillar.position.set(roomWidth / 2 - frameThickness / 2, frameThickness + pillarHeight / 2, windowWallZ); rightPillar.castShadow = true; rightPillar.receiveShadow = true; addCollidable(rightPillar);
      const columnHeight = pillarHeight; const columnGeo = new THREE.BoxGeometry(frameThickness, columnHeight, wallThickness * 0.8); const windowOpeningWidth = roomWidth - 2 * frameThickness; const columnSpacing = windowOpeningWidth / 3;
      const column1 = new THREE.Mesh(columnGeo, windowFrameMaterial); column1.position.set(-roomWidth / 2 + frameThickness + columnSpacing, frameThickness + columnHeight / 2, windowWallZ); column1.castShadow = true; column1.receiveShadow = true; addCollidable(column1);
      const column2 = new THREE.Mesh(columnGeo, windowFrameMaterial); column2.position.set(-roomWidth / 2 + frameThickness + 2 * columnSpacing, frameThickness + columnHeight / 2, windowWallZ); column2.castShadow = true; column2.receiveShadow = true; addCollidable(column2);

      const landscapeHeight = 200; const landscapeWidth = landscapeHeight * (16/9); const landscapeDist = 50;
      const landscapeGeo = new THREE.PlaneGeometry(landscapeWidth, landscapeHeight);
      const landscapeMat = new THREE.MeshBasicMaterial({ map: landscapeTexture, side: THREE.DoubleSide, fog: false });
      landscapePlane = new THREE.Mesh(landscapeGeo, landscapeMat); landscapePlane.position.set(0, roomHeight * 0.6, windowWallZ + landscapeDist); landscapePlane.rotation.y = Math.PI; scene.add(landscapePlane);

      // --- Create 9 Timeline Plaques --- (MODIFIED: Uses display order map for positioning)
      const plaqueWidth = 11.0; const plaqueHeight = 5.5; const plaqueDepth = 0.1;
      const plaqueYPos = 3.0; const wallOffset = 0.2;
      const totalPlaqueWidth = plaqueWidth * 3;
      const backWallSpace = roomWidth - wallThickness * 2 - 2;
      const sideWallSpace = roomDepth - 2;
      const backHorizontalSpacing = (backWallSpace - totalPlaqueWidth) / 2 + plaqueWidth;
      const sideHorizontalSpacing = (sideWallSpace - totalPlaqueWidth) / 2 + plaqueWidth;
      const textMaterialOptions = { resolution: 1024, fontSize: 30, padding: 50, fontColor: 'rgba(30, 30, 30, 1)', backgroundColor: 'rgba(255, 255, 255, 0.95)', textAlign: 'left', fontFace: 'Verdana, sans-serif', lineHeight: 30 * 1.3, imagePadding: 35 };
      const plaqueGeometry = new THREE.BoxGeometry(plaqueWidth, plaqueHeight, plaqueDepth);

      // Iterate 9 times, representing the 9 fixed plaque positions (0 to 8)
      for (let displayIndex = 0; displayIndex < 9; displayIndex++) {
          const targetYear = plaqueDisplayOrderMap[displayIndex];
          if (!targetYear) {
              console.warn(`No year defined for display index ${displayIndex} in plaqueDisplayOrderMap`);
              continue; // Skip if the map is incomplete for this position
          }

          // Find the event data corresponding to the target year in the timelineEvents computed property
          const eventData = timelineEvents.value.find(event => event.year === targetYear);

          if (!eventData) {
              console.warn(`Could not find event data for year ${targetYear} needed for display index ${displayIndex}`);
              continue; // Skip if data for the required year is missing
          }

          // Create the plaque texture and mesh using the found eventData
          const plaqueMaterial = new THREE.MeshStandardMaterial({
              map: createTextTextureWithImage(
                  `${eventData.year}\n\n${eventData.event}`,
                  placeholderPlaqueTexture,
                  textMaterialOptions,
                  eventData // Pass the entire event data to access imageUrl
              ),
              roughness: 0.8, metalness: 0.1, transparent: true, opacity: 1.0, side: THREE.FrontSide
          });
          const plaqueMesh = new THREE.Mesh(plaqueGeometry, plaqueMaterial);
          plaqueMesh.receiveShadow = true; plaqueMesh.castShadow = true;
          // Store the actual event data on the mesh if needed elsewhere (e.g., for interaction)
          plaqueMesh.userData = { eventData: eventData };

          // Position and rotate based on the *displayIndex* (0-8), which represents the fixed visual slot
          if (displayIndex < 3) { // Back Wall (Positions 0, 1, 2)
              plaqueMesh.position.set( (displayIndex - 1) * backHorizontalSpacing, plaqueYPos, backWallZ + wallThickness / 2 + wallOffset );
              plaqueMesh.rotation.y = 0;
          } else if (displayIndex < 6) { // Left Wall (Positions 3, 4, 5)
              const zPosIndex = displayIndex - 3; // Convert displayIndex (3,4,5) to wall index (0,1,2)
              plaqueMesh.position.set( leftWallX + wallThickness / 2 + wallOffset, plaqueYPos, (zPosIndex - 1) * sideHorizontalSpacing );
              plaqueMesh.rotation.y = Math.PI / 2;
          } else { // Right Wall (Positions 6, 7, 8)
              const zPosIndex = displayIndex - 6; // Convert displayIndex (6,7,8) to wall index (0,1,2)
              plaqueMesh.position.set( rightWallX - wallThickness / 2 - wallOffset, plaqueYPos, (zPosIndex - 1) * sideHorizontalSpacing );
              plaqueMesh.rotation.y = -Math.PI / 2;
          }

          scene.add(plaqueMesh);
          textMeshes.push(plaqueMesh); // Add to array for potential later use (like redraw or disposal)
      }

      console.log("Square showroom env created with plaques in specific display order. Collidables:", collidableObjects.length, "Cached BBoxes:", collidableBoxes.length);
  }

  // --- Helper to Redraw Textures (if image loads late) --- (MODIFIED: Needs to find correct event data)
  function redrawPlaqueTextures() {
      if (!placeholderPlaqueTexture || !placeholderPlaqueTexture.image) {
          console.warn("Cannot redraw plaque textures, placeholder image not ready.");
          return;
      }
      const textMaterialOptions = { resolution: 1024, fontSize: 30, padding: 50, fontColor: 'rgba(30, 30, 30, 1)', backgroundColor: 'rgba(255, 255, 255, 0.95)', textAlign: 'left', fontFace: 'Verdana, sans-serif', lineHeight: 30 * 1.3, imagePadding: 35 };

      textMeshes.forEach(plaqueMesh => {
          // Check if we stored the eventData on the mesh during creation
          if (plaqueMesh.material && plaqueMesh.userData && plaqueMesh.userData.eventData) {
              const eventData = plaqueMesh.userData.eventData;
              plaqueMesh.material.map?.dispose(); // Dispose old texture
              plaqueMesh.material.map = createTextTextureWithImage(
                  `${eventData.year}\n\n${eventData.event}`,
                  placeholderPlaqueTexture, // Use the (now hopefully loaded) texture
                  textMaterialOptions,
                  eventData // Pass the event data to access imageUrl
              );
              plaqueMesh.material.needsUpdate = true;
          } else {
              console.warn("Cannot redraw texture for a plaque mesh, missing material or eventData in userData.", plaqueMesh);
          }
      });
      console.log("Attempted redraw of plaque textures completed.");
  }


  // --- Create Tire Track Assets --- (Unchanged)
  function createTrackAssets() { trackGeometry = new THREE.BoxGeometry(TRACK_WIDTH, TRACK_LENGTH, 0.001); tireTrackMaterial = new THREE.MeshBasicMaterial({ color: 0x333333, transparent: true, opacity: 0.7, depthWrite: false, depthTest: true, polygonOffset: true, polygonOffsetFactor: -2.0, polygonOffsetUnits: -8.0 }); tireTrackInstancedMesh = new THREE.InstancedMesh(trackGeometry, tireTrackMaterial, MAX_TRACKS); tireTrackInstancedMesh.instanceMatrix.setUsage(THREE.DynamicDrawUsage); tireTrackInstancedMesh.castShadow = false; tireTrackInstancedMesh.receiveShadow = false; tireTrackInstancedMesh.frustumCulled = false; tireTrackInstancedMesh.renderOrder = 1; trackInstanceData.length = 0; for (let i = 0; i < MAX_TRACKS; i++) { trackInstanceData.push({ life: 0, active: false }); tireTrackInstancedMesh.setMatrixAt(i, _hideMatrix); } tireTrackInstancedMesh.instanceMatrix.needsUpdate = true; scene.add(tireTrackInstancedMesh); console.log(`Tire track assets created (Transparent, FrustumCulled=false, RenderOrder=1) (Max: ${MAX_TRACKS}).`); }

  // --- Load Car Model --- (Unchanged)
  function loadAssets() { const gltfLoader = new GLTFLoader(loadingManager); const carModelPath = '/car.glb'; gltfLoader.load(carModelPath, (gltf) => { carModel = gltf.scene; carModel.scale.set(0.5, 0.5, 0.5); carModel.position.set(0, 0.1, 15); carModel.traverse((node) => { if (node.isMesh) { node.castShadow = true; node.receiveShadow = true; } }); scene.add(carModel); carModel.updateMatrixWorld(true); try { carBox.setFromObject(carModel, true); if (!carBox.isEmpty()) { carBox.getSize(carSize); carSizeInitialized = true; console.log("Car BBox size initialized:", carSize.x.toFixed(2), carSize.y.toFixed(2), carSize.z.toFixed(2)); currentLookAtTarget.copy(carModel.position).add(_vector3_1.set(0, 0.5, 0)); camera.lookAt(currentLookAtTarget); startAnimationLoop(); } else { console.error("Car BBox calculation resulted in an empty box!"); startAnimationLoop(); } } catch (e) { console.error("Error calculating car bounding box:", e); startAnimationLoop(); } }, undefined, (error) => { console.error('Error loading car model:', error); addFallbackCube(); }); }

  // --- Fallback Cube --- (Unchanged)
  function addFallbackCube() { const geometry = new THREE.BoxGeometry(1, 0.5, 2); const material = new THREE.MeshStandardMaterial({ color: 0xff0000 }); carModel = new THREE.Mesh(geometry, material); carModel.position.set(0, 0.25, 15); carModel.castShadow = true; carModel.receiveShadow = true; scene.add(carModel); carModel.updateMatrixWorld(true); try { carBox.setFromObject(carModel); if (!carBox.isEmpty()) { carBox.getSize(carSize); carSizeInitialized = true; console.log("Fallback Cube BBox size initialized:", carSize.x.toFixed(2), carSize.y.toFixed(2), carSize.z.toFixed(2)); currentLookAtTarget.copy(carModel.position).add(_vector3_1.set(0, 0.25, 0)); camera.lookAt(currentLookAtTarget); startAnimationLoop(); } else { console.error("Fallback Cube BBox calculation resulted in empty box!"); startAnimationLoop(); } } catch(e) { console.error("Error calculating fallback cube bounding box:", e); startAnimationLoop(); } }

  // --- Event Listeners --- (Unchanged)
  function setupEventListeners() { window.addEventListener('resize', handleResize); window.addEventListener('keydown', handleKeyDown); window.addEventListener('keyup', handleKeyUp); }
  function handleResize() { if (!camera || !renderer || !canvasContainer.value) return; const width = canvasContainer.value.clientWidth; const height = canvasContainer.value.clientHeight; camera.aspect = width / height; camera.updateProjectionMatrix(); renderer.setSize(width, height); }
  function handleKeyDown(event) { if (keyState.hasOwnProperty(event.key)) { event.preventDefault(); keyState[event.key] = true; } }
  function handleKeyUp(event) { if (keyState.hasOwnProperty(event.key)) { keyState[event.key] = false; } }
  function handleTouchStart(keyName) { if (keyState.hasOwnProperty(keyName)) { keyState[keyName] = true; } }
  function handleTouchEnd(keyName) { if (keyState.hasOwnProperty(keyName)) { keyState[keyName] = false; } }

  // --- Animation Loop --- (Unchanged)
  function startAnimationLoop() { if (animationFrameId) return; console.log("Starting animation loop..."); clock.start(); animate(); }
  function animate() { animationFrameId = requestAnimationFrame(animate); const deltaTime = Math.min(clock.getDelta(), 0.1); try { if (carModel && carSizeInitialized) { updateCar(deltaTime); updateTireTracks(deltaTime); } updateCamera(); if (renderer && scene && camera) { renderer.render(scene, camera); } } catch (error) { console.error("Error in animation loop:", error); cancelAnimationFrame(animationFrameId); animationFrameId = null; } }

  // --- Car Movement Logic --- (Unchanged)
  function updateCar(deltaTime) { let appliedAcceleration = 0; if (keyState.ArrowUp) appliedAcceleration = carState.acceleration; else if (keyState.ArrowDown) appliedAcceleration = (carState.speed > 0.005) ? -carState.braking : -carState.acceleration * 0.6; carState.speed += appliedAcceleration; if (appliedAcceleration === 0) carState.speed *= Math.pow(carState.friction, deltaTime * 60); carState.speed = Math.max(carState.maxReverseSpeed, Math.min(carState.maxSpeed, carState.speed)); if (Math.abs(carState.speed) < 0.001 && appliedAcceleration === 0) carState.speed = 0; let didTurn = false; if (Math.abs(carState.speed) > 0.005) { let turnDirection = (keyState.ArrowLeft ? 1 : 0) - (keyState.ArrowRight ? 1 : 0); if (turnDirection !== 0) { const turnAmount = carState.turnSpeed * turnDirection * Math.sign(carState.speed) * (deltaTime * 60); carModel.rotation.y += turnAmount; didTurn = true; } } const moveStep = carState.speed * (deltaTime * 60); if (Math.abs(moveStep) < 0.00001) { if (didTurn) carModel.updateMatrixWorld(); return; } const moveX = -Math.sin(carModel.rotation.y) * moveStep; const moveZ = -Math.cos(carModel.rotation.y) * moveStep; const potentialMove = _vector3_1.set(moveX, 0, moveZ); const currentPosition = carModel.position; const carCenterOffsetY = carSize.y / 2; const carCenterOffset = _vector3_2.set(0, carCenterOffsetY, 0); let collisionX = false; let collisionZ = false; const potentialCenterPos = _vector3_3.copy(currentPosition).add(potentialMove).add(carCenterOffset); const potentialCarBox = _box3_1.setFromCenterAndSize(potentialCenterPos, carSize); for (const wallBox of collidableBoxes) { if (potentialCarBox.intersectsBox(wallBox)) { const potentialCenterX = _vector3_3.copy(currentPosition).add(_vector3_1.set(moveX, 0, 0)).add(carCenterOffset); _box3_2.setFromCenterAndSize(potentialCenterX, carSize); if (_box3_2.intersectsBox(wallBox)) { collisionX = true; } const potentialCenterZ = _vector3_3.copy(currentPosition).add(_vector3_1.set(0, 0, moveZ)).add(carCenterOffset); _box3_2.setFromCenterAndSize(potentialCenterZ, carSize); if (_box3_2.intersectsBox(wallBox)) { collisionZ = true; } if (collisionX || collisionZ) break; } } const finalMove = potentialMove; let collisionOccurred = false; if (collisionX) { finalMove.x = 0; collisionOccurred = true; } if (collisionZ) { finalMove.z = 0; collisionOccurred = true; } carModel.position.add(finalMove); carModel.updateMatrixWorld(); if (collisionOccurred) { if (Math.sign(carState.speed) === Math.sign(appliedAcceleration) || appliedAcceleration === 0) { carState.speed *= 0.5; } } trackSpawnTimer += deltaTime; if (Math.abs(carState.speed) > TRACK_SPAWN_THRESHOLD && tireTrackInstancedMesh && trackSpawnTimer >= TRACK_SPAWN_INTERVAL) { spawnTireTrackPair(); trackSpawnTimer = 0; } }

  // --- Tire Track Spawning --- (Unchanged)
  function spawnTireTrackPair() { if (!carModel || !tireTrackInstancedMesh) return; carModel.getWorldDirection(vecForward); vecRight.crossVectors(carModel.up, vecForward).normalize(); wheelPosLeft.copy(carModel.position) .addScaledVector(vecRight, -WHEEL_OFFSET_X) .addScaledVector(vecForward, -WHEEL_OFFSET_Z); wheelPosRight.copy(carModel.position) .addScaledVector(vecRight, WHEEL_OFFSET_X) .addScaledVector(vecForward, -WHEEL_OFFSET_Z); spawnSingleTrackInstance(wheelPosLeft, carModel.rotation.y); spawnSingleTrackInstance(wheelPosRight, carModel.rotation.y); tireTrackInstancedMesh.instanceMatrix.needsUpdate = true; }
  function spawnSingleTrackInstance(position, carRotationY) { if (!tireTrackInstancedMesh) return; const index = nextTrackInstanceIndex; trackInstanceData[index].life = TRACK_LIFETIME; trackInstanceData[index].active = true; _position.copy(position); _position.y = TRACK_OFFSET_Y; _euler.set(-Math.PI / 2, carRotationY, 0, 'YXZ'); _quaternion.setFromEuler(_euler); _scale.set(1, 1, 1); _matrix.compose(_position, _quaternion, _scale); tireTrackInstancedMesh.setMatrixAt(index, _matrix); nextTrackInstanceIndex = (nextTrackInstanceIndex + 1) % MAX_TRACKS; }

  // --- Update Tire Tracks --- (Unchanged)
  function updateTireTracks(deltaTime) { if (!tireTrackInstancedMesh || isNaN(deltaTime) || deltaTime <= 0) return; let needsMatrixUpdate = false; for (let i = 0; i < MAX_TRACKS; i++) { const data = trackInstanceData[i]; if (data.active) { data.life -= deltaTime; if (data.life <= 0) { data.active = false; data.life = 0; tireTrackInstancedMesh.setMatrixAt(i, _hideMatrix); needsMatrixUpdate = true; } } } if (needsMatrixUpdate) { tireTrackInstancedMesh.instanceMatrix.needsUpdate = true; } }

  // --- Camera Follow Logic --- (Unchanged)
  function updateCamera() { if (!carModel || !camera) return; const relativeCameraOffset = _vector3_1.set(0, 1.5, 3.5); _cameraOffset.copy(relativeCameraOffset).applyMatrix4(carModel.matrixWorld); _cameraTarget.copy(carModel.position).add(_vector3_2.set(0, 0.5, 0)); const positionLerpFactor = 0.08; const lookAtLerpFactor = 0.1; camera.position.lerp(_cameraOffset, positionLerpFactor); currentLookAtTarget.lerp(_cameraTarget, lookAtLerpFactor); camera.lookAt(currentLookAtTarget); }

  // --- Watcher for Loading State --- (Unchanged)
  watch(isLoading, (newValue, oldValue) => { if (newValue === false && oldValue === true) { console.log("Loading finished, showing arrow prompt."); showArrowPrompt.value = true; isFadingArrowPrompt.value = false; clearTimeout(promptFadeTimeoutId); clearTimeout(promptRemoveTimeoutId); promptFadeTimeoutId = setTimeout(() => { isFadingArrowPrompt.value = true; promptRemoveTimeoutId = setTimeout(() => { showArrowPrompt.value = false; }, 500); }, 2500); } });

  // --- Initialization --- (Unchanged)
  onMounted(() => { if (canvasContainer.value) { initThreeScene(canvasContainer.value); createEnvironment(); createTrackAssets(); loadAssets(); setupEventListeners(); } else { console.error("Canvas container ref not found on mount!"); } });

  // --- Cleanup --- (Unchanged)
  onUnmounted(() => {
      console.log("Showroom unmounting...");
      clearTimeout(promptFadeTimeoutId); clearTimeout(promptRemoveTimeoutId); if (animationFrameId) { cancelAnimationFrame(animationFrameId); animationFrameId = null; }
      window.removeEventListener('resize', handleResize); window.removeEventListener('keydown', handleKeyDown); window.removeEventListener('keyup', handleKeyUp);
      console.log("Disposing Three.js resources...");
      trackInstanceData.length = 0; if (tireTrackInstancedMesh) { scene?.remove(tireTrackInstancedMesh); tireTrackInstancedMesh.geometry?.dispose(); tireTrackInstancedMesh.material?.dispose(); console.log("Tire track InstancedMesh disposed."); } tireTrackInstancedMesh = null; tireTrackMaterial = null; trackGeometry = null;
      collidableObjects.length = 0; collidableBoxes.length = 0;
      // Dispose timeline plaques using the tracked textMeshes array
      textMeshes.forEach(textMesh => { if(textMesh) { scene?.remove(textMesh); textMesh.geometry?.dispose(); if (textMesh.material) { textMesh.material.map?.dispose(); textMesh.material.dispose(); } } }); textMeshes.length = 0; console.log("Timeline plaques disposed.");
      if (placeholderPlaqueTexture) { placeholderPlaqueTexture.dispose(); placeholderPlaqueTexture = null; console.log("Placeholder plaque texture disposed."); }
      if(landscapePlane) { scene?.remove(landscapePlane); landscapePlane.geometry?.dispose(); landscapePlane.material?.map?.dispose(); landscapePlane.material?.dispose(); console.log("Landscape plane disposed."); } landscapeTexture?.dispose(); landscapeTexture = null; landscapePlane = null;
      if(ceilingPlane) { scene?.remove(ceilingPlane); ceilingPlane.geometry?.dispose(); ceilingPlane.material?.dispose(); console.log("Ceiling plane disposed."); } ceilingPlane = null;
      if(gridHelper) { scene?.remove(gridHelper); gridHelper.geometry?.dispose(); gridHelper.material?.dispose(); console.log("GridHelper disposed."); } gridHelper = null;
      if(groundPlane) { scene?.remove(groundPlane); groundPlane.geometry?.dispose(); groundPlane.material?.dispose(); console.log("Solid floor plane disposed."); } groundPlane = null;
      if (scene) { scene.traverse((object) => { if (object.isMesh) { object.geometry?.dispose(); if (object.material) { if (Array.isArray(object.material)) { object.material.forEach(material => { material.map?.dispose(); material.dispose(); }); } else { object.material.map?.dispose(); object.material.dispose(); } } } else if (object.isLight && object.shadow && object.shadow.map) { object.shadow.map.dispose(); } }); console.log("Remaining scene objects disposed via traversal."); }
      if (renderer) { renderer.dispose(); renderer.forceContextLoss(); if (renderer.domElement?.parentNode) { renderer.domElement.parentNode.removeChild(renderer.domElement); } renderer = null; console.log("Renderer disposed."); }
      scene = null; camera = null; clock = null; carModel = null; textureLoader = null; loadingManager = null; carSizeInitialized = false;
      console.log("Three.js scene cleanup complete.");
   });

  // --- Return values --- (Unchanged)
  return {
    canvasContainer, isLoading, loadingProgress, showArrowPrompt,
    isFadingArrowPrompt, handleTouchStart, handleTouchEnd
  };
}
};
</script>

<style scoped>
/* Styles remain the same */
.about-page-3d { position: relative; width: 100%; height: calc(100vh - 60px); min-height: 500px; overflow: hidden; touch-action: none; background-color: #454545; }
.canvas-container { width: 100%; height: 100%; display: block; }
.loading-overlay { position: absolute; inset: 0; background-color: rgba(0, 0, 0, 0.8); color: white; display: flex; justify-content: center; align-items: center; font-size: 1.8em; z-index: 20; font-weight: bold; }
.arrow-key-prompt { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: rgba(0, 0, 0, 0.75); color: white; padding: 20px 25px; border-radius: 10px; display: flex; flex-direction: column; align-items: center; z-index: 15; opacity: 1; transition: opacity 0.5s ease-out; pointer-events: none; }
.arrow-key-prompt.fade-out { opacity: 0; }
.arrow-row { display: flex; justify-content: center; align-items: center; margin-top: 5px; }
.arrow-key { background-color: rgba(255, 255, 255, 0.2); border: 1px solid rgba(255, 255, 255, 0.5); padding: 5px 10px; margin: 3px; border-radius: 4px; font-size: 1.4em; min-width: 35px; text-align: center; line-height: 1; }
.arrow-key.up { margin-bottom: 0; }
.prompt-text { margin-top: 15px; font-size: 1em; font-weight: bold; }
.mobile-controls { position: absolute; bottom: 0; left: 0; width: 100%; height: 150px; z-index: 10; pointer-events: none; display: block; }
.mobile-button { position: absolute; bottom: 20px; width: 60px; height: 60px; background-color: rgba(0, 0, 0, 0.4); color: white; border: 2px solid rgba(255, 255, 255, 0.5); border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 2em; font-weight: bold; pointer-events: auto; user-select: none; -webkit-user-select: none; touch-action: manipulation; transition: background-color 0.1s ease; }
.mobile-button:active { background-color: rgba(0, 0, 0, 0.7); }
.mobile-button.left { left: 20px; } .mobile-button.right { left: 100px; } .mobile-button.down { right: 100px; } .mobile-button.up { right: 20px; }
@media (min-width: 768px) { .mobile-controls { display: none; } }
</style>