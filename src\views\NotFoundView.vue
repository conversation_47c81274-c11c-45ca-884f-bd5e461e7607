<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/');
};
</script>

<template>
  <section class="min-h-screen flex items-center justify-center bg-light">
    <div class="container-custom">
      <div class="text-center max-w-2xl mx-auto">
        <div class="mb-8">
          <span class="text-9xl font-heading font-bold text-secondary">404</span>
        </div>
        <h1 class="text-4xl md:text-5xl font-heading font-bold mb-4">Page Not Found</h1>
        <p class="text-lg text-gray-600 mb-8">
          The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button @click="goHome" class="btn btn-primary">
            Return to Home
          </button>
          <router-link to="/inventory" class="btn btn-outline">
            Browse Inventory
          </router-link>
        </div>
      </div>
    </div>
  </section>
</template>