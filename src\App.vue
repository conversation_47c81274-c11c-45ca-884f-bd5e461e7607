<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import TheHeader from './components/layout/TheHeader.vue';
import TheFooter from './components/layout/TheFooter.vue';
import ChatBot from './components/ui/ChatBot.vue';
import vehicleStore from './store/vehicles';
import businessStore from './store/business';
import stylingStore from './store/styling';

const isLoading = ref(true);

// Get styling from store
const styling = computed(() => stylingStore.styling);

// Helper function to convert hex to RGB
const hexToRgb = (hex) => {
  // Remove the hash if it exists
  hex = hex.replace(/^#/, '');

  // Parse the hex values
  let r, g, b;
  if (hex.length === 3) {
    // For shorthand hex (#RGB)
    r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
    g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
    b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
  } else {
    // For full hex (#RRGGBB)
    r = parseInt(hex.substring(0, 2), 16);
    g = parseInt(hex.substring(2, 4), 16);
    b = parseInt(hex.substring(4, 6), 16);
  }

  return `${r}, ${g}, ${b}`;
};

// Apply CSS variables for dynamic styling
const applyDynamicStyling = () => {
  const root = document.documentElement;

  // Apply colors
  root.style.setProperty('--color-primary', styling.value.colors.primary);
  root.style.setProperty('--color-secondary', styling.value.colors.secondary);
  root.style.setProperty('--color-accent', styling.value.colors.accent);
  root.style.setProperty('--color-light', styling.value.colors.light);
  root.style.setProperty('--color-dark', styling.value.colors.dark);

  // Apply RGB versions of colors for opacity support
  root.style.setProperty('--color-primary-rgb', hexToRgb(styling.value.colors.primary));
  root.style.setProperty('--color-secondary-rgb', hexToRgb(styling.value.colors.secondary));
  root.style.setProperty('--color-accent-rgb', hexToRgb(styling.value.colors.accent));
  root.style.setProperty('--color-light-rgb', hexToRgb(styling.value.colors.light));
  root.style.setProperty('--color-dark-rgb', hexToRgb(styling.value.colors.dark));

  // Apply fonts
  root.style.setProperty('--font-heading', styling.value.typography.headingFont);
  root.style.setProperty('--font-body', styling.value.typography.bodyFont);

  // Apply layout
  root.style.setProperty('--card-border-radius', styling.value.layout.cardBorderRadius);
  root.style.setProperty('--button-border-radius', styling.value.layout.buttonBorderRadius);

  // Apply card shadow
  let shadowValue = '0 1px 2px rgba(0, 0, 0, 0.05)'; // sm
  if (styling.value.layout.cardShadow === 'md') {
    shadowValue = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
  } else if (styling.value.layout.cardShadow === 'lg') {
    shadowValue = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
  } else if (styling.value.layout.cardShadow === 'none') {
    shadowValue = 'none';
  }
  root.style.setProperty('--card-shadow', shadowValue);
};

// Watch for styling changes
watch(() => styling.value, () => {
  applyDynamicStyling();
}, { deep: true });

onMounted(async () => {
  // Reset styling store state to prevent perpetual saving state
  stylingStore.isSaving.value = false;
  stylingStore.saveSuccess.value = false;
  stylingStore.saveError.value = false;

  // Simulate loading time
  setTimeout(() => {
    isLoading.value = false;
  }, 1000);

  // Initialize stores
  await Promise.all([
    vehicleStore.initStore(),
    businessStore.initStore(),
    stylingStore.initStore()
  ]);

  // Apply initial styling
  applyDynamicStyling();
});
</script>

<template>
  <!-- Loading screen -->
  <div v-if="isLoading" class="fixed inset-0 bg-primary flex items-center justify-center z-50">
    <div class="text-center flex flex-col items-center justify-center">
      <img src="/GTWHEEL.png" alt="GT Wheel" class="animate-spin h-16 w-16 mb-4 mx-auto" />
      <h2 class="text-xl font-heading font-bold text-white">GT Motor Sports</h2>
    </div>
  </div>

  <!-- Main application -->
  <div class="flex flex-col min-h-screen" v-else>
    <TheHeader />

    <main class="flex-grow">
      <!-- Simplified router-view without transitions for debugging -->
      <router-view></router-view>
    </main>

    <TheFooter />

    <!-- Chat Bot Component -->
    <ChatBot />
  </div>
</template>

<style>
.page-enter-active,
.page-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>