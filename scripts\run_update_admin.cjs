// Modified script to load environment variables from .env file
// Import dotenv to load environment variables
require('dotenv').config();

// Import the Supabase client library
const { createClient } = require('@supabase/supabase-js');

// Actual User UID and Tenant ID
const userIdToUpdate = '64dbf91c-d906-4156-b8d4-bea2e3e69739';
const tenantIdToSet = 'b316264d-e086-42c8-b50f-1ce5134514ee';

// Environment variables for Supabase connection
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// --- Validation ---
if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.error('Error: Missing required environment variables SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
  console.error('Current values:');
  console.error(`SUPABASE_URL: ${supabaseUrl ? 'Set' : 'Not set'}`);
  console.error(`SUPABASE_SERVICE_ROLE_KEY: ${supabaseServiceRoleKey ? 'Set' : 'Not set'}`);
  process.exit(1); // Exit if env vars are missing
}

// --- Main Async Function ---
async function updateUserMetadata() {
  console.log(`Updating app_metadata for user: ${userIdToUpdate}`);
  console.log(`Setting tenant_id to: ${tenantIdToSet}`);

  try {
    // Initialize Supabase Admin Client
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Update the user's app_metadata
    const { data, error } = await supabase.auth.admin.updateUserById(
      userIdToUpdate,
      {
        app_metadata: { tenant_id: tenantIdToSet }
      }
    );

    if (error) {
      console.error('Error updating user metadata:', error.message);
      process.exit(1); // Exit on error
    }

    console.log('Success! User metadata updated:');
    console.log(data); // Log the returned user data object

  } catch (error) {
    // Catch any unexpected errors during execution
    console.error('Unexpected error:', error.message || error);
    process.exit(1); // Exit on unexpected error
  }
}

// --- Execute the function ---
updateUserMetadata();