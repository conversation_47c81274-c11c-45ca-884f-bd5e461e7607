-- Create a PostgreSQL function to extract tenant_id from JWT claims
-- This function will be used within RLS policies to safely get the current user's tenant_id

CREATE OR REPLACE FUNCTION auth.current_tenant_id()
RETURNS uuid
LANGUAGE sql
STABLE
AS $$
  SELECT 
    CAST(
      nullif(
        current_setting('request.jwt.claims', true)::jsonb -> 'app_metadata' ->> 'tenant_id',
        ''
      ) AS uuid
    );
$$;

-- This function:
-- 1. Gets the JWT claims using current_setting('request.jwt.claims', true)
-- 2. Extracts the tenant_id from app_metadata
-- 3. Uses nullif to handle empty strings
-- 4. Casts the result to UUID
-- 5. Returns NULL if tenant_id is missing or empty