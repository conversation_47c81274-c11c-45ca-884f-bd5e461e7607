@echo off
echo Running Supabase Storage Fix Script...
echo.
echo This script will help fix the site-media bucket and RLS policies in your Supabase project.
echo.
echo Prerequisites:
echo - You need to have the Supabase CLI installed
echo - You need to be logged in to Supabase CLI
echo - You need to have access to your Supabase project
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause > nul

echo.
echo Executing SQL script to fix site-media bucket and RLS policies...
echo.

REM Run the SQL script using Supabase CLI
supabase db execute --file supabase_storage_fix.sql

echo.
echo SQL script execution completed.
echo.
echo If you see any errors above, please check:
echo 1. That you have the Supabase CLI installed and are logged in
echo 2. That you have access to your Supabase project
echo 3. That the supabase_storage_fix.sql file exists in the current directory
echo.
echo If the script ran successfully, the site-media bucket should now be properly configured.
echo You can now try using the Site Styling tab in the admin dashboard again.
echo.
echo Press any key to exit...
pause > nul
