<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import authStore from '../../store/supabaseAuth';
import stylingStore from '../../store/styling';
import BusinessInfoProvider from '../BusinessInfoProvider.vue';

const router = useRouter();
const route = useRoute();
const isScrolled = ref(false);
const isMobileMenuOpen = ref(false);

// Initialize stores
onMounted(() => {
  authStore.initAuth();
  stylingStore.initStore();
});

// Get styling from store
const styling = computed(() => stylingStore.styling);

// Check if user is authenticated
const isAuthenticated = computed(() => authStore.isAuthenticated.value);

const navLinks = [
  { name: 'Home', path: '/' },
  { name: 'Inventory', path: '/inventory' },
  { name: 'Financing', path: '/financing' },
  { name: 'Detailing', path: '/detailing' },
  { name: 'Contact', path: '/contact' },
  { name: 'About', path: '/about' }
];

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50;
};

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
};

// Programmatic navigation function
const navigateTo = (path, event) => {
  if (event) {
    event.preventDefault();
  }

  // Force a full page navigation instead of using Vue Router
  window.location.href = path;
};

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  handleScroll(); // Check initial scroll position
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});

// Close mobile menu when route changes
watch(() => route.path, () => {
  isMobileMenuOpen.value = false;
});
</script>

<template>
  <BusinessInfoProvider v-slot="{ title, phone, email, isLoading }">
    <header
      class="fixed top-0 left-0 right-0 z-40 transition-all duration-300"
      :class="[
        isScrolled
          ? 'bg-white/95 backdrop-blur-sm shadow-lg py-2'
          : 'bg-gradient-to-r from-white/90 to-white/95 backdrop-blur-sm py-3'
      ]"
      :style="{ '--header-height': isScrolled ? '60px' : '72px' }"
    >
    <div class="container-custom">
      <div class="flex items-center justify-between">
        <!-- Logo -->
        <div class="flex items-center">
          <a href="/" @click="(e) => navigateTo('/', e)" class="flex items-center group">
            <!-- Show uploaded logo if available -->
            <img
              v-if="styling.media.logo"
              :src="styling.media.logo"
              alt="GT Motor Sports"
              class="h-10 mr-2 transition-transform duration-300 group-hover:scale-105"
            />
            <!-- Otherwise show text logo -->
            <span v-else class="flex items-center text-2xl font-heading font-bold">
              <span class="bg-secondary text-white px-2 py-1 mr-2 shadow-sm transition-all duration-300 group-hover:shadow-md">GT</span>
              <span class="text-black transition-colors duration-300 group-hover:text-secondary">
                {{ title ? title.replace('GT', '') : 'Motor Sports' }}
              </span>
            </span>
          </a>
        </div>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <a
            v-for="link in navLinks"
            :key="link.name"
            :href="link.path"
            @click="(e) => navigateTo(link.path, e)"
            class="relative text-black hover:text-secondary transition-all duration-300 font-medium group py-2"
            :class="{ 'text-secondary': route.path === link.path }"
          >
            {{ link.name }}
            <span
              class="absolute bottom-0 left-0 w-0 h-0.5 bg-secondary transition-all duration-300 group-hover:w-full"
              :class="{ 'w-full': route.path === link.path }"
            ></span>
          </a>
          <!-- Admin Link (only visible when authenticated) -->
          <a
            v-if="isAuthenticated"
            href="/admin/dashboard"
            @click="(e) => navigateTo('/admin/dashboard', e)"
            class="relative text-gray-500 hover:text-secondary transition-all duration-300 font-medium text-sm flex items-center group py-2"
            :class="{ 'text-secondary': route.path.includes('/admin') }"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Admin
            <span
              class="absolute bottom-0 left-0 w-0 h-0.5 bg-secondary transition-all duration-300 group-hover:w-full"
              :class="{ 'w-full': route.path.includes('/admin') }"
            ></span>
          </a>

          <a :href="`tel:${phone}`" class="px-4 py-2 border border-secondary text-secondary hover:bg-secondary hover:text-white transition-all duration-300 flex items-center rounded-md shadow-sm hover:shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            {{ phone || '(*************' }}
          </a>
        </nav>

        <!-- Mobile Menu Button -->
        <button
          class="md:hidden text-black hover:text-secondary focus:outline-none p-2 rounded-md transition-all duration-300 hover:bg-gray-100"
          @click="toggleMobileMenu"
          aria-label="Toggle menu"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 transition-transform duration-300"
            :class="{'rotate-90': isMobileMenuOpen}"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              v-if="!isMobileMenuOpen"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
            <path
              v-else
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Menu - Enhanced for better mobile experience -->
    <div
      v-if="isMobileMenuOpen"
      class="md:hidden fixed top-[calc(var(--header-height,60px))] left-0 right-0 bottom-0 bg-white shadow-lg py-6 px-6 border-t border-gray-100 z-50 overflow-y-auto"
      style="height: calc(100vh - var(--header-height, 60px)); min-height: 100%;"
    >
      <!-- Show logo in mobile menu -->
      <div v-if="styling.media.logo" class="flex justify-center mb-6">
        <img :src="styling.media.logo" alt="GT Motor Sports" class="h-12 shadow-sm" />
      </div>
      <nav class="flex flex-col space-y-5">
        <a
          v-for="link in navLinks"
          :key="link.name"
          :href="link.path"
          @click="(e) => { navigateTo(link.path, e); isMobileMenuOpen = false; }"
          class="text-black hover:text-secondary transition-all duration-300 py-3 px-4 font-medium flex items-center justify-between rounded-lg hover:bg-gray-50"
          :class="{ 'text-secondary bg-gray-50': route.path === link.path }"
        >
          <span>{{ link.name }}</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>

        <!-- Admin Link (only visible when authenticated) -->
        <a
          v-if="isAuthenticated"
          href="/admin/dashboard"
          @click="(e) => { navigateTo('/admin/dashboard', e); isMobileMenuOpen = false; }"
          class="text-gray-500 hover:text-secondary transition-all duration-300 py-3 px-4 font-medium text-sm flex items-center justify-between rounded-lg hover:bg-gray-50"
          :class="{ 'text-secondary bg-gray-50': route.path.includes('/admin') }"
        >
          <span class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Admin
          </span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>

        <div class="pt-4 mt-4 border-t border-gray-100">
          <a :href="`tel:${phone}`" class="w-full px-4 py-3 border border-secondary text-secondary hover:bg-secondary hover:text-white transition-all duration-300 flex items-center justify-center rounded-md shadow-sm hover:shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            {{ phone || '(*************' }}
          </a>
        </div>
      </nav>
    </div>
  </header>
  </BusinessInfoProvider>
</template>