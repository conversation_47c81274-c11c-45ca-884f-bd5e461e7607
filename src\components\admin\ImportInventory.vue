<script setup>
import { ref, computed } from 'vue';
import importInventory, { createSampleJsonInventory } from '../../importInventory';

const isImporting = ref(false);
const importResult = ref(null);
const importError = ref(null);
const selectedFile = ref(null);
const fileContents = ref(null);
const previewData = ref(null);
const showPreview = ref(false);
const importedCount = ref(0);
const fileFormat = ref('markdown'); // 'markdown' or 'json'

// Validation states
const isValidFile = computed(() => {
  if (!selectedFile.value) return false;
  
  if (fileFormat.value === 'markdown') {
    return selectedFile.value.name.endsWith('.md');
  } else if (fileFormat.value === 'json') {
    return selectedFile.value.name.endsWith('.json');
  }
  
  return false;
});

const isValidContent = computed(() => {
  if (!fileContents.value) return false;
  
  if (fileFormat.value === 'markdown') {
    return fileContents.value.includes('# GT Motorsports Vehicle Inventory');
  } else if (fileFormat.value === 'json') {
    try {
      const parsed = JSON.parse(fileContents.value);
      return parsed && parsed.vehicles && Array.isArray(parsed.vehicles);
    } catch (e) {
      return false;
    }
  }
  
  return false;
});

// Handle file format change
const handleFormatChange = (format) => {
  fileFormat.value = format;
  selectedFile.value = null;
  fileContents.value = null;
  previewData.value = null;
  showPreview.value = false;
  importResult.value = null;
  importError.value = null;
};

// Handle file selection
const handleFileChange = async (event) => {
  const file = event.target.files[0];
  selectedFile.value = file;
  importResult.value = null;
  importError.value = null;
  fileContents.value = null;
  previewData.value = null;
  showPreview.value = false;
  
  if (!file) return;
  
  const isMarkdown = file.name.endsWith('.md');
  const isJson = file.name.endsWith('.json');
  
  if ((fileFormat.value === 'markdown' && isMarkdown) || (fileFormat.value === 'json' && isJson)) {
    try {
      // Read file contents
      const content = await readFileContent(file);
      fileContents.value = content;
      
      // Generate preview data
      generatePreview(content, fileFormat.value);
    } catch (error) {
      console.error('Error reading file:', error);
      importError.value = 'Failed to read file. Please try again.';
    }
  } else {
    importError.value = `Invalid file format. Please select a ${fileFormat.value === 'markdown' ? 'markdown (.md)' : 'JSON (.json)'} file.`;
  }
};

// Read file content
const readFileContent = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      resolve(event.target.result);
    };
    
    reader.onerror = (error) => {
      reject(new Error('Failed to read file: ' + error.message));
    };
    
    reader.readAsText(file);
  });
};

// Generate preview data from file content
const generatePreview = (content, format) => {
  try {
    if (format === 'markdown') {
      generateMarkdownPreview(content);
    } else if (format === 'json') {
      generateJsonPreview(content);
    }
  } catch (error) {
    console.error('Error generating preview:', error);
    importError.value = 'Failed to parse file. Please check the file format.';
  }
};

// Generate preview from markdown content
const generateMarkdownPreview = (content) => {
  // Extract makes and count vehicles
  const makeRegex = /## ([^\n]+)/g;
  const makes = [...content.matchAll(makeRegex)].map(match => match[1]);
  
  // Count vehicles per make
  const vehicleCounts = {};
  for (const make of makes) {
    const makeMatches = [...content.matchAll(new RegExp(`## ${make}([\\s\\S]*?)(?=## |$)`, 'g'))];
    if (makeMatches.length > 0) {
      const makeContent = makeMatches[0][1];
      const vehicleEntries = [...makeContent.matchAll(/\d+\.\s+\*\*([^*]+)\*\*/g)];
      vehicleCounts[make] = vehicleEntries.length;
    } else {
      vehicleCounts[make] = 0;
    }
  }
  
  // Calculate total vehicles
  const totalVehicles = Object.values(vehicleCounts).reduce((sum, count) => sum + count, 0);
  
  previewData.value = {
    makes: makes.length,
    totalVehicles,
    vehicleCounts
  };
  
  showPreview.value = true;
};

// Generate preview from JSON content
const generateJsonPreview = (content) => {
  try {
    const data = JSON.parse(content);
    
    if (!data.vehicles || !Array.isArray(data.vehicles)) {
      throw new Error('Invalid JSON format. Expected a "vehicles" array.');
    }
    
    const vehicles = data.vehicles;
    
    // Count makes
    const makeCount = {};
    vehicles.forEach(vehicle => {
      if (vehicle.make) {
        makeCount[vehicle.make] = (makeCount[vehicle.make] || 0) + 1;
      }
    });
    
    previewData.value = {
      makes: Object.keys(makeCount).length,
      totalVehicles: vehicles.length,
      vehicleCounts: makeCount
    };
    
    showPreview.value = true;
  } catch (error) {
    console.error('Error parsing JSON:', error);
    importError.value = 'Failed to parse JSON file. Please check the file format.';
  }
};

// Create and download a sample JSON file
const downloadSampleJson = () => {
  const sampleData = createSampleJsonInventory();
  const jsonString = JSON.stringify(sampleData, null, 2);
  const blob = new Blob([jsonString], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = 'sample_inventory.json';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// Handle import button click
const handleImport = async () => {
  if (isImporting.value || !selectedFile.value || !isValidFile.value || !isValidContent.value) return;
  
  isImporting.value = true;
  importResult.value = null;
  importError.value = null;
  
  try {
    // Call the import function with the selected file
    const count = await importInventory(selectedFile.value);
    importedCount.value = count;
    importResult.value = `Successfully imported ${count} vehicles from inventory`;
    
    // Clear file selection after successful import
    if (count > 0) {
      selectedFile.value = null;
      fileContents.value = null;
      previewData.value = null;
      showPreview.value = false;
    }
  } catch (error) {
    console.error('Import error:', error);
    importError.value = error.message || 'An error occurred during import';
  } finally {
    isImporting.value = false;
  }
};

// Format number with commas
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};
</script>

<template>
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900">Import Inventory</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">
        Import vehicles from a file into the system
      </p>
    </div>
    
    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
      <div class="flex flex-col space-y-4">
        <p class="text-sm text-gray-700">
          Upload a file containing vehicle inventory data.
          The file will be parsed to create vehicle listings for all cars in the inventory.
          The process may take a few moments to complete.
        </p>
        
        <!-- File Format Selection -->
        <div class="mt-2">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Select File Format
          </label>
          <div class="flex space-x-4">
            <button 
              @click="handleFormatChange('markdown')" 
              class="px-4 py-2 text-sm font-medium rounded-md"
              :class="fileFormat === 'markdown' 
                ? 'bg-primary text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
            >
              Markdown (.md)
            </button>
            <button 
              @click="handleFormatChange('json')" 
              class="px-4 py-2 text-sm font-medium rounded-md"
              :class="fileFormat === 'json' 
                ? 'bg-primary text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
            >
              JSON (.json)
            </button>
          </div>
        </div>
        
        <!-- File Upload Input -->
        <div class="mt-4">
          <label for="file-upload" class="block text-sm font-medium text-gray-700">
            Select Inventory File ({{ fileFormat === 'markdown' ? 'GTINV.md' : 'inventory.json' }})
          </label>
          <div class="mt-1 flex items-center">
            <input
              id="file-upload"
              type="file"
              :accept="fileFormat === 'markdown' ? '.md' : '.json'"
              @change="handleFileChange"
              class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary-dark"
            />
          </div>
          <p class="mt-1 text-sm text-gray-500" v-if="selectedFile">
            Selected file: {{ selectedFile.name }}
          </p>
          
          <!-- File validation messages -->
          <p v-if="selectedFile && !isValidFile" class="mt-1 text-sm text-red-500">
            Please select a {{ fileFormat === 'markdown' ? 'markdown (.md)' : 'JSON (.json)' }} file
          </p>
          <p v-if="fileContents && !isValidContent" class="mt-1 text-sm text-red-500">
            Invalid file format. The file must be a valid {{ fileFormat === 'markdown' ? 'GT Motorsports inventory markdown' : 'vehicle inventory JSON' }} file.
          </p>
        </div>
        
        <!-- Sample File Download -->
        <div v-if="fileFormat === 'json'" class="mt-2">
          <button 
            @click="downloadSampleJson" 
            class="text-sm text-primary hover:text-primary-dark font-medium flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            Download Sample JSON File
          </button>
          <p class="text-xs text-gray-500 mt-1">
            Download a sample JSON file to see the expected format
          </p>
        </div>
        
        <!-- File Preview -->
        <div v-if="showPreview && previewData" class="mt-4 p-4 bg-blue-50 rounded-md">
          <h4 class="font-medium text-blue-800">File Preview</h4>
          <div class="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white p-3 rounded shadow-sm">
              <p class="text-sm text-gray-500">Makes</p>
              <p class="text-xl font-semibold">{{ previewData.makes }}</p>
            </div>
            <div class="bg-white p-3 rounded shadow-sm">
              <p class="text-sm text-gray-500">Total Vehicles</p>
              <p class="text-xl font-semibold">{{ formatNumber(previewData.totalVehicles) }}</p>
            </div>
            <div class="bg-white p-3 rounded shadow-sm">
              <p class="text-sm text-gray-500">File Size</p>
              <p class="text-xl font-semibold">{{ Math.round(selectedFile.size / 1024) }} KB</p>
            </div>
          </div>
          
          <!-- Top makes preview -->
          <div v-if="Object.keys(previewData.vehicleCounts).length > 0" class="mt-4">
            <h5 class="text-sm font-medium text-blue-800 mb-2">Top Makes</h5>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
              <div
                v-for="(count, make) in Object.fromEntries(
                  Object.entries(previewData.vehicleCounts)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 8)
                )"
                :key="make"
                class="bg-white p-2 rounded text-sm"
              >
                <span class="font-medium">{{ make }}:</span> {{ count }} vehicles
              </div>
            </div>
          </div>
        </div>
        
        <div class="flex items-center">
          <button
            @click="handleImport"
            :disabled="isImporting || !selectedFile || !isValidFile || !isValidContent"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            :class="{ 'opacity-75 cursor-not-allowed': isImporting || !selectedFile || !isValidFile || !isValidContent }"
          >
            <svg v-if="isImporting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isImporting ? 'Importing...' : 'Import Inventory' }}
          </button>
        </div>
        
        <!-- Import Result -->
        <div v-if="importResult" class="p-4 bg-green-50 border-l-4 border-green-400 text-green-700">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium">{{ importResult }}</p>
              <div v-if="importedCount > 0" class="mt-2 text-sm">
                <p>The vehicles have been added to your inventory and are now available in the system.</p>
                <p class="mt-1">You can view and manage them in the inventory table above.</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Import Error -->
        <div v-if="importError" class="p-4 bg-red-50 border-l-4 border-red-400 text-red-700">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium">{{ importError }}</p>
              <p class="mt-2 text-sm">
                Please check the file format and try again. 
                {{ fileFormat === 'markdown' 
                  ? 'The file should be a markdown (.md) file with the correct GT Motorsports inventory structure.' 
                  : 'The file should be a JSON (.json) file with a "vehicles" array containing vehicle objects.' 
                }}
              </p>
            </div>
          </div>
        </div>
        
        <!-- Help Section -->
        <div class="mt-6 border-t border-gray-200 pt-4">
          <h4 class="text-sm font-medium text-gray-700">File Format Requirements</h4>
          
          <!-- Markdown Format Help -->
          <div v-if="fileFormat === 'markdown'">
            <p class="mt-1 text-sm text-gray-500">The markdown file should follow this structure:</p>
            <pre class="mt-2 p-2 bg-gray-50 rounded text-xs overflow-x-auto">
# GT Motorsports Vehicle Inventory

## [Make Name]

1. **[Year] [Make] [Model]**
   - Trim: [Trim]
   - Body Style: [Body Style]
   - Engine: [Engine]
   - Exterior Color: [Color]
   - Interior Color: [Color]
   - Drive Type: [Drive Type]
   - Odometer: [Mileage] km
   - Price: $[Price]
            </pre>
          </div>
          
          <!-- JSON Format Help -->
          <div v-if="fileFormat === 'json'">
            <p class="mt-1 text-sm text-gray-500">The JSON file should follow this structure:</p>
            <pre class="mt-2 p-2 bg-gray-50 rounded text-xs overflow-x-auto">
{
  "name": "GT Motorsports Vehicle Inventory",
  "description": "Complete inventory of vehicles",
  "vehicles": [
    {
      "title": "2023 Electric Sedan",
      "year": 2023,
      "make": "Example Make",
      "model": "Electric Sedan",
      "trim": "Long Range AWD",
      "bodyStyle": "Sedan",
      "engine": "Electric",
      "exteriorColor": "Red",
      "interiorColor": "Black",
      "drivetrain": "AWD",
      "mileage": 5000,
      "price": 45988
    },
    // More vehicles...
  ]
}
            </pre>
            <p class="mt-2 text-xs text-gray-500">
              Click the "Download Sample JSON File" button above to get a complete example.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>