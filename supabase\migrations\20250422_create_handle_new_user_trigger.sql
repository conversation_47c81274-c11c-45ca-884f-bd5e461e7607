-- Create a trigger to execute the handle-new-user Edge Function
-- This trigger activates AFTER INSERT on the auth.users table
-- It executes for each ROW and calls the handle-new-user Edge Function

CREATE OR REPLACE TRIGGER trigger_handle_new_user
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION supabase_functions.http_request(
  'https://wjqlfcxgrdfyqpjsbnyp.supabase.co/functions/v1/handle-new-user',
  'POST',
  '{"Content-Type":"application/json"}',
  '{"record":' || row_to_json(NEW) || '}'
);

-- Note: Replace YOUR_PROJECT_REF with your actual Supabase project reference
-- Example: if your Supabase URL is https://abcdefghijklm.supabase.co,
-- then YOUR_PROJECT_REF would be abcdefghijklm