// src/components/admin/VehicleForm.vue
<script setup>
import { ref, computed, watch } from 'vue';
import ImageUploader from './ImageUploader.vue';

// Define the URL for your backend proxy endpoint
const PROXY_DECODE_API_URL = '/api/decode-vin'; // Adjust if your backend route is different

const props = defineProps({
  vehicle: {
    type: Object,
    default: () => ({
      title: '',
      price: '',
      specialPrice: '',
      image: '',
      gallery: [''],
      mileage: '',
      year: null,
      make: '',
      model: '',
      trim: '',
      doors: null,
      bodyStyle: '',
      engine: '',
      engineSize: '',
      drivetrain: '',
      transmission: '',
      exteriorColor: '',
      interiorColor: '',
      passengers: null,
      fuelType: '',
      cityFuel: '',
      hwyFuel: '',
      stockNumber: '',
      vin: '',
      carfaxLink: '',
      highlights: [''],
      features: [''],
      description: ''
    })
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  isSubmitting: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['submit', 'cancel', 'update:vehicle']);

// Form data - Ensure initial values match the default prop structure
const formData = ref({ ...props.vehicle });
// Ensure arrays are properly initialized if missing from prop
if (!Array.isArray(formData.value.gallery)) formData.value.gallery = [''];
if (!Array.isArray(formData.value.highlights)) formData.value.highlights = [''];
if (!Array.isArray(formData.value.features)) formData.value.features = [''];


// VIN lookup state
const isLookingUpVin = ref(false);
const vinLookupError = ref('');
const vinLookupSuccess = ref(false);

// Form validation errors
const errors = ref({});


// Computed properties
const formTitle = computed(() => props.isEdit ? 'Edit Vehicle' : 'Add New Vehicle');
const submitButtonText = computed(() => props.isEdit ? 'Update Vehicle' : 'Add Vehicle');

// Computed property for managing all images (main + gallery)
const allImages = computed({
  get: () => {
    const images = [];
    if (formData.value.image) {
      images.push(formData.value.image);
    }
    if (Array.isArray(formData.value.gallery)) {
        images.push(...formData.value.gallery.filter(url => url && url !== formData.value.image));
    }
    return images;
  },
  set: (newImages) => {
    if (newImages && newImages.length > 0) {
      const processedImages = newImages.map(img => {
        if (typeof img === 'object' && img !== null) {
          return img.url || '';
        }
        return img;
      }).filter(Boolean);

      formData.value.image = processedImages[0] || '';
      formData.value.gallery = [...processedImages];
    } else {
      formData.value.image = '';
      formData.value.gallery = [''];
    }
  }
});


// Add empty field to dynamic arrays (Highlights, Features)
const addField = (fieldName) => {
    if (Array.isArray(formData.value[fieldName])) {
        formData.value[fieldName].push('');
    } else {
        formData.value[fieldName] = [''];
    }
};

// Remove field from dynamic arrays
const removeField = (fieldName, index) => {
  if (Array.isArray(formData.value[fieldName]) && formData.value[fieldName].length > 1) {
    formData.value[fieldName].splice(index, 1);
  } else if (Array.isArray(formData.value[fieldName]) && formData.value[fieldName].length === 1) {
      formData.value[fieldName][0] = '';
  }
};

// --- VIN Lookup function calling the BACKEND PROXY ---
const lookupVin = async () => {
  const vin = formData.value.vin ? formData.value.vin.trim().toUpperCase() : '';

  if (!vin || vin.length !== 17) {
    vinLookupError.value = 'Please enter a valid 17-digit VIN.';
    vinLookupSuccess.value = false;
    return;
  }

  isLookingUpVin.value = true;
  vinLookupError.value = '';
  vinLookupSuccess.value = false;

  try {
    // Prepare the payload to send to YOUR backend
    const payload = { vin: vin };

    // Make the request to YOUR backend proxy endpoint
    const response = await fetch(PROXY_DECODE_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json', // Sending JSON to backend
        'Accept': 'application/json'       // Expecting JSON response from backend
      },
      body: JSON.stringify(payload), // Send VIN in JSON body
    });

    const data = await response.json(); // Parse JSON response from YOUR backend

    if (!response.ok) {
        // Handle errors reported by YOUR backend
        throw new Error(data.error || `Server error: ${response.statusText}`);
    }

    // Assuming your backend returns the decoded data directly on success
    // e.g., { year: 2020, make: 'Toyota', model: 'Camry', ... features: [...] }
    console.log('Data received from backend proxy:', JSON.stringify(data, null, 2));
    console.log('Current formData before update:', JSON.stringify(formData.value, null, 2));

    // --- Populate formData with data from YOUR backend response ---
    // (Adjust these assignments based on the exact structure your backend returns)
    formData.value.year = data.year || formData.value.year;
    formData.value.make = data.make || formData.value.make;
    formData.value.model = data.model || formData.value.model;
    formData.value.trim = data.trim || formData.value.trim;
    formData.value.bodyStyle = data.bodyStyle || formData.value.bodyStyle;
    formData.value.engine = data.engine || formData.value.engine;
    formData.value.engineSize = data.engineSize || formData.value.engineSize;
    formData.value.transmission = data.transmission || formData.value.transmission;
    formData.value.drivetrain = data.drivetrain || formData.value.drivetrain;
    formData.value.fuelType = data.fuelType || formData.value.fuelType;
    formData.value.cityFuel = data.cityFuel || formData.value.cityFuel;
    formData.value.hwyFuel = data.hwyFuel || formData.value.hwyFuel;
    formData.value.doors = data.doors || formData.value.doors;
    formData.value.passengers = data.passengers || formData.value.passengers;
    formData.value.exteriorColor = data.exteriorColor || formData.value.exteriorColor;
    formData.value.interiorColor = data.interiorColor || formData.value.interiorColor;

    // Handle features array returned from backend
    if (Array.isArray(data.features) && data.features.length > 0) {
        formData.value.features = data.features;
    } else if (!formData.value.features || formData.value.features.length === 0 || (formData.value.features.length === 1 && !formData.value.features[0])) {
        // Ensure features array has at least one empty string if none returned
        formData.value.features = [''];
    }

    // Auto-generate Title if empty and basic info is present
    if (!formData.value.title && formData.value.year && formData.value.make && formData.value.model) {
      formData.value.title = `${formData.value.year} ${formData.value.make} ${formData.value.model}${formData.value.trim ? ' ' + formData.value.trim : ''}`.trim();
    }

    vinLookupSuccess.value = true;
    vinLookupError.value = ''; // Clear error on success
    
    console.log('Updated formData after VIN lookup:', JSON.stringify(formData.value, null, 2));
    
    // Emit the updated formData to the parent component
    emit('update:vehicle', { ...formData.value });
    console.log('Emitted update:vehicle event with data');

  } catch (error) {
    console.error('Error looking up VIN via proxy:', error);
    vinLookupError.value = `VIN Lookup Failed: ${error.message}`;
    vinLookupSuccess.value = false;
  } finally {
    isLookingUpVin.value = false;
  }
};


// Form validation function (Keep your existing validation logic)
const validateForm = () => {
  const newErrors = {};
  // --- Your existing validation rules ---
  if (!formData.value.title) newErrors.title = 'Title is required';
  if (!formData.value.price) newErrors.price = 'Price is required';
  else if (isNaN(Number(formData.value.price))) newErrors.price = 'Price must be a number';

  const validImages = allImages.value.filter(img => {
    if (typeof img === 'object' && img !== null) return !img.failed;
    return img && typeof img === 'string' && img.trim() !== '';
  });
  if (validImages.length === 0) newErrors.image = 'At least one valid image is required';

  if (!formData.value.make) newErrors.make = 'Make is required';
  if (!formData.value.model) newErrors.model = 'Model is required';
  if (!formData.value.year) newErrors.year = 'Year is required';
  else if (isNaN(Number(formData.value.year))) newErrors.year = 'Year must be a number';

  if (!formData.value.stockNumber) newErrors.stockNumber = 'Stock number is required';
  if (!formData.value.vin) newErrors.vin = 'VIN is required';
  else if (formData.value.vin.length !== 17) newErrors.vin = 'VIN must be 17 characters';

  if (formData.value.specialPrice && isNaN(Number(formData.value.specialPrice))) newErrors.specialPrice = 'Special price must be a number';
  if (formData.value.mileage && isNaN(Number(formData.value.mileage))) newErrors.mileage = 'Mileage must be a number';
  if (formData.value.doors && isNaN(Number(formData.value.doors))) newErrors.doors = 'Doors must be a number';
  if (formData.value.passengers && isNaN(Number(formData.value.passengers))) newErrors.passengers = 'Passengers must be a number';

  const validateArrayField = (fieldName, fieldLabel) => {
      if (Array.isArray(formData.value[fieldName])) {
          formData.value[fieldName].forEach((item, index) => {
              if (!item && formData.value[fieldName].length > 1) {
                  if (!newErrors[fieldName]) newErrors[fieldName] = {};
                  newErrors[fieldName][index] = `${fieldLabel} cannot be empty`;
              }
          });
      }
  };
  validateArrayField('highlights', 'Highlight');
  validateArrayField('features', 'Feature');
  // --- End of validation rules ---

  errors.value = newErrors;
  return Object.keys(newErrors).length === 0;
};


// Generate a folder name for the vehicle images (Keep existing logic)
const getVehicleFolderName = () => {
  if (props.isEdit && formData.value.id) {
    return formData.value.id;
  }
  let folderName = '';
  if (formData.value.year) folderName += formData.value.year;
  if (formData.value.make) folderName += (folderName ? '-' : '') + formData.value.make.replace(/\s+/g, '-');
  if (formData.value.model) folderName += (folderName ? '-' : '') + formData.value.model.replace(/\s+/g, '-');
  if (!folderName) folderName = 'temp-' + Date.now();
  folderName += '-' + Date.now().toString().slice(-6);
  return folderName.toLowerCase();
};

// Submit form (Keep existing logic)
const submitForm = () => {
  if (validateForm()) {
    if (!formData.value.title && formData.value.year && formData.value.make && formData.value.model) {
      formData.value.title = `${formData.value.year} ${formData.value.make} ${formData.value.model}${formData.value.trim ? ' ' + formData.value.trim : ''}`.trim();
    }

    const processedData = {
      ...formData.value,
      price: formData.value.price ? Number(formData.value.price) : null,
      specialPrice: formData.value.specialPrice ? Number(formData.value.specialPrice) : null,
      mileage: formData.value.mileage ? Number(formData.value.mileage) : null,
      year: formData.value.year ? Number(formData.value.year) : null,
      doors: formData.value.doors ? Number(formData.value.doors) : null,
      passengers: formData.value.passengers ? Number(formData.value.passengers) : null,
      vin: formData.value.vin ? formData.value.vin.toUpperCase() : ''
    };

    const validImageUrls = allImages.value
        .map(img => (typeof img === 'object' && img !== null && !img.failed) ? img.url : (typeof img === 'string' ? img : null))
        .filter(url => url && url.trim() !== '');
    processedData.image = validImageUrls[0] || '';
    processedData.gallery = validImageUrls;

    processedData.highlights = formData.value.highlights.filter(h => h && h.trim() !== '');
    if (processedData.highlights.length === 0) processedData.highlights = [''];
    processedData.features = formData.value.features.filter(f => f && f.trim() !== '');
    if (processedData.features.length === 0) processedData.features = [''];

    console.log('Submitting processed data:', processedData);
    emit('submit', processedData);
  } else {
      console.log("Form validation failed:", errors.value);
  }
};

// Cancel form
const cancelForm = () => {
  emit('cancel');
};
</script>

<template>
  <div class="bg-white shadow overflow-hidden sm:rounded-lg relative">
    <!-- Loading Overlay -->
     <div v-if="props.isSubmitting" class="absolute inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center z-10">
       <div class="bg-white p-6 rounded-lg shadow-xl text-center">
         <svg class="animate-spin h-10 w-10 text-primary mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
           <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
           <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
         </svg>
         <p class="text-lg font-medium text-gray-900">{{ props.isEdit ? 'Updating vehicle...' : 'Adding vehicle...' }}</p>
         <p class="text-sm text-gray-500 mt-2">Please wait...</p>
       </div>
     </div>

    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
      <h3 class="text-lg leading-6 font-medium text-gray-900">{{ formTitle }}</h3>
      <button
        type="button"
        @click="cancelForm"
        class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Cancel
      </button>
    </div>

    <div class="border-t border-gray-200">
      <form @submit.prevent="submitForm" class="p-6">
        <!-- VIN Lookup Section -->
        <div class="mb-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 class="text-md font-medium text-gray-900 mb-3">VIN Lookup</h4>
          <p class="text-sm text-gray-600 mb-3">Enter the VIN to automatically populate vehicle details.</p>
          <div class="flex flex-col sm:flex-row sm:items-start gap-3">
            <div class="flex-grow">
              <label for="vin" class="sr-only">VIN</label>
              <input
                type="text"
                id="vin"
                v-model="formData.vin"
                placeholder="Enter 17-Digit Vehicle Identification Number (VIN)"
                maxlength="17"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm uppercase"
                :class="{ 'border-red-500': errors.vin || vinLookupError }"
                aria-describedby="vin-error vin-success"
              />
              <p v-if="errors.vin" id="vin-error-validation" class="mt-1 text-sm text-red-600">{{ errors.vin }}</p>
            </div>
            <button
              type="button"
              @click="lookupVin"
              :disabled="isLookingUpVin"
              class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-75 disabled:cursor-not-allowed"
            >
              <svg v-if="isLookingUpVin" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              {{ isLookingUpVin ? 'Decoding...' : 'Decode VIN' }}
            </button>
          </div>

          <!-- VIN Lookup Result Messages -->
           <div v-if="vinLookupError" id="vin-error" class="mt-3 p-3 bg-red-50 border-l-4 border-red-400 text-sm text-red-700 rounded-r-md">
            {{ vinLookupError }}
           </div>
           <div v-if="vinLookupSuccess" id="vin-success" class="mt-3 p-3 bg-green-50 border-l-4 border-green-400 text-sm text-green-700 rounded-r-md">
             VIN decoded successfully! Form fields have been updated. Please review and complete the remaining details.
           </div>
        </div>

        <!-- Basic Information -->
        <div class="mb-8">
          <h4 class="text-md font-medium text-gray-900 mb-4">Basic Information</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            <!-- Title -->
            <div>
              <label for="title" class="block text-sm font-medium text-gray-700">Listing Title</label>
              <input type="text" id="title" v-model="formData.title" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.title }"/>
              <p v-if="errors.title" class="mt-1 text-sm text-red-600">{{ errors.title }}</p>
            </div>
             <!-- Stock Number -->
            <div>
              <label for="stockNumber" class="block text-sm font-medium text-gray-700">Stock Number</label>
              <input type="text" id="stockNumber" v-model="formData.stockNumber" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.stockNumber }"/>
              <p v-if="errors.stockNumber" class="mt-1 text-sm text-red-600">{{ errors.stockNumber }}</p>
            </div>
             <!-- Price -->
            <div>
              <label for="price" class="block text-sm font-medium text-gray-700">Price ($)</label>
              <input type="number" step="0.01" id="price" v-model="formData.price" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.price }"/>
              <p v-if="errors.price" class="mt-1 text-sm text-red-600">{{ errors.price }}</p>
            </div>
             <!-- Special Price -->
            <div>
              <label for="specialPrice" class="block text-sm font-medium text-gray-700">Special Price ($) <span class="text-xs text-gray-500">(Optional)</span></label>
              <input type="number" step="0.01" id="specialPrice" v-model="formData.specialPrice" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.specialPrice }"/>
              <p v-if="errors.specialPrice" class="mt-1 text-sm text-red-600">{{ errors.specialPrice }}</p>
            </div>
             <!-- Make -->
            <div>
              <label for="make" class="block text-sm font-medium text-gray-700">Make</label>
              <input type="text" id="make" v-model="formData.make" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.make }"/>
              <p v-if="errors.make" class="mt-1 text-sm text-red-600">{{ errors.make }}</p>
            </div>
             <!-- Model -->
            <div>
              <label for="model" class="block text-sm font-medium text-gray-700">Model</label>
              <input type="text" id="model" v-model="formData.model" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.model }"/>
              <p v-if="errors.model" class="mt-1 text-sm text-red-600">{{ errors.model }}</p>
            </div>
             <!-- Trim -->
            <div>
              <label for="trim" class="block text-sm font-medium text-gray-700">Trim <span class="text-xs text-gray-500">(Optional)</span></label>
              <input type="text" id="trim" v-model="formData.trim" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.trim }"/>
              <p v-if="errors.trim" class="mt-1 text-sm text-red-600">{{ errors.trim }}</p>
            </div>
             <!-- Year -->
            <div>
              <label for="year" class="block text-sm font-medium text-gray-700">Year</label>
              <input type="number" id="year" v-model.number="formData.year" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.year }"/>
              <p v-if="errors.year" class="mt-1 text-sm text-red-600">{{ errors.year }}</p>
            </div>
             <!-- Mileage -->
            <div>
              <label for="mileage" class="block text-sm font-medium text-gray-700">Mileage (km) <span class="text-xs text-gray-500">(Optional)</span></label>
              <input type="number" id="mileage" v-model.number="formData.mileage" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.mileage }"/>
              <p v-if="errors.mileage" class="mt-1 text-sm text-red-600">{{ errors.mileage }}</p>
            </div>
          </div>
        </div>

        <!-- Vehicle Details -->
        <div class="mb-8">
          <h4 class="text-md font-medium text-gray-900 mb-4">Vehicle Details <span class="text-sm font-normal text-gray-500">(Filled from VIN where possible)</span></h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4">
             <!-- Body Style -->
            <div>
              <label for="bodyStyle" class="block text-sm font-medium text-gray-700">Body Style</label>
              <input type="text" id="bodyStyle" v-model="formData.bodyStyle" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
            </div>
             <!-- Doors -->
            <div>
              <label for="doors" class="block text-sm font-medium text-gray-700">Doors</label>
              <input type="number" id="doors" v-model.number="formData.doors" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.doors }"/>
               <p v-if="errors.doors" class="mt-1 text-sm text-red-600">{{ errors.doors }}</p>
            </div>
             <!-- Passengers -->
            <div>
              <label for="passengers" class="block text-sm font-medium text-gray-700">Passengers</label>
              <input type="number" id="passengers" v-model.number="formData.passengers" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" :class="{ 'border-red-500': errors.passengers }"/>
               <p v-if="errors.passengers" class="mt-1 text-sm text-red-600">{{ errors.passengers }}</p>
            </div>
             <!-- Engine -->
            <div>
              <label for="engine" class="block text-sm font-medium text-gray-700">Engine</label>
              <input type="text" id="engine" v-model="formData.engine" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
            </div>
             <!-- Engine Size -->
            <div>
              <label for="engineSize" class="block text-sm font-medium text-gray-700">Engine Size</label>
              <input type="text" id="engineSize" v-model="formData.engineSize" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
            </div>
             <!-- Transmission -->
            <div>
              <label for="transmission" class="block text-sm font-medium text-gray-700">Transmission</label>
              <input type="text" id="transmission" v-model="formData.transmission" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
            </div>
             <!-- Drivetrain -->
            <div>
              <label for="drivetrain" class="block text-sm font-medium text-gray-700">Drivetrain</label>
              <input type="text" id="drivetrain" v-model="formData.drivetrain" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
            </div>
             <!-- Fuel Type -->
            <div>
              <label for="fuelType" class="block text-sm font-medium text-gray-700">Fuel Type</label>
              <input type="text" id="fuelType" v-model="formData.fuelType" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
            </div>
             <!-- Exterior Color -->
            <div>
              <label for="exteriorColor" class="block text-sm font-medium text-gray-700">Exterior Color</label>
              <input type="text" id="exteriorColor" v-model="formData.exteriorColor" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
            </div>
             <!-- Interior Color -->
            <div>
              <label for="interiorColor" class="block text-sm font-medium text-gray-700">Interior Color</label>
              <input type="text" id="interiorColor" v-model="formData.interiorColor" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
            </div>
             <!-- City Fuel -->
            <div>
              <label for="cityFuel" class="block text-sm font-medium text-gray-700">City Fuel Economy</label>
              <input type="text" id="cityFuel" v-model="formData.cityFuel" placeholder="e.g., 9.5 L/100km or 25 MPG" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
            </div>
             <!-- Highway Fuel -->
            <div>
              <label for="hwyFuel" class="block text-sm font-medium text-gray-700">Highway Fuel Economy</label>
              <input type="text" id="hwyFuel" v-model="formData.hwyFuel" placeholder="e.g., 7.0 L/100km or 35 MPG" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
            </div>
            <!-- Carfax Link -->
            <div>
              <label for="carfaxLink" class="block text-sm font-medium text-gray-700">Carfax Report Link</label>
              <input type="url" id="carfaxLink" v-model="formData.carfaxLink" placeholder="https://www.carfax.com/report/..." class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"/>
              <p class="mt-1 text-xs text-gray-500">Enter the full URL to the Carfax report for this vehicle</p>
            </div>
          </div>
        </div>

        <!-- Images -->
        <div class="mb-8">
          <h4 class="text-md font-medium text-gray-900 mb-4">Images</h4>
          <ImageUploader
            v-model:images="allImages"
            :max-images="10"
            :vehicle-id="getVehicleFolderName()"
            :class="{'ring-2 ring-red-500 rounded-md': errors.image }"
            aria-describedby="image-error"
          />
          <p v-if="errors.image" id="image-error" class="mt-1 text-sm text-red-600">{{ errors.image }}</p>
          <p class="mt-2 text-sm text-gray-500">
            Drag and drop images or click to upload. The first image is the main image. Max 10 images.
          </p>
        </div>

        <!-- Highlights and Features -->
        <div class="mb-8 grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
          <!-- Highlights -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Highlights <span class="text-xs text-gray-500">(Optional key points)</span></label>
            <div v-for="(highlight, index) in formData.highlights" :key="`highlight-${index}`" class="flex items-center mb-2">
              <input
                type="text"
                v-model="formData.highlights[index]"
                class="flex-grow px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                :class="{ 'border-red-500': errors.highlights && errors.highlights[index] }"
                placeholder="e.g., Low Mileage, One Owner"
                :aria-describedby="errors.highlights && errors.highlights[index] ? `highlight-error-${index}` : null"
              />
              <button
                type="button"
                @click="removeField('highlights', index)"
                class="ml-2 p-1.5 text-gray-400 hover:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="formData.highlights.length <= 1"
                title="Remove Highlight"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
             <p v-if="errors.highlights && errors.highlights[index]" :id="`highlight-error-${index}`" class="mt-1 text-sm text-red-600">{{ errors.highlights[index] }}</p>
            <button
              type="button"
              @click="addField('highlights')"
              class="mt-1 inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
              </svg>
              Add Highlight
            </button>
          </div>

          <!-- Features -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Features <span class="text-xs text-gray-500">(Populated from VIN if possible)</span></label>
            <div v-for="(feature, index) in formData.features" :key="`feature-${index}`" class="flex items-center mb-2">
              <input
                type="text"
                v-model="formData.features[index]"
                class="flex-grow px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                :class="{ 'border-red-500': errors.features && errors.features[index] }"
                placeholder="e.g., Sunroof, Leather Seats"
                :aria-describedby="errors.features && errors.features[index] ? `feature-error-${index}` : null"
              />
              <button
                type="button"
                @click="removeField('features', index)"
                class="ml-2 p-1.5 text-gray-400 hover:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="formData.features.length <= 1"
                 title="Remove Feature"
             >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
             <p v-if="errors.features && errors.features[index]" :id="`feature-error-${index}`" class="mt-1 text-sm text-red-600">{{ errors.features[index] }}</p>
            <button
              type="button"
              @click="addField('features')"
              class="mt-1 inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
              </svg>
              Add Feature
            </button>
          </div>
        </div>

        <!-- Description -->
        <div class="mb-8">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description <span class="text-xs text-gray-500">(Optional)</span></label>
          <textarea
            id="description"
            v-model="formData.description"
            rows="5"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            placeholder="Enter vehicle description..."
          ></textarea>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-5 border-t border-gray-200">
          <button
            type="button"
            @click="cancelForm"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="props.isSubmitting"
            class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-75 disabled:cursor-not-allowed"
          >
            <svg v-if="props.isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ submitButtonText }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>