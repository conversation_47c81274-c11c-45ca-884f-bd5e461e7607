// Debug script to check tenant setup
import { supabase } from './src/lib/supabaseClient.js';

async function debugTenant() {
  try {
    console.log('=== DEBUGGING TENANT SETUP ===');
    
    // Check current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) {
      console.error('Error getting user:', userError);
      return;
    }
    
    console.log('Current user ID:', user?.id);
    console.log('User email:', user?.email);
    console.log('User app_metadata:', user?.app_metadata);
    console.log('User user_metadata:', user?.user_metadata);
    
    const tenant_id = user?.app_metadata?.tenant_id || user?.user_metadata?.tenant_id || user?.id;
    console.log('Resolved tenant_id:', tenant_id);
    
    // Check if tenants table exists
    console.log('\n=== CHECKING TENANTS TABLE ===');
    const { data: tenants, error: tenantsError } = await supabase
      .from('tenants')
      .select('*')
      .limit(5);
    
    if (tenantsError) {
      console.error('Error accessing tenants table:', tenantsError);
    } else {
      console.log('Tenants table exists. Sample data:', tenants);
    }
    
    // Check if our tenant exists
    if (tenant_id) {
      console.log('\n=== CHECKING SPECIFIC TENANT ===');
      const { data: specificTenant, error: specificTenantError } = await supabase
        .from('tenants')
        .select('*')
        .eq('id', tenant_id)
        .single();
      
      if (specificTenantError) {
        console.error('Error finding specific tenant:', specificTenantError);
      } else {
        console.log('Found tenant:', specificTenant);
      }
    }
    
    // Check GTINV table structure
    console.log('\n=== CHECKING GTINV TABLE ===');
    const { data: gtinvSample, error: gtinvError } = await supabase
      .from('GTINV')
      .select('*')
      .limit(1);
    
    if (gtinvError) {
      console.error('Error accessing GTINV table:', gtinvError);
    } else {
      console.log('GTINV table accessible. Sample data:', gtinvSample);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

debugTenant();
