<template>
  <div class="min-h-screen bg-gray-100">
    <!-- <PERSON><PERSON> -->
    <AdminNavbar />

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-20">
      <div v-if="isLoading" class="text-center py-10">
        <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-24 w-24 mx-auto mb-3" />
        <p class="text-gray-500">Loading AI Studio...</p>
      </div>

      <div v-else>
        <!-- AI Studio Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
          <div>
            <h2 class="text-2xl font-bold text-gray-900">AI Studio</h2>
            <p class="text-gray-600 mt-1">
              {{ vehicle ? `Editing: ${vehicle.year} ${vehicle.make} ${vehicle.model}` : 'No vehicle selected' }}
            </p>
          </div>

          <div class="flex space-x-3">
            <button
              @click="goBack"
              class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Inventory
            </button>
          </div>
        </div>

        <!-- AI Studio Content -->
        <div v-if="vehicle" class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6">
            <!-- Left Panel: Image Gallery -->
            <div class="lg:col-span-1 space-y-6">
              <div class="border rounded-lg overflow-hidden">
                <div class="bg-gray-50 px-4 py-2 border-b">
                  <h3 class="font-medium text-gray-700">Original Images</h3>
                </div>
                <div class="p-4 space-y-4">
                  <!-- Current Image Display -->
                  <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg overflow-hidden shadow-lg">
                    <img :src="currentImage" alt="Vehicle Image" class="object-contain w-full h-full" />
                  </div>

                  <!-- Image Navigation -->
                  <div class="flex justify-between items-center">
                    <button
                      @click="prevImage"
                      :disabled="currentImageIndex === 0"
                      class="bg-gray-200 text-gray-700 rounded-full p-2 hover:bg-gray-300 disabled:opacity-40 disabled:cursor-not-allowed transition-all"
                      aria-label="Previous Image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <span class="text-sm text-gray-600">{{ currentImageIndex + 1 }} / {{ vehicleImages.length }}</span>
                    <button
                      @click="nextImage"
                      :disabled="currentImageIndex === vehicleImages.length - 1"
                      class="bg-gray-200 text-gray-700 rounded-full p-2 hover:bg-gray-300 disabled:opacity-40 disabled:cursor-not-allowed transition-all"
                      aria-label="Next Image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>

                  <!-- Thumbnail Gallery -->
                   <div v-if="vehicleImages.length > 1" class="flex space-x-2 overflow-x-auto pb-2">
                    <button
                      v-for="(image, index) in vehicleImages"
                      :key="index"
                      @click="currentImageIndex = index; modifiedImageUrl = null; errorMessage = null;"
                      class="flex-shrink-0 h-16 w-24 rounded-md overflow-hidden focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                      :class="{'ring-2 ring-primary': currentImageIndex === index}"
                    >
                      <img :src="image" alt="Thumbnail" class="h-full w-full object-cover" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Middle Panel: AI Tools -->
            <div class="lg:col-span-1 space-y-6">
              <div class="border rounded-lg overflow-hidden">
                <div class="bg-gray-50 px-4 py-2 border-b">
                  <h3 class="font-medium text-gray-700">AI Tools</h3>
                </div>
                <div class="p-4 space-y-4">
                  <!-- AI Tools Section -->
                  <div class="space-y-4">
                    <!-- Background Removal -->
                    <div class="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <h4 class="font-medium text-gray-800 mb-2">Background Removal</h4>
                      <p class="text-sm text-gray-600 mb-3">Remove the background from your vehicle image</p>
                      <button
                        @click="applyBackgroundRemoval"
                        :disabled="isProcessing || !currentImage || currentImage === '/favicon.svg'"
                        class="w-full bg-primary text-white py-2 rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      >
                        <svg v-if="isProcessing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {{ isProcessing ? 'Processing...' : 'Apply' }}
                      </button>
                    </div>

                    <!-- Background Replacement and Relighting -->
                    <div class="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <h4 class="font-medium text-gray-800 mb-2">Change Background</h4>
                      <p class="text-sm text-gray-600 mb-3">Place your vehicle in a different environment with improved lighting</p>

                      <!-- Background Prompt Input -->
                      <div class="mb-3">
                        <label for="backgroundPrompt" class="block text-sm font-medium text-gray-700 mb-1">Background Description</label>
                        <input
                          type="text"
                          id="backgroundPrompt"
                          v-model="backgroundPrompt"
                          placeholder="e.g., sunset beach, mountain road, showroom"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                        />
                      </div>

                      <!-- Light Direction Dropdown -->
                      <div class="mb-3">
                        <label for="lightDirection" class="block text-sm font-medium text-gray-700 mb-1">Light Direction (Optional)</label>
                        <select
                          id="lightDirection"
                          v-model="lightDirection"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                        >
                          <option value="">Default</option>
                          <option value="above">Above</option>
                          <option value="below">Below</option>
                          <option value="left">Left</option>
                          <option value="right">Right</option>
                        </select>
                      </div>

                      <button
                        @click="applyBackgroundReplacement"
                        :disabled="isProcessing || !currentImage || currentImage === '/favicon.svg' || !backgroundPrompt"
                        class="w-full bg-primary text-white py-2 rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      >
                        <svg v-if="isProcessing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {{ isProcessing ? 'Processing...' : 'Apply' }}
                      </button>
                    </div>
                     <div class="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors opacity-50"> <!-- Example: Disabled look -->
                      <h4 class="font-medium text-gray-800 mb-2">360 Interactive View</h4>
                      <p class="text-sm text-gray-600 mb-3">Create an interactive 360° view of your vehicle</p>
                      <button class="w-full bg-primary text-white py-2 rounded-md hover:bg-primary-dark transition-colors opacity-50 cursor-not-allowed" disabled>
                        Apply (Coming Soon)
                      </button>
                    </div>
                     <div class="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors opacity-50"> <!-- Example: Disabled look -->
                      <h4 class="font-medium text-gray-800 mb-2">Ai Video</h4>
                      <p class="text-sm text-gray-600 mb-3">Generate a promotional video of your vehicle</p>
                      <button class="w-full bg-primary text-white py-2 rounded-md hover:bg-primary-dark transition-colors opacity-50 cursor-not-allowed" disabled>
                        Apply (Coming Soon)
                      </button>
                    </div>
                     <!-- Error Message Display with Retry Button -->
                     <div v-if="errorMessage" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
                       <p class="mb-2"><strong>Error:</strong> {{ errorMessage }}</p>
                       <div v-if="lastFailedOperation" class="mt-3 flex justify-end">
                         <button
                           @click="retryLastOperation"
                           class="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
                         >
                           Retry Operation
                         </button>
                       </div>
                     </div>
                     
                     <!-- Processing Status Display -->
                     <div v-if="isProcessing && processingStatus" class="mt-4 p-3 bg-blue-50 border border-blue-200 text-blue-700 rounded-md text-sm">
                       <p><strong>Status:</strong> {{ processingStatus }}</p>
                       <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                         <div class="bg-blue-600 h-2.5 rounded-full animate-pulse" style="width: 100%"></div>
                       </div>
                     </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Panel: Preview & Save -->
            <div class="lg:col-span-1 space-y-6">
              <div class="border rounded-lg overflow-hidden">
                <div class="bg-gray-50 px-4 py-2 border-b">
                  <h3 class="font-medium text-gray-700">Modified Image</h3>
                </div>
                <div class="p-4 space-y-4">
                  <!-- Modified Image Preview -->
                  <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg overflow-hidden shadow-lg flex items-center justify-center">
                    <!-- Show modified image if available -->
                    <img v-if="modifiedImageUrl" :src="modifiedImageUrl" alt="Modified Vehicle Image" class="object-contain w-full h-full"
                         @error="handleImageError" @load="handleImageLoad" />
                    <!-- Show placeholder if no modified image -->
                    <div v-else class="text-center text-gray-500 px-4">
                       <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto opacity-30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p class="mt-2 text-sm">
                        {{ isProcessing ? 'Generating preview...' : 'Apply an AI tool to see the result' }}
                      </p>
                    </div>
                  </div>

                  <!-- Save & Download Buttons -->
                  <div class="space-y-3 pt-4">
                    <button
                      @click="downloadModifiedImage"
                      :disabled="!modifiedImageUrl || isProcessing"
                      class="w-full bg-gray-800 text-white py-2 rounded-md hover:bg-gray-700 transition-colors flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                      </svg>
                      Download Image
                    </button>
                    <button
                      @click="saveToGallery"
                      :disabled="!modifiedImageUrl || isProcessing"
                      class="w-full bg-primary text-white py-2 rounded-md hover:bg-primary-dark transition-colors flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /> <!-- Checkmark icon -->
                         <!-- Or use a save icon:
                         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4V4m0 0l-3 3m3-3l3 3" /> -->
                      </svg>
                      Save to Vehicle Gallery
                    </button>

                  </div>
                </div>
              </div>

              <!-- Coming Soon Features -->
              <div class="border rounded-lg overflow-hidden bg-gray-50">
                <div class="p-4 text-center">
                  <h3 class="font-medium text-gray-700 mb-2">Coming Soon</h3>
                  <p class="text-sm text-gray-600">
                    More AI features are being developed and will be available soon!
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- No Vehicle Selected State -->
        <div v-else class="bg-white shadow overflow-hidden sm:rounded-lg mb-6 p-10 text-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No Vehicle Selected</h3>
          <p class="text-gray-600 mb-6">Please select a vehicle from the inventory to use the AI Studio</p>
          <button
            @click="goBack"
            class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Go to Inventory
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios'; // Import axios
import AdminNavbar from '../../components/admin/AdminNavbar.vue';
import vehicleStore from '../../store/vehicles';
import authStore from '../../store/supabaseAuth';

const router = useRouter();
const route = useRoute();
const isLoading = ref(true);
const isProcessing = ref(false); // New state for AI processing
const vehicle = ref(null);
const modifiedImageUrl = ref(null); // To store the result from the backend
const errorMessage = ref(null); // To store potential errors
const processingStatus = ref(''); // To show processing status messages
const lastFailedOperation = ref(null); // To track which operation failed for retry
const lastOperationPayload = ref(null); // To store the last operation payload for retry

// Background replacement options
const backgroundPrompt = ref('');
const lightDirection = ref('');
const lightStrength = ref(0.3);
const preserveSubject = ref(0.6);

// --- Backend API URL ---
// Make sure this matches where your backend is running
// Use the environment variable for the backend URL.
// It MUST start with VITE_ to be exposed to the client-side code by Vite.
// Add fallback to relative URL if VITE_BACKEND_URL is not defined
const BACKEND_API_URL = import.meta.env.VITE_BACKEND_URL || '';
console.log('Backend API URL:', BACKEND_API_URL);
const currentImageIndex = ref(0);

// --- Authentication & Initial Data Fetch ---
onMounted(async () => {
  // ... (your existing onMounted logic) ...
  try {
    isLoading.value = true;
    authStore.initAuth(); // Ensure auth is initialized before checking
    if (!authStore.isAuthenticated.value) {
      router.push('/admin/login');
      return;
    }
    const vehicleId = route.params.id;
    if (vehicleId) {
      const vehicleData = await vehicleStore.getVehicleById(vehicleId);
      if (vehicleData) {
        vehicle.value = vehicleData;
        console.log('Loaded vehicle for AI Studio:', vehicle.value.title);
      } else {
        console.error('Vehicle not found');
        errorMessage.value = 'Vehicle not found.';
      }
    } else {
       errorMessage.value = 'No vehicle ID provided in the route.';
    }
  } catch (error) {
    console.error('Failed to load vehicle data:', error);
    errorMessage.value = 'Failed to load vehicle data.';
  } finally {
    isLoading.value = false;
  }
});

// --- Image Handling ---
const vehicleImages = computed(() => {
  if (!vehicle.value) return [];

  // Check if gallery array exists
  if (Array.isArray(vehicle.value.gallery) && vehicle.value.gallery.length > 0) {
    return vehicle.value.gallery;
  }

  // Fallback to single image if gallery doesn't exist
  if (vehicle.value.image) {
    return [vehicle.value.image];
  }

  return [];
});

const currentImage = computed(() => {
  if (vehicleImages.value.length === 0) {
    // Return a placeholder image URL
    return '/favicon.svg';
  }
  return vehicleImages.value[currentImageIndex.value];
});

function nextImage() {
  if (currentImageIndex.value < vehicleImages.value.length - 1) {
    currentImageIndex.value++;
    modifiedImageUrl.value = null; // Clear previous result when image changes
    errorMessage.value = null;
  }
}

function prevImage() {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--;
    modifiedImageUrl.value = null; // Clear previous result when image changes
    errorMessage.value = null;
  }
}

// Reset image index and modified image when the vehicle changes
watch(() => vehicle.value, () => {
  currentImageIndex.value = 0;
  modifiedImageUrl.value = null;
  errorMessage.value = null;
}, { deep: true });

// --- AI Tool Functions ---

async function applyBackgroundRemoval() {
  if (!currentImage.value || currentImage.value === '/favicon.svg') {
    errorMessage.value = "Please select a valid image first.";
    return;
  }

  isProcessing.value = true;
  modifiedImageUrl.value = null; // Clear previous result
  errorMessage.value = null;     // Clear previous error
  processingStatus.value = "Preparing to remove background...";
  lastFailedOperation.value = null; // Reset retry state

  console.log(`Sending image URL to backend: ${currentImage.value}`);

  // Store operation info for potential retry
  const operationPayload = {
    imageUrl: currentImage.value,
  };
  lastOperationPayload.value = operationPayload;
  
  try {
    processingStatus.value = "Sending image to AI service...";
    // Use relative URL if BACKEND_API_URL is empty
    const apiUrl = BACKEND_API_URL ? `${BACKEND_API_URL}/api/ai/remove-background` : '/api/ai/remove-background';
    const response = await axios.post(apiUrl, operationPayload);

    if (response.data.success) {
      processingStatus.value = "Processing complete!";
      // Log the first 100 characters of the image data to see what format it's in
      console.log("Background removal - Image data preview:", response.data.imageData.substring(0, 100));

      modifiedImageUrl.value = response.data.imageData; // Store the base64 Data URL
      console.log("Background removal successful, received image data.");

      // Log the final URL format (first 100 chars)
      console.log("Background removal - Final modifiedImageUrl preview:", modifiedImageUrl.value.substring(0, 100));
      
      // Clear retry state on success
      lastFailedOperation.value = null;
    } else {
      errorMessage.value = `Error: ${response.data.error || 'Unknown error from backend.'}`;
      console.error("Background removal failed:", errorMessage.value);
      
      // Set retry state
      lastFailedOperation.value = 'backgroundRemoval';
    }
  } catch (error) {
    console.error('Error calling background removal API:', error);
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      errorMessage.value = `Server Error (${error.response.status}): ${error.response.data?.error || error.message}`;
    } else if (error.request) {
      // The request was made but no response was received
      errorMessage.value = 'Network Error: Could not reach backend server.';
    } else {
      // Something happened in setting up the request that triggered an Error
      errorMessage.value = `Request Error: ${error.message}`;
    }
    
    // Set retry state
    lastFailedOperation.value = 'backgroundRemoval';
  } finally {
    isProcessing.value = false;
    processingStatus.value = '';
  }
}

// Function to handle background replacement and relighting with polling
async function applyBackgroundReplacement() {
  if (!currentImage.value || currentImage.value === '/favicon.svg') {
    errorMessage.value = "Please select a valid image first.";
    return;
  }

  if (!backgroundPrompt.value) {
    errorMessage.value = "Please enter a background description.";
    return;
  }

  isProcessing.value = true;
  modifiedImageUrl.value = null; // Clear previous result
  errorMessage.value = null;     // Clear previous error
  processingStatus.value = "Preparing to change background...";
  lastFailedOperation.value = null; // Reset retry state

  console.log(`Sending image URL to backend for background replacement: ${currentImage.value}`);
  console.log(`Background prompt: ${backgroundPrompt.value}`);

  const payload = {
    imageUrl: currentImage.value,
    backgroundPrompt: backgroundPrompt.value,
    outputFormat: 'webp'
  };

  if (lightDirection.value) {
    payload.lightSourceDirection = lightDirection.value;
  }

  if (lightStrength.value !== undefined) {
    payload.lightSourceStrength = lightStrength.value;
  }

  if (preserveSubject.value !== undefined) {
    payload.preserveOriginalSubject = preserveSubject.value;
  }
  
  // Add a random seed for more consistent results between retries
  payload.seed = Math.floor(Math.random() * 4294967294);

  // Store operation info for potential retry
  lastOperationPayload.value = payload;

  // Track retries
  let retryCount = 0;
  const maxRetries = 3; // Increased from 2 to 3
  const retryDelays = [5000, 10000, 15000]; // Progressive backoff
  
  const attemptBackgroundReplacement = async () => {
    try {
      processingStatus.value = "Sending image to AI service...";
      // The backend handles polling internally
      // Use relative URL if BACKEND_API_URL is empty
      const apiUrl = BACKEND_API_URL ? `${BACKEND_API_URL}/api/ai/replace-background-and-relight` : '/api/ai/replace-background-and-relight';
      const response = await axios.post(apiUrl, payload, {
        // Add a longer timeout for this request since it involves polling
        timeout: 120000 // 2 minute timeout
      });

      // The backend returns the final result directly
      if (response.data.success) {
        processingStatus.value = "Processing complete!";
        modifiedImageUrl.value = response.data.imageData;
        console.log("Background replacement successful, image received directly from backend polling.");
        // Clear retry state on success
        lastFailedOperation.value = null;
        return true; // Success
      } else {
        // Check if this is an error that might benefit from a retry
        if (retryCount < maxRetries && (
            // 404 errors (job not found)
            response.data.details?.message?.includes('job could not be found') ||
            // Network errors or timeouts
            response.data.error?.includes('timeout') ||
            response.data.error?.includes('network') ||
            // Server errors
            response.data.status >= 500
          )) {
          processingStatus.value = `Received error (${response.data.status || 'unknown'}), will retry (${retryCount + 1}/${maxRetries})...`;
          console.log(`Received error, will retry (${retryCount + 1}/${maxRetries})...`, response.data);
          return false; // Signal for retry
        }
        
        // Format a more user-friendly error message
        let userMessage = `Error: ${response.data.error || 'Unknown error from backend.'}`;
        
        // Add troubleshooting tips for specific errors
        if (response.data.status === 404) {
          userMessage += "\n\nThe AI service couldn't find the requested job. This might be a temporary issue.";
        }
        
        errorMessage.value = userMessage;
        console.error("Background replacement failed:", errorMessage.value, response.data.details);
        
        // Set retry state
        lastFailedOperation.value = 'backgroundReplacement';
        
        return true; // Don't retry for other errors
      }
    } catch (error) {
      handleApiError(error, "background replacement");
      // Set retry state
      lastFailedOperation.value = 'backgroundReplacement';
      // Only retry on network errors, not on other types of errors
      return !(error.request && !error.response && retryCount < maxRetries);
    }
  };
  
  // Initial attempt
  let success = await attemptBackgroundReplacement();
  
  // Retry logic with progressive backoff
  while (!success && retryCount < maxRetries) {
    retryCount++;
    const retryDelay = retryDelays[retryCount - 1] || 15000;
    const retryMessage = `Retrying background replacement (${retryCount}/${maxRetries}) in ${retryDelay/1000} seconds...`;
    console.log(retryMessage);
    processingStatus.value = retryMessage;
    
    // Wait before retrying with progressive backoff
    await new Promise(resolve => setTimeout(resolve, retryDelay));
    
    // Update status before retry
    processingStatus.value = `Executing retry attempt ${retryCount}/${maxRetries}...`;
    success = await attemptBackgroundReplacement();
  }
  
  isProcessing.value = false;
  processingStatus.value = '';
}

// Enhanced error handling function for API calls
function handleApiError(error, processName) {
  console.error(`Error calling ${processName} API:`, error);
  
  // Detailed error logging for debugging
  if (error.response) {
    console.error(`Response status:`, error.response.status);
    console.error(`Response headers:`, error.response.headers);
    console.error(`Response data:`, error.response.data);
  } else if (error.request) {
    console.error(`Request error:`, error.request);
  }
  
  // Format user-friendly error message
  let userMessage = '';
  
  if (error.response) {
    // Format a more user-friendly error message based on status code
    switch (error.response.status) {
      case 400:
        userMessage = `Invalid request: ${error.response.data?.error || 'The request was not formatted correctly.'}`;
        break;
      case 401:
        userMessage = `Authentication error: The API key may be invalid or expired.`;
        break;
      case 404:
        userMessage = `The AI service couldn't find the requested resource. This might be a temporary issue.`;
        break;
      case 413:
        userMessage = `The image is too large. Please try a smaller image.`;
        break;
      case 429:
        userMessage = `The AI service is currently busy. Please try again in a few moments.`;
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        userMessage = `The AI service is experiencing technical difficulties. Please try again later.`;
        break;
      default:
        userMessage = `Server Error (${error.response.status}): ${error.response.data?.error || error.message}`;
    }
  } else if (error.request) {
    if (error.code === 'ECONNABORTED') {
      userMessage = `Request timeout: The operation took too long to complete. Please try again.`;
    } else {
      userMessage = `Network Error: Could not reach the backend server. Please check your internet connection.`;
    }
  } else {
    userMessage = `Request Error: ${error.message}`;
  }
  
  // Add troubleshooting tips
  userMessage += `\n\nTroubleshooting tips:
  • Try refreshing the page
  • Check your internet connection
  • Try a different image
  • Try a simpler background prompt`;
  
  errorMessage.value = userMessage;
}



// Function to retry the last failed operation
async function retryLastOperation() {
  if (!lastFailedOperation.value || !lastOperationPayload.value) {
    errorMessage.value = "No operation available to retry.";
    return;
  }
  
  // Clear error message
  errorMessage.value = null;
  
  console.log(`Retrying last operation: ${lastFailedOperation.value}`);
  
  if (lastFailedOperation.value === 'backgroundRemoval') {
    await applyBackgroundRemoval();
  } else if (lastFailedOperation.value === 'backgroundReplacement') {
    await applyBackgroundReplacement();
  } else {
    console.error(`Unknown operation type: ${lastFailedOperation.value}`);
    errorMessage.value = "Cannot retry unknown operation type.";
  }
}

// --- Image handling functions ---
function handleImageError(event) {
  console.error("Error loading image:", event);
  errorMessage.value = "Error loading the modified image. The image data may be invalid.";
}

function handleImageLoad() {
  console.log("Image loaded successfully!");
}

// --- Helper for downloading Base64 ---
function downloadModifiedImage() {
  if (!modifiedImageUrl.value) return;

  const link = document.createElement('a');
  link.href = modifiedImageUrl.value;
  // Extract filename suggestion (optional)
  const originalFilename = currentImage.value.split('/').pop().split('.')[0] || 'image';
  link.download = `${originalFilename}_no_bg.webp`; // Set desired filename
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// --- TODO: Implement Save to Gallery ---
function saveToGallery() {
    if (!modifiedImageUrl.value) {
        alert("No modified image to save.");
        return;
    }
    // This function needs more logic:
    // 1. Convert the base64 Data URL back to a File/Blob object.
    // 2. Upload this File/Blob to your server/storage (e.g., using Firebase Storage, S3, or your own upload endpoint).
    // 3. Get the public URL of the newly uploaded image.
    // 4. Update the vehicle's gallery array in your database (e.g., via vehicleStore.updateVehicleGallery(vehicleId, newImageUrl)).
    // 5. Refresh the vehicle data or update the local `vehicle.value.gallery` array.
    console.warn("Save to Gallery functionality not fully implemented.");
    alert("Save to Gallery: Needs implementation to upload the image and update the vehicle record.");
}

// --- Navigation ---
function goBack() {
  router.push('/admin/dashboard');
}
</script>

<style scoped>
/* Add aspect ratio plugin if not already included in tailwind.config.js */
/* npm install -D @tailwindcss/aspect-ratio */
/* plugins: [require('@tailwindcss/aspect-ratio')] */
.aspect-w-16 { position: relative; padding-bottom: calc(9 / 16 * 100%); }
.aspect-h-9 { /* Covered by aspect-w-16 */ }
.aspect-w-16 > * { position: absolute; height: 100%; width: 100%; top: 0; right: 0; bottom: 0; left: 0; object-fit: contain; /* Ensure image fits */ }

/* Custom scrollbar for thumbnails */
div::-webkit-scrollbar {
  height: 6px;
}
div::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
div::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}
div::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Improve focus visibility for accessibility */
button:focus-visible {
  outline: 2px solid theme('colors.primary');
  outline-offset: 2px;
}
</style>