/**
 * Database implementation for GT Motorsports vehicle inventory
 * This file provides functions to interact with the database
 */
import schema from './schema';

// Use IndexedDB for client-side storage
const DB_NAME = 'GTMotorsportsDB';
const DB_VERSION = 1;

let db = null;

/**
 * Initialize the database
 * @returns {Promise} Promise that resolves when the database is ready
 */
export const initDatabase = () => {
  return new Promise((resolve, reject) => {
    if (db) {
      resolve(db);
      return;
    }

    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = (event) => {
      console.error('Database error:', event.target.error);
      reject(event.target.error);
    };

    request.onsuccess = (event) => {
      db = event.target.result;
      console.log('Database opened successfully');
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      
      // Create tables based on schema
      if (!db.objectStoreNames.contains('vehicles')) {
        const vehicleStore = db.createObjectStore('vehicles', { keyPath: 'id', autoIncrement: true });
        vehicleStore.createIndex('make', 'make', { unique: false });
        vehicleStore.createIndex('model', 'model', { unique: false });
        vehicleStore.createIndex('year', 'year', { unique: false });
        vehicleStore.createIndex('stockNumber', 'stockNumber', { unique: true });
        vehicleStore.createIndex('vin', 'vin', { unique: true });
      }

      if (!db.objectStoreNames.contains('vehicleDetails')) {
        const detailsStore = db.createObjectStore('vehicleDetails', { keyPath: 'id', autoIncrement: true });
        detailsStore.createIndex('vehicleId', 'vehicleId', { unique: true });
      }

      if (!db.objectStoreNames.contains('vehicleFeatures')) {
        const featuresStore = db.createObjectStore('vehicleFeatures', { keyPath: 'id', autoIncrement: true });
        featuresStore.createIndex('vehicleId', 'vehicleId', { unique: false });
      }

      if (!db.objectStoreNames.contains('vehicleHighlights')) {
        const highlightsStore = db.createObjectStore('vehicleHighlights', { keyPath: 'id', autoIncrement: true });
        highlightsStore.createIndex('vehicleId', 'vehicleId', { unique: false });
      }

      if (!db.objectStoreNames.contains('vehicleImages')) {
        const imagesStore = db.createObjectStore('vehicleImages', { keyPath: 'id', autoIncrement: true });
        imagesStore.createIndex('vehicleId', 'vehicleId', { unique: false });
        imagesStore.createIndex('isPrimary', 'isPrimary', { unique: false });
      }

      if (!db.objectStoreNames.contains('vehicleTypes')) {
        const typesStore = db.createObjectStore('vehicleTypes', { keyPath: 'id', autoIncrement: true });
        typesStore.createIndex('name', 'name', { unique: true });
      }

      if (!db.objectStoreNames.contains('vehicleTypeMap')) {
        const typeMapStore = db.createObjectStore('vehicleTypeMap', { keyPath: ['vehicleId', 'typeId'] });
        typeMapStore.createIndex('vehicleId', 'vehicleId', { unique: false });
        typeMapStore.createIndex('typeId', 'typeId', { unique: false });
      }

      if (!db.objectStoreNames.contains('motorcycleDetails')) {
        const motorcycleStore = db.createObjectStore('motorcycleDetails', { keyPath: 'vehicleId' });
      }

      if (!db.objectStoreNames.contains('boatDetails')) {
        const boatStore = db.createObjectStore('boatDetails', { keyPath: 'vehicleId' });
      }

      if (!db.objectStoreNames.contains('atvDetails')) {
        const atvStore = db.createObjectStore('atvDetails', { keyPath: 'vehicleId' });
      }

      console.log('Database schema created');
    };
  });
};

/**
 * Add a vehicle to the database
 * @param {Object} vehicleData - The vehicle data to add
 * @returns {Promise} Promise that resolves with the new vehicle ID
 */
export const addVehicle = async (vehicleData) => {
  await initDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([
      'vehicles', 
      'vehicleDetails', 
      'vehicleFeatures', 
      'vehicleHighlights', 
      'vehicleImages'
    ], 'readwrite');
    
    transaction.onerror = (event) => {
      console.error('Transaction error:', event.target.error);
      reject(event.target.error);
    };
    
    // Extract data for different tables
    const {
      // Core vehicle data
      title, year, make, model, trim, price, specialPrice, stockNumber, vin, description,
      
      // Vehicle details
      bodyStyle, doors, engine, engineSize, drivetrain, transmission, 
      exteriorColor, interiorColor, passengers, fuelType, cityFuel, hwyFuel, mileage,
      
      // Arrays
      features = [], highlights = [], gallery = [],
      
      // Special vehicle type details
      vehicleType, typeSpecificDetails = {}
    } = vehicleData;
    
    // Add to vehicles table
    const vehicleStore = transaction.objectStore('vehicles');
    const vehicleRequest = vehicleStore.add({
      title, 
      year, 
      make, 
      model, 
      trim, 
      price, 
      specialPrice, 
      stockNumber, 
      vin, 
      description,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    vehicleRequest.onsuccess = (event) => {
      const vehicleId = event.target.result;
      
      // Add to vehicleDetails table
      const detailsStore = transaction.objectStore('vehicleDetails');
      detailsStore.add({
        vehicleId,
        bodyStyle, 
        doors, 
        engine, 
        engineSize, 
        drivetrain, 
        transmission, 
        exteriorColor, 
        interiorColor, 
        passengers, 
        fuelType, 
        cityFuel, 
        hwyFuel, 
        mileage,
        odometer: mileage,
        odometerUnit: 'km'
      });
      
      // Add features
      const featuresStore = transaction.objectStore('vehicleFeatures');
      features.forEach(feature => {
        featuresStore.add({
          vehicleId,
          feature
        });
      });
      
      // Add highlights
      const highlightsStore = transaction.objectStore('vehicleHighlights');
      highlights.forEach(highlight => {
        highlightsStore.add({
          vehicleId,
          highlight
        });
      });
      
      // Add images
      const imagesStore = transaction.objectStore('vehicleImages');
      gallery.forEach((url, index) => {
        imagesStore.add({
          vehicleId,
          url,
          isPrimary: index === 0, // First image is primary
          sortOrder: index
        });
      });
      
      // Handle vehicle type specific details if provided
      if (vehicleType) {
        handleVehicleTypeDetails(transaction, vehicleId, vehicleType, typeSpecificDetails);
      }
      
      resolve(vehicleId);
    };
  });
};

/**
 * Handle vehicle type specific details
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @param {string} vehicleType - The vehicle type (motorcycle, boat, atv)
 * @param {Object} details - The type-specific details
 */
const handleVehicleTypeDetails = (transaction, vehicleId, vehicleType, details) => {
  // First ensure the vehicle type exists
  const typesStore = transaction.objectStore('vehicleTypes');
  const typeRequest = typesStore.index('name').get(vehicleType);
  
  typeRequest.onsuccess = (event) => {
    let typeId;
    
    if (!event.target.result) {
      // Type doesn't exist, create it
      const newType = {
        name: vehicleType,
        description: `${vehicleType.charAt(0).toUpperCase() + vehicleType.slice(1)} vehicle type`
      };
      
      const addTypeRequest = typesStore.add(newType);
      addTypeRequest.onsuccess = (event) => {
        typeId = event.target.result;
        mapVehicleToType(transaction, vehicleId, typeId);
        addTypeSpecificDetails(transaction, vehicleId, vehicleType, details);
      };
    } else {
      // Type exists, use it
      typeId = event.target.result.id;
      mapVehicleToType(transaction, vehicleId, typeId);
      addTypeSpecificDetails(transaction, vehicleId, vehicleType, details);
    }
  };
};

/**
 * Map a vehicle to a type
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @param {number} typeId - The type ID
 */
const mapVehicleToType = (transaction, vehicleId, typeId) => {
  const typeMapStore = transaction.objectStore('vehicleTypeMap');
  typeMapStore.add({
    vehicleId,
    typeId
  });
};

/**
 * Add type-specific details for a vehicle
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @param {string} vehicleType - The vehicle type
 * @param {Object} details - The type-specific details
 */
const addTypeSpecificDetails = (transaction, vehicleId, vehicleType, details) => {
  switch (vehicleType.toLowerCase()) {
    case 'motorcycle':
      const motorcycleStore = transaction.objectStore('motorcycleDetails');
      motorcycleStore.add({
        vehicleId,
        ...details
      });
      break;
    case 'boat':
      const boatStore = transaction.objectStore('boatDetails');
      boatStore.add({
        vehicleId,
        ...details
      });
      break;
    case 'atv':
      const atvStore = transaction.objectStore('atvDetails');
      atvStore.add({
        vehicleId,
        ...details
      });
      break;
  }
};

/**
 * Get all vehicles from the database
 * @returns {Promise} Promise that resolves with an array of vehicles
 */
export const getAllVehicles = async () => {
  await initDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['vehicles'], 'readonly');
    const vehicleStore = transaction.objectStore('vehicles');
    const request = vehicleStore.getAll();
    
    request.onerror = (event) => {
      console.error('Error getting vehicles:', event.target.error);
      reject(event.target.error);
    };
    
    request.onsuccess = (event) => {
      const vehicles = event.target.result;
      resolve(vehicles);
    };
  });
};

/**
 * Get a vehicle by ID with all related data
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle data
 */
export const getVehicleById = async (id) => {
  await initDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([
      'vehicles', 
      'vehicleDetails', 
      'vehicleFeatures', 
      'vehicleHighlights', 
      'vehicleImages',
      'vehicleTypeMap',
      'vehicleTypes',
      'motorcycleDetails',
      'boatDetails',
      'atvDetails'
    ], 'readonly');
    
    const vehicleStore = transaction.objectStore('vehicles');
    const request = vehicleStore.get(id);
    
    request.onerror = (event) => {
      console.error('Error getting vehicle:', event.target.error);
      reject(event.target.error);
    };
    
    request.onsuccess = async (event) => {
      const vehicle = event.target.result;
      
      if (!vehicle) {
        resolve(null);
        return;
      }
      
      // Get vehicle details
      const details = await getVehicleDetails(transaction, id);
      
      // Get features
      const features = await getVehicleFeatures(transaction, id);
      
      // Get highlights
      const highlights = await getVehicleHighlights(transaction, id);
      
      // Get images
      const images = await getVehicleImages(transaction, id);
      
      // Get vehicle type
      const vehicleType = await getVehicleType(transaction, id);
      
      // Get type-specific details if applicable
      let typeSpecificDetails = null;
      if (vehicleType) {
        typeSpecificDetails = await getTypeSpecificDetails(transaction, id, vehicleType.name);
      }
      
      // Combine all data
      const completeVehicle = {
        ...vehicle,
        ...details,
        features: features.map(f => f.feature),
        highlights: highlights.map(h => h.highlight),
        gallery: images.map(i => i.url),
        image: images.find(i => i.isPrimary)?.url || images[0]?.url || '',
        vehicleType: vehicleType?.name || null,
        typeSpecificDetails
      };
      
      resolve(completeVehicle);
    };
  });
};

/**
 * Get vehicle details
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle details
 */
const getVehicleDetails = (transaction, vehicleId) => {
  return new Promise((resolve) => {
    const detailsStore = transaction.objectStore('vehicleDetails');
    const request = detailsStore.index('vehicleId').get(vehicleId);
    
    request.onsuccess = (event) => {
      resolve(event.target.result || {});
    };
    
    request.onerror = () => {
      resolve({});
    };
  });
};

/**
 * Get vehicle features
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle features
 */
const getVehicleFeatures = (transaction, vehicleId) => {
  return new Promise((resolve) => {
    const featuresStore = transaction.objectStore('vehicleFeatures');
    const request = featuresStore.index('vehicleId').getAll(vehicleId);
    
    request.onsuccess = (event) => {
      resolve(event.target.result || []);
    };
    
    request.onerror = () => {
      resolve([]);
    };
  });
};

/**
 * Get vehicle highlights
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle highlights
 */
const getVehicleHighlights = (transaction, vehicleId) => {
  return new Promise((resolve) => {
    const highlightsStore = transaction.objectStore('vehicleHighlights');
    const request = highlightsStore.index('vehicleId').getAll(vehicleId);
    
    request.onsuccess = (event) => {
      resolve(event.target.result || []);
    };
    
    request.onerror = () => {
      resolve([]);
    };
  });
};

/**
 * Get vehicle images
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle images
 */
const getVehicleImages = (transaction, vehicleId) => {
  return new Promise((resolve) => {
    const imagesStore = transaction.objectStore('vehicleImages');
    const request = imagesStore.index('vehicleId').getAll(vehicleId);
    
    request.onsuccess = (event) => {
      const images = event.target.result || [];
      // Sort by sortOrder
      images.sort((a, b) => a.sortOrder - b.sortOrder);
      resolve(images);
    };
    
    request.onerror = () => {
      resolve([]);
    };
  });
};

/**
 * Get vehicle type
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle type
 */
const getVehicleType = async (transaction, vehicleId) => {
  return new Promise((resolve) => {
    const typeMapStore = transaction.objectStore('vehicleTypeMap');
    const request = typeMapStore.index('vehicleId').getAll(vehicleId);
    
    request.onsuccess = (event) => {
      const typeMaps = event.target.result || [];
      
      if (typeMaps.length === 0) {
        resolve(null);
        return;
      }
      
      const typeId = typeMaps[0].typeId;
      const typesStore = transaction.objectStore('vehicleTypes');
      const typeRequest = typesStore.get(typeId);
      
      typeRequest.onsuccess = (event) => {
        resolve(event.target.result);
      };
      
      typeRequest.onerror = () => {
        resolve(null);
      };
    };
    
    request.onerror = () => {
      resolve(null);
    };
  });
};

/**
 * Get type-specific details
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @param {string} typeName - The vehicle type name
 * @returns {Promise} Promise that resolves with the type-specific details
 */
const getTypeSpecificDetails = (transaction, vehicleId, typeName) => {
  return new Promise((resolve) => {
    let storeName;
    
    switch (typeName.toLowerCase()) {
      case 'motorcycle':
        storeName = 'motorcycleDetails';
        break;
      case 'boat':
        storeName = 'boatDetails';
        break;
      case 'atv':
        storeName = 'atvDetails';
        break;
      default:
        resolve(null);
        return;
    }
    
    const store = transaction.objectStore(storeName);
    const request = store.get(vehicleId);
    
    request.onsuccess = (event) => {
      resolve(event.target.result || null);
    };
    
    request.onerror = () => {
      resolve(null);
    };
  });
};

/**
 * Update a vehicle in the database
 * @param {number} id - The vehicle ID
 * @param {Object} vehicleData - The updated vehicle data
 * @returns {Promise} Promise that resolves when the update is complete
 */
export const updateVehicle = async (id, vehicleData) => {
  await initDatabase();
  
  // First get the existing vehicle to compare changes
  const existingVehicle = await getVehicleById(id);
  
  if (!existingVehicle) {
    throw new Error(`Vehicle with ID ${id} not found`);
  }
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([
      'vehicles', 
      'vehicleDetails', 
      'vehicleFeatures', 
      'vehicleHighlights', 
      'vehicleImages',
      'vehicleTypeMap',
      'vehicleTypes',
      'motorcycleDetails',
      'boatDetails',
      'atvDetails'
    ], 'readwrite');
    
    transaction.onerror = (event) => {
      console.error('Transaction error:', event.target.error);
      reject(event.target.error);
    };
    
    // Extract data for different tables
    const {
      // Core vehicle data
      title, year, make, model, trim, price, specialPrice, stockNumber, vin, description,
      
      // Vehicle details
      bodyStyle, doors, engine, engineSize, drivetrain, transmission, 
      exteriorColor, interiorColor, passengers, fuelType, cityFuel, hwyFuel, mileage,
      
      // Arrays
      features = [], highlights = [], gallery = [],
      
      // Special vehicle type details
      vehicleType, typeSpecificDetails = {}
    } = vehicleData;
    
    // Update vehicles table
    const vehicleStore = transaction.objectStore('vehicles');
    const vehicleRequest = vehicleStore.get(id);
    
    vehicleRequest.onsuccess = (event) => {
      const vehicle = event.target.result;
      
      if (!vehicle) {
        reject(new Error(`Vehicle with ID ${id} not found`));
        return;
      }
      
      // Update vehicle data
      vehicle.title = title || vehicle.title;
      vehicle.year = year || vehicle.year;
      vehicle.make = make || vehicle.make;
      vehicle.model = model || vehicle.model;
      vehicle.trim = trim !== undefined ? trim : vehicle.trim;
      vehicle.price = price !== undefined ? price : vehicle.price;
      vehicle.specialPrice = specialPrice !== undefined ? specialPrice : vehicle.specialPrice;
      vehicle.stockNumber = stockNumber || vehicle.stockNumber;
      vehicle.vin = vin || vehicle.vin;
      vehicle.description = description || vehicle.description;
      vehicle.updatedAt = new Date();
      
      // Put updated vehicle back
      vehicleStore.put(vehicle);
      
      // Update vehicle details
      updateVehicleDetails(transaction, id, {
        bodyStyle, doors, engine, engineSize, drivetrain, transmission, 
        exteriorColor, interiorColor, passengers, fuelType, cityFuel, hwyFuel, mileage
      });
      
      // Update features (delete all and re-add)
      if (features && features.length > 0) {
        updateVehicleFeatures(transaction, id, features);
      }
      
      // Update highlights (delete all and re-add)
      if (highlights && highlights.length > 0) {
        updateVehicleHighlights(transaction, id, highlights);
      }
      
      // Update images (delete all and re-add)
      if (gallery && gallery.length > 0) {
        updateVehicleImages(transaction, id, gallery);
      }
      
      // Update vehicle type if changed
      if (vehicleType && vehicleType !== existingVehicle.vehicleType) {
        updateVehicleType(transaction, id, vehicleType, typeSpecificDetails);
      } else if (typeSpecificDetails && Object.keys(typeSpecificDetails).length > 0) {
        // Just update the type-specific details
        updateTypeSpecificDetails(transaction, id, existingVehicle.vehicleType, typeSpecificDetails);
      }
      
      resolve(id);
    };
  });
};

/**
 * Update vehicle details
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @param {Object} details - The updated details
 */
const updateVehicleDetails = (transaction, vehicleId, details) => {
  const detailsStore = transaction.objectStore('vehicleDetails');
  const request = detailsStore.index('vehicleId').get(vehicleId);
  
  request.onsuccess = (event) => {
    const existingDetails = event.target.result;
    
    if (existingDetails) {
      // Update existing details
      Object.keys(details).forEach(key => {
        if (details[key] !== undefined) {
          existingDetails[key] = details[key];
        }
      });
      
      detailsStore.put(existingDetails);
    } else {
      // Create new details
      detailsStore.add({
        vehicleId,
        ...details
      });
    }
  };
};

/**
 * Update vehicle features
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @param {Array} features - The updated features
 */
const updateVehicleFeatures = (transaction, vehicleId, features) => {
  const featuresStore = transaction.objectStore('vehicleFeatures');
  const request = featuresStore.index('vehicleId').getAll(vehicleId);
  
  request.onsuccess = (event) => {
    const existingFeatures = event.target.result;
    
    // Delete existing features
    existingFeatures.forEach(feature => {
      featuresStore.delete(feature.id);
    });
    
    // Add new features
    features.forEach(feature => {
      featuresStore.add({
        vehicleId,
        feature
      });
    });
  };
};

/**
 * Update vehicle highlights
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @param {Array} highlights - The updated highlights
 */
const updateVehicleHighlights = (transaction, vehicleId, highlights) => {
  const highlightsStore = transaction.objectStore('vehicleHighlights');
  const request = highlightsStore.index('vehicleId').getAll(vehicleId);
  
  request.onsuccess = (event) => {
    const existingHighlights = event.target.result;
    
    // Delete existing highlights
    existingHighlights.forEach(highlight => {
      highlightsStore.delete(highlight.id);
    });
    
    // Add new highlights
    highlights.forEach(highlight => {
      highlightsStore.add({
        vehicleId,
        highlight
      });
    });
  };
};

/**
 * Update vehicle images
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @param {Array} gallery - The updated images
 */
const updateVehicleImages = (transaction, vehicleId, gallery) => {
  const imagesStore = transaction.objectStore('vehicleImages');
  const request = imagesStore.index('vehicleId').getAll(vehicleId);
  
  request.onsuccess = (event) => {
    const existingImages = event.target.result;
    
    // Delete existing images
    existingImages.forEach(image => {
      imagesStore.delete(image.id);
    });
    
    // Add new images
    gallery.forEach((url, index) => {
      imagesStore.add({
        vehicleId,
        url,
        isPrimary: index === 0, // First image is primary
        sortOrder: index
      });
    });
  };
};

/**
 * Update vehicle type
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @param {string} vehicleType - The new vehicle type
 * @param {Object} typeSpecificDetails - The type-specific details
 */
const updateVehicleType = (transaction, vehicleId, vehicleType, typeSpecificDetails) => {
  // First delete existing type mappings
  const typeMapStore = transaction.objectStore('vehicleTypeMap');
  const request = typeMapStore.index('vehicleId').getAll(vehicleId);
  
  request.onsuccess = (event) => {
    const existingMappings = event.target.result;
    
    // Delete existing mappings
    existingMappings.forEach(mapping => {
      typeMapStore.delete([mapping.vehicleId, mapping.typeId]);
    });
    
    // Add new type mapping
    handleVehicleTypeDetails(transaction, vehicleId, vehicleType, typeSpecificDetails);
  };
};

/**
 * Update type-specific details
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 * @param {string} vehicleType - The vehicle type
 * @param {Object} details - The updated details
 */
const updateTypeSpecificDetails = (transaction, vehicleId, vehicleType, details) => {
  if (!vehicleType) return;
  
  let storeName;
  
  switch (vehicleType.toLowerCase()) {
    case 'motorcycle':
      storeName = 'motorcycleDetails';
      break;
    case 'boat':
      storeName = 'boatDetails';
      break;
    case 'atv':
      storeName = 'atvDetails';
      break;
    default:
      return;
  }
  
  const store = transaction.objectStore(storeName);
  const request = store.get(vehicleId);
  
  request.onsuccess = (event) => {
    const existingDetails = event.target.result;
    
    if (existingDetails) {
      // Update existing details
      Object.keys(details).forEach(key => {
        if (details[key] !== undefined) {
          existingDetails[key] = details[key];
        }
      });
      
      store.put(existingDetails);
    } else {
      // Create new details
      store.add({
        vehicleId,
        ...details
      });
    }
  };
};

/**
 * Delete a vehicle from the database
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves when the delete is complete
 */
export const deleteVehicle = async (id) => {
  await initDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([
      'vehicles', 
      'vehicleDetails', 
      'vehicleFeatures', 
      'vehicleHighlights', 
      'vehicleImages',
      'vehicleTypeMap',
      'motorcycleDetails',
      'boatDetails',
      'atvDetails'
    ], 'readwrite');
    
    transaction.onerror = (event) => {
      console.error('Transaction error:', event.target.error);
      reject(event.target.error);
    };
    
    // Delete from vehicles table (cascade will handle the rest)
    const vehicleStore = transaction.objectStore('vehicles');
    const request = vehicleStore.delete(id);
    
    request.onsuccess = () => {
      // Delete from related tables
      deleteRelatedData(transaction, id);
      resolve(true);
    };
    
    request.onerror = (event) => {
      console.error('Error deleting vehicle:', event.target.error);
      reject(event.target.error);
    };
  });
};

/**
 * Delete related data for a vehicle
 * @param {IDBTransaction} transaction - The current transaction
 * @param {number} vehicleId - The vehicle ID
 */
const deleteRelatedData = (transaction, vehicleId) => {
  // Delete from vehicleDetails
  const detailsStore = transaction.objectStore('vehicleDetails');
  const detailsRequest = detailsStore.index('vehicleId').getAll(vehicleId);
  
  detailsRequest.onsuccess = (event) => {
    const details = event.target.result;
    details.forEach(detail => {
      detailsStore.delete(detail.id);
    });
  };
  
  // Delete from vehicleFeatures
  const featuresStore = transaction.objectStore('vehicleFeatures');
  const featuresRequest = featuresStore.index('vehicleId').getAll(vehicleId);
  
  featuresRequest.onsuccess = (event) => {
    const features = event.target.result;
    features.forEach(feature => {
      featuresStore.delete(feature.id);
    });
  };
  
  // Delete from vehicleHighlights
  const highlightsStore = transaction.objectStore('vehicleHighlights');
  const highlightsRequest = highlightsStore.index('vehicleId').getAll(vehicleId);
  
  highlightsRequest.onsuccess = (event) => {
    const highlights = event.target.result;
    highlights.forEach(highlight => {
      highlightsStore.delete(highlight.id);
    });
  };
  
  // Delete from vehicleImages
  const imagesStore = transaction.objectStore('vehicleImages');
  const imagesRequest = imagesStore.index('vehicleId').getAll(vehicleId);
  
  imagesRequest.onsuccess = (event) => {
    const images = event.target.result;
    images.forEach(image => {
      imagesStore.delete(image.id);
    });
  };
  
  // Delete from vehicleTypeMap
  const typeMapStore = transaction.objectStore('vehicleTypeMap');
  const typeMapRequest = typeMapStore.index('vehicleId').getAll(vehicleId);
  
  typeMapRequest.onsuccess = (event) => {
    const typeMaps = event.target.result;
    typeMaps.forEach(typeMap => {
      typeMapStore.delete([typeMap.vehicleId, typeMap.typeId]);
    });
  };
  
  // Delete from type-specific tables
  const motorcycleStore = transaction.objectStore('motorcycleDetails');
  motorcycleStore.delete(vehicleId);
  
  const boatStore = transaction.objectStore('boatDetails');
  boatStore.delete(vehicleId);
  
  const atvStore = transaction.objectStore('atvDetails');
  atvStore.delete(vehicleId);
};

export default {
  initDatabase,
  addVehicle,
  getAllVehicles,
  getVehicleById,
  updateVehicle,
  deleteVehicle
};