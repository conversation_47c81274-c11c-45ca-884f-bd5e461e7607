// Supabase Edge Function: handle-new-user
// This function is triggered after a new user is created in auth.users
// It implements Option A multi-tenancy by:
// 1. Creating a tenant
// 2. Creating a public user profile
// 3. Updating the auth user's app_metadata with the tenant_id

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// Define interfaces for better type safety
interface AuthUser {
  id: string;
  email: string;
  raw_user_meta_data?: {
    full_name?: string;
  };
}

interface Tenant {
  id: string;
  name: string;
  created_at?: string;
}

interface PublicUser {
  id: string;
  tenant_id: string;
  role_id: number;
  full_name: string;
  email: string;
}

serve(async (req: Request) => {
  try {
    // Parse the request body to get the new auth.users record
    const { record: authUser } = await req.json();

    // Basic validation
    if (!authUser || !authUser.id || !authUser.email) {
      console.error("Invalid auth user data received:", authUser);
      return new Response(
        JSON.stringify({ error: "Invalid auth user data" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    console.log(`Processing new user: ${authUser.id} (${authUser.email})`);

    // Initialize admin Supabase client with service role key
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceRoleKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

    if (!supabaseUrl || !supabaseServiceRoleKey) {
      throw new Error("Missing Supabase environment variables");
    }

    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

    // Step 1: Create a new tenant
    const tenantName = `Dealership for ${authUser.email}`;
    const { data: newTenant, error: tenantError } = await supabase
      .from("tenants")
      .insert({ name: tenantName })
      .select("id")
      .single();

    if (tenantError || !newTenant) {
      console.error("Error creating tenant:", tenantError);
      throw new Error(`Failed to create tenant: ${tenantError?.message}`);
    }

    const newTenantId = newTenant.id;
    console.log(`Created new tenant: ${newTenantId} (${tenantName})`);

    // Step 2: Create a public user profile
    const fullName = authUser.raw_user_meta_data?.full_name || authUser.email;
    const publicUser: Omit<PublicUser, "created_at"> = {
      id: authUser.id,
      tenant_id: newTenantId,
      role_id: 1, // Default administrator role ID
      full_name: fullName,
      email: authUser.email,
    };

    const { error: userError } = await supabase
      .from("users")
      .insert(publicUser);

    if (userError) {
      console.error("Error creating public user:", userError);
      throw new Error(`Failed to create public user: ${userError.message}`);
    }

    console.log(`Created public user profile for: ${authUser.id}`);

    // Step 3: Update the auth user's app_metadata with the tenant_id
    const { error: updateError } = await supabase.auth.admin.updateUserById(
      authUser.id,
      {
        app_metadata: { tenant_id: newTenantId },
      }
    );

    if (updateError) {
      console.error("Error updating user metadata:", updateError);
      throw new Error(`Failed to update user metadata: ${updateError.message}`);
    }

    console.log(`Updated app_metadata for user: ${authUser.id}`);

    // Return success response
    return new Response(
      JSON.stringify({ success: true, tenantId: newTenantId }),
      { status: 200, headers: { "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Error in handle-new-user function:", error);
    return new Response(
      JSON.stringify({ error: error.message || "Unknown error occurred" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
});