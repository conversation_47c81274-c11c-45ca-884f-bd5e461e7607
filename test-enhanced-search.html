<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Search Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .search-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .results {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>Enhanced Search System Test</h1>
    <p>This page tests the enhanced search functionality with various search terms including typos, partial matches, and intelligent field matching.</p>

    <div class="test-case">
        <h3>Test Case 1: Basic Make Search</h3>
        <p>Should find Ford vehicles</p>
        <input type="text" class="search-input" value="Ford" id="test1">
        <button class="test-button" onclick="testSearch('test1', 'Basic make search')">Test</button>
        <div id="result1" class="results"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 2: Year Search</h3>
        <p>Should find 2023 vehicles</p>
        <input type="text" class="search-input" value="2023" id="test2">
        <button class="test-button" onclick="testSearch('test2', 'Year search')">Test</button>
        <div id="result2" class="results"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 3: Typo in Make</h3>
        <p>Should find Toyota vehicles despite typo</p>
        <input type="text" class="search-input" value="toyata" id="test3">
        <button class="test-button" onclick="testSearch('test3', 'Typo correction')">Test</button>
        <div id="result3" class="results"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 4: Abbreviation</h3>
        <p>Should find SUV vehicles</p>
        <input type="text" class="search-input" value="suv" id="test4">
        <button class="test-button" onclick="testSearch('test4', 'Abbreviation search')">Test</button>
        <div id="result4" class="results"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 5: Color Search</h3>
        <p>Should find white vehicles</p>
        <input type="text" class="search-input" value="white" id="test5">
        <button class="test-button" onclick="testSearch('test5', 'Color search')">Test</button>
        <div id="result5" class="results"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 6: Multiple Words</h3>
        <p>Should find 2023 Ford vehicles</p>
        <input type="text" class="search-input" value="2023 ford" id="test6">
        <button class="test-button" onclick="testSearch('test6', 'Multiple words')">Test</button>
        <div id="result6" class="results"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 7: Transmission Type</h3>
        <p>Should find automatic transmission vehicles</p>
        <input type="text" class="search-input" value="automatic" id="test7">
        <button class="test-button" onclick="testSearch('test7', 'Transmission search')">Test</button>
        <div id="result7" class="results"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 8: Fuel Type</h3>
        <p>Should find gasoline vehicles</p>
        <input type="text" class="search-input" value="gas" id="test8">
        <button class="test-button" onclick="testSearch('test8', 'Fuel type search')">Test</button>
        <div id="result8" class="results"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 9: Partial Model</h3>
        <p>Should find Camry vehicles</p>
        <input type="text" class="search-input" value="cam" id="test9">
        <button class="test-button" onclick="testSearch('test9', 'Partial model search')">Test</button>
        <div id="result9" class="results"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 10: Complex Search</h3>
        <p>Should find red SUV vehicles</p>
        <input type="text" class="search-input" value="red suv" id="test10">
        <button class="test-button" onclick="testSearch('test10', 'Complex search')">Test</button>
        <div id="result10" class="results"></div>
    </div>

    <script>
        // Mock search function to test the enhanced search logic
        function testSearch(inputId, testName) {
            const input = document.getElementById(inputId);
            const resultDiv = document.getElementById('result' + inputId.replace('test', ''));
            const searchTerm = input.value;

            resultDiv.innerHTML = `<p>Testing "${searchTerm}" for ${testName}...</p>`;

            // Simulate the enhanced search conditions
            const conditions = buildEnhancedSearchConditions(searchTerm);
            
            resultDiv.innerHTML = `
                <p class="success">✓ Generated ${conditions.length} search conditions</p>
                <p><strong>Search term:</strong> "${searchTerm}"</p>
                <p><strong>Conditions generated:</strong> ${conditions.length}</p>
                <details>
                    <summary>View conditions (first 10)</summary>
                    <pre>${conditions.slice(0, 10).join('\n')}</pre>
                </details>
            `;
        }

        // Simplified version of the enhanced search function for testing
        function buildEnhancedSearchConditions(searchTerm) {
            const conditions = [];
            const normalizedTerm = searchTerm.toLowerCase().trim();
            
            // Handle multiple words
            const words = normalizedTerm.split(/\s+/).filter(word => word.length > 0);
            
            words.forEach(word => {
                const isNumeric = /^\d+$/.test(word);
                
                // Core text fields
                const textFields = ['make', 'model', 'trim', 'stockNumber', 'vin'];
                textFields.forEach(field => {
                    conditions.push(`${field}.ilike.%${word}%`);
                });
                
                // Extended fields
                const extendedFields = ['bodyStyle', 'engine', 'transmission', 'fuelType', 'exteriorColor', 'interiorColor', 'driveTrain'];
                extendedFields.forEach(field => {
                    conditions.push(`${field}.ilike.%${word}%`);
                });
                
                // Numeric handling
                if (isNumeric) {
                    const numericValue = parseInt(word, 10);
                    if (word.length === 4 && numericValue >= 1900 && numericValue <= new Date().getFullYear() + 2) {
                        conditions.push(`year.eq.${numericValue}`);
                    }
                }
            });
            
            // Add synonyms
            const synonymMap = {
                'suv': ['SUV', 'Sport Utility', 'Crossover'],
                'truck': ['Pickup Truck', 'Pickup'],
                'van': ['VAN', 'Minivan'],
                'auto': ['Automatic', 'Auto'],
                'gas': ['Gasoline', 'Gas', 'Petrol']
            };
            
            Object.keys(synonymMap).forEach(key => {
                if (normalizedTerm.includes(key)) {
                    synonymMap[key].forEach(synonym => {
                        conditions.push(`bodyStyle.ilike.%${synonym}%`);
                        conditions.push(`transmission.ilike.%${synonym}%`);
                        conditions.push(`fuelType.ilike.%${synonym}%`);
                    });
                }
            });
            
            // Add fuzzy matching
            const fuzzyMatches = {
                'toyata': 'toyota',
                'toyoto': 'toyota',
                'honada': 'honda',
                'nisan': 'nissan'
            };
            
            Object.keys(fuzzyMatches).forEach(misspelling => {
                if (normalizedTerm.includes(misspelling)) {
                    const correction = fuzzyMatches[misspelling];
                    conditions.push(`make.ilike.%${correction}%`);
                }
            });
            
            return conditions;
        }
    </script>
</body>
</html>
