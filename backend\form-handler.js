/**
 * Form Handler Service
 * This is a simple Express server that handles form submissions
 * and forwards them to Supabase using the service role key.
 */
import express from 'express';
import cors from 'cors';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Initialize dotenv
dotenv.config();

// Get directory name (ES modules don't have __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Create Supabase client with service role key (bypasses RLS)
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY; // This should be the service role key

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Supabase configuration missing. Please set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Form submission endpoint
app.post('/api/pre-qualification', async (req, res) => {
  try {
    const formData = req.body;
    
    // Validate required fields
    const requiredFields = ['first_name', 'last_name', 'email', 'phone_number', 'financing_type'];
    const missingFields = requiredFields.filter(field => !formData[field]);
    
    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Missing required fields: ${missingFields.join(', ')}`
      });
    }

    // Validate financing_type is either 'Purchase' or 'Lease'
    if (!['Purchase', 'Lease'].includes(formData.financing_type)) {
      return res.status(400).json({
        success: false,
        error: 'Financing type must be either "Purchase" or "Lease"'
      });
    }

    // Insert the pre-qualification data into the Supabase table using service role key
    const { data, error } = await supabase
      .from('pre_qualifications')
      .insert([
        {
          first_name: formData.first_name,
          last_name: formData.last_name,
          email: formData.email,
          phone_number: formData.phone_number,
          vehicle_of_interest: formData.vehicle_of_interest || null,
          financing_type: formData.financing_type
        }
      ])
      .select();

    if (error) {
      console.error('Error submitting pre-qualification form:', error);
      return res.status(500).json({
        success: false,
        error: error.message || 'Failed to submit pre-qualification form'
      });
    }

    console.log('Pre-qualification form submitted successfully:', data);
    return res.status(200).json({
      success: true,
      data: data[0]
    });
  } catch (error) {
    console.error('Unexpected error submitting pre-qualification form:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'An unexpected error occurred'
    });
  }
});

// Get all pre-qualifications endpoint
app.get('/api/pre-qualifications', async (req, res) => {
  try {
    // In a production environment, this endpoint should be protected
    // with authentication middleware to ensure only admins can access it
    
    // Fetch all pre-qualifications from Supabase
    const { data, error } = await supabase
      .from('pre_qualifications')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching pre-qualification submissions:', error);
      return res.status(500).json({
        success: false,
        error: error.message || 'Failed to fetch pre-qualification submissions'
      });
    }

    console.log(`Successfully fetched ${data.length} pre-qualification submissions`);
    return res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Unexpected error fetching pre-qualification submissions:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'An unexpected error occurred'
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Form handler service running on port ${PORT}`);
});