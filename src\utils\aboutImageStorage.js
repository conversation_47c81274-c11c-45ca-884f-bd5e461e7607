/**
 * About Images Storage Service
 * This file provides functions to interact with Supabase Storage for about page images
 */
import { supabase } from '../lib/supabaseClient';

// The bucket name for about page images
const ABOUT_IMAGES_BUCKET = 'about-images';
const PLACEHOLDER_IMAGE = 'https://via.placeholder.com/400x300?text=No+Image';

/**
 * Get the public URL for an image using Supabase's built-in method
 * @param {string} path - The path of the image in the bucket
 * @returns {string} The public URL of the image
 */
export const getPublicImageUrl = (path) => {
  console.log(`[getPublicImageUrl] Called with path: "${path}"`);
  
  if (!path) {
    console.warn(`[getPublicImageUrl] Path is null or undefined, returning placeholder`);
    return PLACEHOLDER_IMAGE;
  }
  
  // If it's already a full Supabase URL, return it
  if (path.includes(`supabase.co/storage/v1/object/public/${ABOUT_IMAGES_BUCKET}/`)) {
    console.log(`[getPublicImageUrl] Path is already a full Supabase URL, returning as is`);
    return path;
  }
  
  // If it's a placeholder or other external URL, return it
  if (path.startsWith('http')) {
    console.log(`[getPublicImageUrl] Path is an external URL, returning as is`);
    return path;
  }
  
  // Otherwise, assume it's a path within the bucket and construct the URL
  try {
    console.log(`[getPublicImageUrl] Generating public URL for path "${path}" in bucket "${ABOUT_IMAGES_BUCKET}"`);
    const { data } = supabase.storage.from(ABOUT_IMAGES_BUCKET).getPublicUrl(path);
    
    if (data?.publicUrl) {
      console.log(`[getPublicImageUrl] Generated public URL: "${data.publicUrl}"`);
      return data.publicUrl;
    } else {
      console.error(`[getPublicImageUrl] Failed to generate public URL for path "${path}": No publicUrl in response`);
      return PLACEHOLDER_IMAGE;
    }
  } catch (error) {
    console.error(`[getPublicImageUrl] Error generating public URL for path "${path}":`, error);
    return PLACEHOLDER_IMAGE;
  }
};

/**
 * Upload an image for a timeline event to Supabase Storage
 * @param {File} file - The file to upload
 * @param {number|string} eventId - The ID of the timeline event
 * @param {string} year - The year of the timeline event (used for naming the file)
 * @returns {Promise<string|null>} Promise that resolves to the public URL of the uploaded image, or null if upload failed
 */
export const uploadTimelineImage = async (file, eventId, year) => {
  try {
    console.log(`Uploading image for timeline event ${eventId} (Year: ${year})`);
    console.log(`File name: ${file.name}, File size: ${file.size}, File type: ${file.type}`);
    
    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      console.error('User is not authenticated. Authentication is required to upload files.');
      return null;
    }
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.error('Invalid file type. Only images are allowed.');
      return null;
    }
    
    // Validate year parameter
    if (!year) {
      console.error('Year parameter is required for naming the file according to the database format.');
      return null;
    }
    
    // Extract file extension and ensure it's valid
    let fileExt = file.name.split('.').pop().toLowerCase();
    if (!fileExt || fileExt === file.name.toLowerCase()) {
      // If no extension found, try to get it from the MIME type
      fileExt = file.type.split('/').pop();
      if (!fileExt || fileExt === 'octet-stream') {
        fileExt = 'jpg'; // Default to jpg if no extension can be determined
      }
    }
    
    // Create a filename that matches the format in the about_timeline table
    // The format is YYYY_image as per the database schema
    const filePath = `${year}/${year}_image.${fileExt}`;
    console.log(`Generated filepath: ${filePath}`);
    
    // Upload the file to Supabase Storage with public access
    const { data, error } = await supabase.storage
      .from(ABOUT_IMAGES_BUCKET)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true, // Overwrite if exists
        contentType: file.type, // Use the file's actual content type
      });
    
    if (error) {
      console.error('Error uploading file to Supabase Storage:', error);
      console.error('Error details:', JSON.stringify(error));
      
      // Provide more specific error messages based on error code
      if (error.statusCode === '403' && error.message.includes('row-level security policy')) {
        console.error('Permission denied: Your account does not have permission to upload to this bucket.');
        console.error('Please check Supabase RLS policies for the storage bucket.');
      }
      
      return null;
    }
    
    console.log('Upload successful:', data);
    
    // Get the public URL
    const publicUrl = getPublicImageUrl(filePath);
    console.log(`Generated public URL: ${publicUrl}`);
    
    // Return both the public URL and the image path that matches the database format
    return {
      publicUrl,
      imagePath: `${year}_image` // This matches the format in the about_timeline table
    };
  } catch (error) {
    console.error('Error in uploadTimelineImage:', error);
    return null;
  }
};

/**
 * Delete an image from Supabase Storage
 * @param {string} url - The public URL of the image to delete
 * @returns {Promise<boolean>} Promise that resolves to true if deletion was successful
 */
export const deleteTimelineImage = async (url) => {
  try {
    // Extract the path from the URL
    // The URL format is: https://<project-id>.supabase.co/storage/v1/object/public/<bucket>/<path>
    const urlParts = url.split(`/public/${ABOUT_IMAGES_BUCKET}/`);
    if (urlParts.length < 2) {
      console.error('Invalid image URL format:', url);
      return false;
    }
    
    const filePath = urlParts[1];
    console.log(`Deleting file at path: ${filePath}`);
    
    // Delete the file from Supabase Storage
    const { error } = await supabase.storage
      .from(ABOUT_IMAGES_BUCKET)
      .remove([filePath]);
    
    if (error) {
      console.error('Error deleting file from Supabase Storage:', error);
      return false;
    }
    
    console.log('File deleted successfully');
    return true;
  } catch (error) {
    console.error('Error in deleteTimelineImage:', error);
    return false;
  }
};

/**
 * Delete a timeline image by year
 * @param {string} year - The year of the timeline event
 * @returns {Promise<boolean>} Promise that resolves to true if deletion was successful
 */
export const deleteTimelineImageByYear = async (year) => {
  try {
    if (!year) {
      console.error('Year parameter is required');
      return false;
    }
    
    // The path format is: year/year_image.ext
    // Since we don't know the extension, we'll list files in the directory and filter
    const { data, error } = await supabase.storage
      .from(ABOUT_IMAGES_BUCKET)
      .list(`${year}`);
    
    if (error) {
      console.error('Error listing files in directory:', error);
      return false;
    }
    
    if (!data || data.length === 0) {
      console.warn(`No files found in directory ${year}`);
      return true; // Nothing to delete
    }
    
    // Filter files that match the pattern year_image.*
    const filesToDelete = data
      .filter(file => file.name.startsWith(`${year}_image.`))
      .map(file => `${year}/${file.name}`);
    
    if (filesToDelete.length === 0) {
      console.warn(`No matching files found for pattern ${year}_image.*`);
      return true; // Nothing to delete
    }
    
    console.log(`Deleting files: ${filesToDelete.join(', ')}`);
    
    // Delete the files from Supabase Storage
    const { error: deleteError } = await supabase.storage
      .from(ABOUT_IMAGES_BUCKET)
      .remove(filesToDelete);
    
    if (deleteError) {
      console.error('Error deleting files from Supabase Storage:', deleteError);
      return false;
    }
    
    console.log('Files deleted successfully');
    return true;
  } catch (error) {
    console.error('Error in deleteTimelineImageByYear:', error);
    return false;
  }
};

export default {
  uploadTimelineImage,
  deleteTimelineImage,
  deleteTimelineImageByYear,
  getPublicImageUrl
};