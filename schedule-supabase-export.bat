@echo off
echo ===================================================
echo GT Motorsports Supabase to CarPages Export Scheduler
echo ===================================================
echo.
echo Starting Supabase to CarPages export scheduler...
echo Timestamp: %date% %time%
echo.

cd scripts

echo Installing dependencies...
call npm install
if %ERRORLEVEL% NEQ 0 (
  echo Failed to install dependencies.
  goto error
)

echo.
echo Starting scheduler...
echo The scheduler will run the export daily at 2:00 AM.
echo Keep this window open for the scheduler to work.
echo.

REM Create a simple scheduler script
echo import cron from 'node-cron'; > supabase-scheduler.js
echo import { spawn } from 'child_process'; >> supabase-scheduler.js
echo import { fileURLToPath } from 'url'; >> supabase-scheduler.js
echo import { dirname } from 'path'; >> supabase-scheduler.js
echo import path from 'path'; >> supabase-scheduler.js
echo. >> supabase-scheduler.js
echo // Get the directory name of the current module >> supabase-scheduler.js
echo const __filename = fileURLToPath(import.meta.url); >> supabase-scheduler.js
echo const __dirname = dirname(__filename); >> supabase-scheduler.js
echo. >> supabase-scheduler.js
echo // Schedule the export to run daily at 2:00 AM >> supabase-scheduler.js
echo console.log('Scheduling Supabase to CarPages export to run daily at 2:00 AM...'); >> supabase-scheduler.js
echo cron.schedule('0 2 * * *', () => { >> supabase-scheduler.js
echo   console.log('Running scheduled Supabase to CarPages export at ' + new Date().toLocaleString()); >> supabase-scheduler.js
echo   const exportProcess = spawn('node', [path.join(__dirname, 'export-supabase-to-carpages.js')], { stdio: 'inherit' }); >> supabase-scheduler.js
echo   exportProcess.on('close', (code) => { >> supabase-scheduler.js
echo     console.log('Export process exited with code ' + code); >> supabase-scheduler.js
echo   }); >> supabase-scheduler.js
echo }, { >> supabase-scheduler.js
echo   scheduled: true, >> supabase-scheduler.js
echo   timezone: 'America/Denver' >> supabase-scheduler.js
echo }); >> supabase-scheduler.js
echo. >> supabase-scheduler.js
echo // Run an initial export immediately >> supabase-scheduler.js
echo console.log('Running initial export...'); >> supabase-scheduler.js
echo const initialExport = spawn('node', [path.join(__dirname, 'export-supabase-to-carpages.js')], { stdio: 'inherit' }); >> supabase-scheduler.js
echo initialExport.on('close', (code) => { >> supabase-scheduler.js
echo   console.log('Initial export process exited with code ' + code); >> supabase-scheduler.js
echo }); >> supabase-scheduler.js
echo. >> supabase-scheduler.js
echo console.log('Scheduler is running. Press Ctrl+C to stop.'); >> supabase-scheduler.js

node supabase-scheduler.js
if %ERRORLEVEL% NEQ 0 (
  echo Failed to start scheduler.
  goto error
)

goto end

:error
echo.
echo Press any key to exit...
pause > nul
exit /b 1

:end
echo.
echo Press any key to exit...
pause > nul
exit /b 0