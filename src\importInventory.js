import vehicleStore from './store/vehicles';
import database from './database';
import supabaseService from './database/supabaseService';

/**
 * Import vehicles from an uploaded file
 * This script parses the file and creates vehicle listings
 * for all vehicles in the inventory
 * @param {File} file - The uploaded file (markdown or JSON)
 * @returns {Promise<number>} - Number of vehicles successfully imported
 */
const importInventory = async (file) => {
  try {
    if (!file) {
      throw new Error('No file selected. Please select a file to import.');
    }
    
    // Determine file type and parse accordingly
    let vehicles = [];
    if (file.name.endsWith('.md')) {
      // Read the uploaded markdown file content
      const inventoryData = await readFileContent(file);
      
      // Validate file content
      if (!inventoryData.includes('# GT Motorsports Vehicle Inventory')) {
        throw new Error('Invalid file format. The file does not appear to be a GT Motorsports inventory file.');
      }
      
      // Parse the markdown content
      vehicles = parseInventoryMarkdown(inventoryData);
    } else if (file.name.endsWith('.json')) {
      // Read and parse JSON file
      const inventoryData = await readFileContent(file);
      try {
        const parsedData = JSON.parse(inventoryData);
        if (Array.isArray(parsedData.vehicles)) {
          vehicles = parsedData.vehicles;
        } else {
          throw new Error('Invalid JSON format. Expected a "vehicles" array.');
        }
      } catch (error) {
        throw new Error(`Failed to parse JSON file: ${error.message}`);
      }
    } else {
      throw new Error('Invalid file format. Please select a markdown (.md) or JSON (.json) file.');
    }
    
    if (vehicles.length === 0) {
      throw new Error('No vehicles found in the inventory file. Please check the file format.');
    }
    
    console.log(`Found ${vehicles.length} vehicles in inventory`);
    
    // Initialize both databases
    await database.initDatabase();
    await supabaseService.initSupabaseDatabase();
    
    // Add each vehicle to the database and store
    let addedCount = 0;
    let errorCount = 0;
    const errors = [];
    
    // Process vehicles in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < vehicles.length; i += batchSize) {
      const batch = vehicles.slice(i, i + batchSize);
      
      // Process batch in parallel
      const results = await Promise.allSettled(
        batch.map(async (vehicle) => {
          try {
            // Validate required fields
            if (!validateVehicle(vehicle)) {
              throw new Error(`Missing required fields for ${vehicle.title || 'Unknown vehicle'}`);
            }
            
            // Add to Supabase database
            const vehicleId = await supabaseService.addVehicleToSupabase(vehicle);
            console.log(`Added to Supabase: ${vehicle.title} (ID: ${vehicleId})`);
            
            // Also add to local database for backward compatibility
            const localId = await database.addVehicle(vehicle);
            console.log(`Added to local database: ${vehicle.title} (ID: ${localId})`);
            
            // Also add to store for backward compatibility
            await vehicleStore.addVehicle(vehicle);
            
            return { success: true, vehicle };
          } catch (error) {
            console.error(`Error adding vehicle ${vehicle.title || 'Unknown'}:`, error);
            return {
              success: false,
              vehicle,
              error: error.message || 'Unknown error'
            };
          }
        })
      );
      
      // Count successes and failures
      results.forEach(result => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            addedCount++;
          } else {
            errorCount++;
            errors.push({
              vehicle: result.value.vehicle.title || 'Unknown vehicle',
              error: result.value.error
            });
          }
        } else {
          errorCount++;
          errors.push({
            vehicle: 'Unknown vehicle',
            error: result.reason.message || 'Unknown error'
          });
        }
      });
    }
    
    // Log summary
    console.log(`Successfully added ${addedCount} vehicles to inventory`);
    
    if (errorCount > 0) {
      console.warn(`Failed to add ${errorCount} vehicles`);
      console.warn('Errors:', errors);
      
      if (addedCount === 0) {
        throw new Error(`Failed to import any vehicles. ${errorCount} vehicles had errors.`);
      }
    }
    
    return addedCount;
  } catch (error) {
    console.error('Error importing inventory:', error);
    throw error;
  }
};

/**
 * Validate that a vehicle has all required fields
 * @param {Object} vehicle - The vehicle object to validate
 * @returns {boolean} - Whether the vehicle is valid
 */
const validateVehicle = (vehicle) => {
  const requiredFields = [
    'title', 'year', 'make', 'model', 'price'
  ];
  
  // Check if all required fields exist and have values
  const hasAllRequiredFields = requiredFields.every(field => {
    const value = vehicle[field];
    return value !== undefined && value !== null && value !== '';
  });

  // Check if year is a valid number
  const hasValidYear = typeof vehicle.year === 'number' && vehicle.year > 1900 && vehicle.year <= new Date().getFullYear() + 1;
  
  // Check if price is a valid number
  const hasValidPrice = typeof vehicle.price === 'number' && vehicle.price > 0;

  // Log validation issues for debugging
  if (!hasAllRequiredFields) {
    const missingFields = requiredFields.filter(field => {
      const value = vehicle[field];
      return value === undefined || value === null || value === '';
    });
    console.error(`Vehicle missing required fields: ${missingFields.join(', ')}`, vehicle);
  }
  
  if (!hasValidYear) {
    console.error(`Vehicle has invalid year: ${vehicle.year}`, vehicle);
  }
  
  if (!hasValidPrice) {
    console.error(`Vehicle has invalid price: ${vehicle.price}`, vehicle);
  }
  
  return hasAllRequiredFields && hasValidYear && hasValidPrice;
};

/**
 * Parse the markdown content to extract vehicle information
 * @param {string} markdownContent - The content of the GTINV.md file
 * @returns {Array} - Array of vehicle objects
 */
const parseInventoryMarkdown = (markdownContent) => {
  const vehicles = [];
  
  // Split the content by make sections
  const makeRegex = /## ([^\n]+)/g;
  const matches = [...markdownContent.matchAll(makeRegex)];
  
  // Process each make section
  for (let i = 0; i < matches.length; i++) {
    const make = matches[i][1].trim();
    
    // Get the content for this make section
    const startPos = matches[i].index + matches[i][0].length;
    const endPos = (i < matches.length - 1) ? matches[i + 1].index : markdownContent.length;
    const makeContent = markdownContent.substring(startPos, endPos);
    
    if (!makeContent) continue;
    
    // Split the make content by vehicle entries
    const vehicleRegex = /\d+\.\s+\*\*([^*]+)\*\*/g;
    const vehicleMatches = [...makeContent.matchAll(vehicleRegex)];
    
    // Process each vehicle entry
    for (let j = 0; j < vehicleMatches.length; j++) {
      try {
        const vehicleTitle = vehicleMatches[j][1].trim();
        
        // Get the content for this vehicle
        const vStartPos = vehicleMatches[j].index + vehicleMatches[j][0].length;
        const vEndPos = (j < vehicleMatches.length - 1) ? vehicleMatches[j + 1].index : makeContent.length;
        const vehicleContent = makeContent.substring(vStartPos, vEndPos);
        
        // Parse the vehicle data
        const vehicle = parseVehicleData(vehicleTitle, vehicleContent, make);
        if (vehicle) {
          vehicles.push(vehicle);
        }
      } catch (error) {
        console.error(`Error parsing vehicle entry at index ${i}:${j}`, error);
      }
    }
  }
  
  console.log(`Found ${vehicles.length} vehicles in markdown content`);
  return vehicles;
};

/**
 * Parse vehicle data from title and content
 * @param {string} title - The vehicle title (year, make, model)
 * @param {string} content - The vehicle details content
 * @param {string} make - The vehicle make
 * @returns {Object} - Vehicle object formatted for the store
 */
const parseVehicleData = (title, content, make) => {
  // Extract year and model from title
  const yearModelMatch = title.match(/^(\d{4})\s+(.+)$/);
  if (!yearModelMatch) {
    console.error("Failed to match year and model in title:", title);
    return null;
  }
  
  const year = parseInt(yearModelMatch[1]);
  const model = yearModelMatch[2];
  
  // Initialize the vehicle object with default values
  const vehicle = {
    title,
    year,
    make,
    model,
    price: 0,
    specialPrice: null,
    image: '', // Will be updated with placeholder images
    gallery: [], // Will be updated with placeholder images
    mileage: 0,
    trim: '',
    doors: '',
    bodyStyle: '',
    engine: '',
    engineSize: '',
    drivetrain: '',
    transmission: 'Automatic', // Default value
    exteriorColor: '',
    interiorColor: '',
    passengers: '',
    fuelType: 'Gasoline', // Default value
    cityFuel: '',
    hwyFuel: '',
    stockNumber: '',
    vin: '',
    highlights: [''],
    features: [''],
    description: `${year} ${make} ${model}`
  };
  
  // Extract details from the content
  const lines = content.split('\n').map(line => line.trim()).filter(Boolean);
  
  for (const line of lines) {
    if (line.startsWith('- ')) {
      const parts = line.substring(2).split(':');
      if (parts.length < 2) {
        console.log(`Skipping invalid line format: ${line}`);
        continue;
      }
      
      const key = parts[0].trim();
      // Join the rest of the parts in case there are colons in the value
      const value = parts.slice(1).join(':').trim();
      
      if (key && value) {
        switch (key.toLowerCase()) {
          case 'trim':
            vehicle.trim = value;
            break;
          case 'body style':
            vehicle.bodyStyle = value;
            break;
          case 'engine':
            vehicle.engine = value;
            vehicle.engineSize = value; // Use same value for both fields
            break;
          case 'exterior color':
            vehicle.exteriorColor = value;
            break;
          case 'interior color':
            vehicle.interiorColor = value;
            break;
          case 'drive type':
            vehicle.drivetrain = value;
            break;
          case 'odometer':
            // Extract numeric value from "X,XXX km"
            const mileageMatch = value.match(/(\d+,?\d*)/);
            if (mileageMatch) {
              vehicle.mileage = parseInt(mileageMatch[1].replace(/,/g, ''));
            }
            break;
          case 'price':
            // Extract numeric value from "$XX,XXX"
            const priceMatch = value.match(/\$(\d+,?\d*)/);
            if (priceMatch) {
              vehicle.price = parseInt(priceMatch[1].replace(/,/g, ''));
            }
            break;
          case 'passenger capacity':
            const passengerMatch = value.match(/(\d+)/);
            if (passengerMatch) {
              vehicle.passengers = parseInt(passengerMatch[1]);
            }
            break;
        }
      }
    }
  }
  
  // Generate a stock number if not available
  if (!vehicle.stockNumber) {
    vehicle.stockNumber = `${make.substring(0, 3)}-${model.substring(0, 3)}-${Math.floor(Math.random() * 10000)}`.toUpperCase();
  }
  
  // Generate a VIN if not available (this is just a placeholder)
  if (!vehicle.vin) {
    vehicle.vin = `GT${Math.random().toString(36).substring(2, 10).toUpperCase()}${year}`;
  }
  
  // Determine vehicle type and add type-specific details
  let vehicleType = null;
  let typeSpecificDetails = {};
  
  if (vehicle.bodyStyle) {
    switch (vehicle.bodyStyle.toLowerCase()) {
      case 'motorcycle':
        vehicleType = 'motorcycle';
        // Extract engine CC from engine field
        const ccMatch = vehicle.engine ? vehicle.engine.match(/([\d\.]+)L|(\d+)cc/) : null;
        if (ccMatch) {
          const cc = ccMatch[1] ? parseFloat(ccMatch[1]) * 1000 : parseInt(ccMatch[2]);
          typeSpecificDetails.engineCC = cc;
        }
        // Extract cylinders from engine field
        const cylinderMatch = vehicle.engine ? vehicle.engine.match(/(\d+)-Cylinder/) : null;
        if (cylinderMatch) {
          typeSpecificDetails.cylinders = parseInt(cylinderMatch[1]);
        }
        break;
      case 'boat':
        vehicleType = 'boat';
        if (vehicle.passengers) {
          typeSpecificDetails.passengerCapacity = vehicle.passengers;
        }
        break;
      case 'atv':
        vehicleType = 'atv';
        // Extract engine CC from engine field
        const atvCCMatch = vehicle.engine ? vehicle.engine.match(/([\d\.]+)L|(\d+)cc/) : null;
        if (atvCCMatch) {
          const cc = atvCCMatch[1] ? parseFloat(atvCCMatch[1]) * 1000 : parseInt(atvCCMatch[2]);
          typeSpecificDetails.engineCC = cc;
        }
        break;
    }
  }
  
  // Set vehicle type and type-specific details if determined
  if (vehicleType) {
    vehicle.vehicleType = vehicleType;
    vehicle.typeSpecificDetails = typeSpecificDetails;
  }
  
  // Add some default features based on the vehicle type
  if (vehicle.bodyStyle) {
    switch (vehicle.bodyStyle.toLowerCase()) {
      case 'suv':
        vehicle.features = ['All-Wheel Drive', 'Spacious Interior', 'Roof Rails', 'Backup Camera'];
        vehicle.highlights = ['Versatile SUV', 'Family Friendly', 'All-Weather Capability'];
        vehicle.doors = vehicle.doors || 4;
        vehicle.passengers = vehicle.passengers || 5;
        break;
      case 'sedan':
        vehicle.features = ['Comfortable Ride', 'Fuel Efficient', 'Backup Camera', 'Bluetooth Connectivity'];
        vehicle.highlights = ['Reliable Sedan', 'Great Daily Driver', 'Excellent Fuel Economy'];
        vehicle.doors = vehicle.doors || 4;
        vehicle.passengers = vehicle.passengers || 5;
        break;
      case 'pickup truck':
        vehicle.features = ['Towing Package', 'Bed Liner', 'Power Windows', 'Bluetooth Connectivity'];
        vehicle.highlights = ['Powerful Truck', 'Great Towing Capacity', 'Versatile Cargo Space'];
        vehicle.doors = vehicle.doors || 2;
        vehicle.passengers = vehicle.passengers || 3;
        break;
      case 'motorcycle':
        vehicle.features = ['Lightweight Design', 'Fuel Efficient', 'Performance Exhaust'];
        vehicle.highlights = ['Exciting Ride', 'Great Handling', 'Fuel Efficient'];
        vehicle.doors = 0;
        vehicle.passengers = vehicle.passengers || 2;
        break;
      case 'boat':
        vehicle.features = ['Life Jackets', 'Navigation System', 'Stereo System'];
        vehicle.highlights = ['Great for Water Activities', 'Reliable Engine', 'Well Maintained'];
        vehicle.doors = 0;
        break;
      case 'atv':
        vehicle.features = ['Rugged Design', 'All-Terrain Capability', 'Reliable Performance'];
        vehicle.highlights = ['Off-Road Ready', 'Powerful Engine', 'Great Handling'];
        vehicle.doors = 0;
        vehicle.passengers = vehicle.passengers || 1;
        break;
      default:
        vehicle.features = ['Power Windows', 'Bluetooth Connectivity', 'Backup Camera'];
        vehicle.highlights = ['Great Value', 'Well Maintained', 'Reliable Transportation'];
        vehicle.doors = vehicle.doors || 4;
        vehicle.passengers = vehicle.passengers || 5;
    }
  }
  
  // Add a more detailed description
  if (vehicle.bodyStyle && ['motorcycle', 'boat', 'atv'].includes(vehicle.bodyStyle.toLowerCase())) {
    vehicle.description = `This ${year} ${make} ${model} ${vehicle.trim} is in excellent condition with ${vehicle.mileage.toLocaleString()} km. ` +
      `It features a ${vehicle.engine} engine and a beautiful ${vehicle.exteriorColor} exterior. ` +
      `This ${vehicle.bodyStyle.toLowerCase()} offers a perfect blend of style, performance, and reliability. YEAR END BLOWOUT!`;
  } else {
    vehicle.description = `This ${year} ${make} ${model} ${vehicle.trim} is in excellent condition with ${vehicle.mileage.toLocaleString()} km. ` +
      `It features a ${vehicle.engine} engine, ${vehicle.drivetrain} drivetrain, and a beautiful ${vehicle.exteriorColor} exterior with ${vehicle.interiorColor} interior. ` +
      `This vehicle offers a perfect blend of style, comfort, and performance. $0 DOWN | EVERYONE APPROVED!`;
  }
  
  // Add placeholder images based on vehicle type
  const placeholderImages = getPlaceholderImages(vehicle.bodyStyle);
  vehicle.gallery = placeholderImages;
  vehicle.image = placeholderImages[0];
  
  return vehicle;
};

/**
 * Get placeholder images based on vehicle type
 * @param {string} bodyStyle - The vehicle body style
 * @returns {Array} - Array of placeholder image URLs
 */
const getPlaceholderImages = (bodyStyle) => {
  if (!bodyStyle) return ['https://via.placeholder.com/800x600?text=No+Image+Available'];
  
  switch (bodyStyle.toLowerCase()) {
    case 'suv':
      return [
        'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
    case 'sedan':
      return [
        'https://images.unsplash.com/photo-1580273916550-e323be2ae537?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
    case 'pickup truck':
      return [
        'https://images.unsplash.com/photo-1659360380235-5fb9e4e0b00b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1569516449771-41c89ee14ca3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
    case 'motorcycle':
      return [
        'https://images.unsplash.com/photo-1558981806-ec527fa84c39?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
    case 'boat':
      return [
        'https://images.unsplash.com/photo-1540946485063-a40da27545f8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1605281317010-fe5ffe798166?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
    case 'atv':
      return [
        'https://images.unsplash.com/photo-1625505826533-5c80aca7d8d1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1581850518616-bcb8077a2336?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
    case 'coupe':
      return [
        'https://images.unsplash.com/photo-1614162692292-7ac56d7f7f1e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
    case 'convertible':
      return [
        'https://images.unsplash.com/photo-1583121274602-3e2820c69888?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1502161254066-6c74afbf07aa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80'
      ];
    case 'minivan':
      return [
        'https://images.unsplash.com/photo-1551743552-43ce05c9511f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1600661653561-629509216228?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
    case 'van':
      return [
        'https://images.unsplash.com/photo-1600661653561-629509216228?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1627059437307-89c87362c95d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
    case 'hatchback':
      return [
        'https://images.unsplash.com/photo-1471444928139-48c5bf5173f8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1590510696537-c0c8b39b80a3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
    default:
      return [
        'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
      ];
  }
};

/**
 * Read the content of a file using FileReader
 * @param {File} file - The file to read
 * @returns {Promise<string>} - The file content as text
 */
const readFileContent = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      resolve(event.target.result);
    };
    
    reader.onerror = (error) => {
      reject(new Error('Failed to read file: ' + error.message));
    };
    
    reader.readAsText(file);
  });
};

/**
 * Create a sample JSON inventory file
 * @returns {Object} - Sample inventory data
 */
export const createSampleJsonInventory = () => {
  return {
    "name": "GT Motorsports Vehicle Inventory",
    "description": "Complete inventory of vehicles available at GT Motorsports",
    "lastUpdated": new Date().toISOString(),
    "vehicles": [
      {
        "title": "2023 Electric Sedan",
        "year": 2023,
        "make": "Example Make",
        "model": "Electric Sedan",
        "trim": "Long Range AWD",
        "bodyStyle": "Sedan",
        "engine": "Electric",
        "exteriorColor": "Red",
        "interiorColor": "Black",
        "drivetrain": "AWD",
        "mileage": 5000,
        "price": 45988,
        "features": [
          "Driver Assistance",
          "Premium Sound System",
          "Panoramic Roof",
          "Heated Seats"
        ],
        "highlights": [
          "All-Electric Vehicle",
          "Long Range Battery",
          "Dual Motor"
        ],
        "description": "This 2023 Electric Sedan Long Range AWD is in excellent condition with 5,000 km. It features an electric powertrain, AWD drivetrain, and a beautiful Red exterior with Black interior. This vehicle offers a perfect blend of style, comfort, and performance. $0 DOWN | EVERYONE APPROVED!"
      },
      {
        "title": "2022 Electric SUV",
        "year": 2022,
        "make": "Example Make",
        "model": "Electric SUV",
        "trim": "Performance AWD",
        "bodyStyle": "SUV",
        "engine": "Electric",
        "exteriorColor": "White",
        "interiorColor": "White",
        "drivetrain": "AWD",
        "mileage": 15200,
        "price": 58988,
        "features": [
          "Driver Assistance",
          "Premium Sound System",
          "Panoramic Roof",
          "Heated Seats"
        ],
        "highlights": [
          "All-Electric Vehicle",
          "Performance Package",
          "Dual Motor"
        ],
        "description": "This 2022 Electric SUV Performance AWD is in excellent condition with 15,200 km. It features an electric powertrain, AWD drivetrain, and a beautiful White exterior with White interior. This vehicle offers a perfect blend of style, comfort, and performance. $0 DOWN | EVERYONE APPROVED!"
      }
    ]
  };
};

export default importInventory;