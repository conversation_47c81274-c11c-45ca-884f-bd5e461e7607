<template>
  <div v-if="value !== null && value !== undefined && value !== ''">
    <dt class="text-xs font-medium text-gray-500 uppercase">{{ label }}</dt>
    <dd class="text-gray-900">{{ value }}</dd>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  label: {
    type: String,
    required: true,
  },
  value: {
    type: [String, Number],
    default: null,
  },
});
</script>