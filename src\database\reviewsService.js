import { supabase } from '../utils/supabase'

/**
 * Get all reviews for the current tenant
 * @param {string} category - Optional category filter
 * @returns {Promise<Array>} Array of reviews
 */
export const getReviews = async (category = null) => {
  try {
    let query = supabase
      .from('reviews')
      .select('*')
    
    // Apply category filter if provided
    if (category) {
      query = query.eq('category', category)
    }
    
    const { data, error } = await query
    
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Error fetching reviews:', error)
    throw error
  }
}

/**
 * Add a new review
 * @param {Object} reviewData - The review data
 * @returns {Promise<Object>} The new review ID
 */
export const addReview = async (reviewData) => {
  try {
    // No need to explicitly set tenant_id as RLS will handle this
    const { data, error } = await supabase
      .from('reviews')
      .insert(reviewData)
      .select('id')
      .single()
    
    if (error) throw error
    return { id: data.id }
  } catch (error) {
    console.error('Error adding review:', error)
    throw error
  }
}

/**
 * Update a review
 * @param {string} id - The review UUID
 * @param {Object} reviewData - The updated review data
 * @returns {Promise<boolean>} Success status
 */
export const updateReview = async (id, reviewData) => {
  try {
    const { error } = await supabase
      .from('reviews')
      .update(reviewData)
      .eq('id', id)
    
    if (error) throw error
    return true
  } catch (error) {
    console.error('Error updating review:', error)
    throw error
  }
}

/**
 * Delete a review
 * @param {string} id - The review UUID
 * @returns {Promise<boolean>} Success status
 */
export const deleteReview = async (id) => {
  try {
    const { error } = await supabase
      .from('reviews')
      .delete()
      .eq('id', id)
    
    if (error) throw error
    return true
  } catch (error) {
    console.error('Error deleting review:', error)
    throw error
  }
}

/**
 * Upload a review image
 * @param {File} file - The image file
 * @param {string} reviewId - The review UUID
 * @returns {Promise<Object>} The image path and public URL
 */
export const uploadReviewImage = async (file, reviewId) => {
  try {
    const fileExt = file.name.split('.').pop()
    const filePath = `reviews/${reviewId}/${Date.now()}.${fileExt}`
    
    const { error } = await supabase.storage
      .from('review-images')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      })
    
    if (error) throw error
    
    // Get the public URL
    const { data } = supabase.storage
      .from('review-images')
      .getPublicUrl(filePath)
    
    return {
      imagePath: filePath,
      publicUrl: data.publicUrl
    }
  } catch (error) {
    console.error('Error uploading review image:', error)
    throw error
  }
}