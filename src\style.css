@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700;800&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&family=Playfair+Display:wght@400;500;600;700&family=Oswald:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Merriweather:wght@300;400;700&family=Poppins:wght@300;400;500;600;700&family=Nunito:wght@300;400;600;700&family=Source+Sans+Pro:wght@300;400;600;700&family=Noto+Sans:wght@300;400;500;600;700&family=Work+Sans:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Default CSS Variables (will be overridden by JS) */
  --color-primary: #1E293B;
  --color-primary-rgb: 30, 41, 59;
  --color-secondary: #E11D48;
  --color-secondary-rgb: 225, 29, 72;
  --color-accent: #0EA5E9;
  --color-accent-rgb: 14, 165, 233;
  --color-light: #F8FAFC;
  --color-light-rgb: 248, 250, 252;
  --color-dark: #0F172A;
  --color-dark-rgb: 15, 23, 42;
  --font-heading: 'Montserrat';
  --font-body: 'Inter';
  --card-border-radius: 0.5rem;
  --button-border-radius: 0.375rem;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    background-color: var(--color-light);
    color: var(--color-dark);
    font-family: var(--font-body), sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading), sans-serif;
    font-weight: bold;
  }
}

/* Shimmer loading effect for skeletons */
@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.shimmer-effect {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 100%);
  background-size: 1000px 100%;
}

.shimmer-bg {
  position: relative;
  overflow: hidden;
}

.shimmer-bg::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  animation: shimmer 2s infinite linear;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 100%);
  background-size: 1000px 100%;
}

/* Fade-in animation for lazy-loaded images */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

img.loaded {
  animation: fadeIn 0.5s ease-in-out;
}

/* Card fade-in animation for vehicle grid */
.fade-in-card {
  opacity: 0;
  animation: fadeIn 0.5s ease-in-out forwards;
}

@layer components {
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: var(--button-border-radius);
    font-weight: 500;
    transition: all 0.3s;
    outline: none;
  }

  .btn-primary {
    background-color: var(--color-secondary);
    color: var(--color-light);
  }

  .btn-primary:hover {
    background-color: color-mix(in srgb, var(--color-secondary) 90%, black);
  }

  .btn-secondary {
    background-color: var(--color-accent);
    color: var(--color-light);
  }

  .btn-secondary:hover {
    background-color: color-mix(in srgb, var(--color-accent) 90%, black);
  }

  .btn-outline {
    border: 2px solid var(--color-secondary);
    color: var(--color-secondary);
  }

  .btn-outline:hover {
    background-color: var(--color-secondary);
    color: var(--color-light);
  }

  .container-custom {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-custom {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  .section {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  @media (min-width: 768px) {
    .section {
      padding-top: 6rem;
      padding-bottom: 6rem;
    }
  }

  .card-custom {
    background-color: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: all 0.3s;
  }

  .card-custom:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* Testimonials Scrolling */
  .testimonials-scroll-container {
    overflow: hidden;
    position: relative;
    width: 100%;
  }

  .testimonials-scroll {
    display: flex;
    padding-top: 1rem;
    padding-bottom: 1rem;
    width: max-content;
    animation: scroll 80s linear infinite;
  }

  .testimonials-scroll:hover {
    animation-play-state: paused;
  }

  .testimonial-card {
    flex-shrink: 0;
  }

  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }

  /* Filter Animations */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Custom Range Slider Styling - only apply to non-custom sliders */
  input[type="range"]:not(.range-slider) {
    appearance: none;
    background-color: #e5e7eb; /* bg-gray-200 */
    height: 0.25rem;
    border-radius: 9999px;
  }

  input[type="range"]:not(.range-slider)::-webkit-slider-thumb {
    appearance: none;
    width: 1rem;
    height: 1rem;
    border-radius: 9999px;
    background-color: var(--color-secondary);
    border: none;
    cursor: pointer;
  }

  input[type="range"]:not(.range-slider)::-moz-range-thumb {
    width: 1rem;
    height: 1rem;
    border-radius: 9999px;
    background-color: var(--color-secondary);
    border: none;
    cursor: pointer;
  }

  /* Filter Tags */
  .filter-tag {
    background-color: rgba(var(--color-secondary-rgb, 225, 29, 72), 0.1);
    color: var(--color-secondary);
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    transition: all 0.2s;
  }

  .filter-tag:hover {
    background-color: rgba(var(--color-secondary-rgb, 225, 29, 72), 0.2);
  }

  /* Hide scrollbar for featured vehicles */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}