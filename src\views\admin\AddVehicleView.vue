<script setup>
import { onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import VehicleForm from '../../components/admin/VehicleForm.vue';
import AdminNavbar from '../../components/admin/AdminNavbar.vue';
import authStore from '../../store/supabaseAuth';
import vehicleStore from '../../store/vehicles';

// We're now using the backend proxy for VIN decoding instead of direct API calls
// This is more secure as it keeps API keys on the server side

const router = useRouter();
const isSubmitting = ref(false);
const showSuccessMessage = ref(false);
const errorMessage = ref('');

// Reactive object to hold form data, passed to VehicleForm via vehicle prop
const formData = ref({
  vin: '',
  title: '',
  year: null,
  make: '',
  model: '',
  trim: '',
  bodyStyle: '', // Changed from body_style to match VehicleForm
  engine: '',
  engineSize: '', // Added to match VehicleForm
  transmission: '',
  drivetrain: '', // Added to match VehicleForm
  exteriorColor: '', // Changed from exterior_color to match VehicleForm
  interiorColor: '', // Changed from interior_color to match VehicleForm
  fuelType: '', // Added to match VehicleForm
  cityFuel: '', // Added to match VehicleForm
  hwyFuel: '', // Added to match VehicleForm
  mileage: null,
  price: null,
  specialPrice: '', // Added to match VehicleForm
  doors: null, // Added to match VehicleForm
  passengers: null, // Added to match VehicleForm
  stockNumber: '', // Added to match VehicleForm
  description: '',
  image: '', // Changed from null to empty string to match VehicleForm
  gallery: [''], // Changed from [] to [''] to match VehicleForm
  highlights: [''], // Added to match VehicleForm
  features: [''] // Added to match VehicleForm
});

// Watch for changes to formData
watch(formData, (newData) => {
  console.log('formData changed in AddVehicleView:', newData);
}, { deep: true });

// Check authentication
onMounted(() => {
  authStore.initAuth();
  if (!authStore.isAuthenticated.value) {
    router.push('/admin/login');
  }
});


// Handle form submission (receives the data from VehicleForm)
// The VehicleForm component will emit the submit event with the form data
const handleSubmit = async (vehicleDataFromForm) => {
  isSubmitting.value = true;
  errorMessage.value = '';
  showSuccessMessage.value = false;

  try {
    console.log('Submitting vehicle data:', vehicleDataFromForm); // Log the data received from the form

    // Use the data received from the form event
    const newVehicle = await vehicleStore.addVehicle(vehicleDataFromForm);
    console.log('Vehicle added successfully:', newVehicle);

    showSuccessMessage.value = true;
    setTimeout(() => {
      router.push('/admin/dashboard');
    }, 2000);
  } catch (error) {
    console.error('Error adding vehicle:', error);
     if (error.message && error.message.includes('No ID returned')) {
       console.log('Vehicle likely added successfully despite ID error');
       showSuccessMessage.value = true;
       setTimeout(() => {
         router.push('/admin/dashboard');
       }, 2000);
     } else {
       errorMessage.value = error.message || 'Unknown error';
     }
  } finally {
    isSubmitting.value = false;
  }
};

// Handle cancel
const handleCancel = () => {
  router.push('/admin/dashboard');
};
</script>

<template>
  <div class="min-h-screen bg-gray-100">
    <AdminNavbar />
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Success Message -->
      <div v-if="showSuccessMessage" class="mb-6 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md">
        <!-- ... (success message content) ... -->
         <div class="flex items-center">
           <svg class="h-6 w-6 text-green-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
           </svg>
           <p class="font-medium">Vehicle added successfully! Redirecting to dashboard...</p>
         </div>
      </div>

      <!-- Error Message -->
      <div v-if="errorMessage" class="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-md">
        <!-- ... (error message content) ... -->
         <div class="flex items-center">
           <svg class="h-6 w-6 text-red-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
           </svg>
           <p class="font-medium">Failed to add vehicle: {{ errorMessage }}</p>
         </div>
      </div>

      <!-- Use VehicleForm component with backend proxy for VIN lookup -->
      <VehicleForm
        v-model:vehicle="formData"
        :isSubmitting="isSubmitting"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </main>
  </div>
</template>