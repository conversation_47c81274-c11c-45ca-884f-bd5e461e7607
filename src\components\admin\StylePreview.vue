<template>
  <div class="style-preview">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Style Preview</h3>

    <div class="border border-gray-300 rounded-lg overflow-hidden">
      <!-- Header Preview -->
      <div class="p-4" :style="{ backgroundColor: styling.colors.primary }">
        <div class="flex items-center">
          <div class="flex items-center">
            <!-- Show logo if available, otherwise show text logo -->
            <div v-if="styling.media.logo" class="h-8 mr-2">
              <img :src="styling.media.logo" alt="Site Logo" class="h-full" />
            </div>
            <span v-else class="flex items-center text-xl" :style="{ fontFamily: styling.typography.headingFont }">
              <span class="px-2 py-1 mr-2" :style="{ backgroundColor: styling.colors.secondary, color: styling.colors.light }">GT</span>
              <span :style="{ color: styling.colors.light }">Motor Sports</span>
            </span>
          </div>
          <div class="ml-auto">
            <div class="flex space-x-4">
              <div class="w-12 h-2 rounded" :style="{ backgroundColor: styling.colors.light }"></div>
              <div class="w-12 h-2 rounded" :style="{ backgroundColor: styling.colors.light }"></div>
              <div class="w-12 h-2 rounded" :style="{ backgroundColor: styling.colors.light }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Hero Preview -->
      <div class="relative h-40 bg-gray-200 flex items-center justify-center overflow-hidden">
        <div v-if="styling.media.heroImage" class="absolute inset-0">
          <img :src="styling.media.heroImage" alt="Hero Image" class="w-full h-full object-cover" />
        </div>
        <div v-if="styling.media.heroVideo" class="absolute inset-0">
          <video :src="styling.media.heroVideo" autoplay muted loop class="w-full h-full object-cover"></video>
        </div>
        <div class="relative z-10 text-center p-4">
          <h2 class="text-2xl font-bold mb-2" :style="{
            color: styling.colors.light,
            fontFamily: styling.typography.headingFont,
            textShadow: '1px 1px 3px rgba(0, 0, 0, 0.8)'
          }">
            Welcome to GT Motor Sports
          </h2>
          <p class="text-sm" :style="{
            color: styling.colors.light,
            fontFamily: styling.typography.bodyFont,
            textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
          }">
            Your premier destination for quality vehicles
          </p>
          <button class="mt-2 px-4 py-1 text-sm font-medium" :style="{
            backgroundColor: styling.colors.secondary,
            color: styling.colors.light,
            borderRadius: styling.layout.buttonBorderRadius,
            fontFamily: styling.typography.bodyFont
          }">
            Explore Inventory
          </button>
        </div>
      </div>

      <!-- Content Preview -->
      <div class="p-4 bg-white">
        <h3 class="text-lg font-medium mb-2" :style="{
          color: styling.colors.dark,
          fontFamily: styling.typography.headingFont
        }">
          Featured Vehicles
        </h3>

        <div class="grid grid-cols-3 gap-4">
          <!-- Card 1 -->
          <div :style="{
            backgroundColor: styling.colors.light,
            borderRadius: styling.layout.cardBorderRadius,
            boxShadow: styling.layout.cardShadow === 'sm' ? '0 1px 2px rgba(0, 0, 0, 0.05)' :
                       styling.layout.cardShadow === 'md' ? '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' :
                       '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
          }">
            <div class="h-16 bg-gray-300 rounded-t" :style="{ borderTopLeftRadius: styling.layout.cardBorderRadius, borderTopRightRadius: styling.layout.cardBorderRadius }"></div>
            <div class="p-2">
              <div class="h-2 w-3/4 mb-1 rounded" :style="{ backgroundColor: styling.colors.primary }"></div>
              <div class="h-2 w-1/2 rounded" :style="{ backgroundColor: styling.colors.secondary }"></div>
            </div>
          </div>

          <!-- Card 2 -->
          <div :style="{
            backgroundColor: styling.colors.light,
            borderRadius: styling.layout.cardBorderRadius,
            boxShadow: styling.layout.cardShadow === 'sm' ? '0 1px 2px rgba(0, 0, 0, 0.05)' :
                       styling.layout.cardShadow === 'md' ? '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' :
                       '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
          }">
            <div class="h-16 bg-gray-300 rounded-t" :style="{ borderTopLeftRadius: styling.layout.cardBorderRadius, borderTopRightRadius: styling.layout.cardBorderRadius }"></div>
            <div class="p-2">
              <div class="h-2 w-3/4 mb-1 rounded" :style="{ backgroundColor: styling.colors.primary }"></div>
              <div class="h-2 w-1/2 rounded" :style="{ backgroundColor: styling.colors.secondary }"></div>
            </div>
          </div>

          <!-- Card 3 -->
          <div :style="{
            backgroundColor: styling.colors.light,
            borderRadius: styling.layout.cardBorderRadius,
            boxShadow: styling.layout.cardShadow === 'sm' ? '0 1px 2px rgba(0, 0, 0, 0.05)' :
                       styling.layout.cardShadow === 'md' ? '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' :
                       '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
          }">
            <div class="h-16 bg-gray-300 rounded-t" :style="{ borderTopLeftRadius: styling.layout.cardBorderRadius, borderTopRightRadius: styling.layout.cardBorderRadius }"></div>
            <div class="p-2">
              <div class="h-2 w-3/4 mb-1 rounded" :style="{ backgroundColor: styling.colors.primary }"></div>
              <div class="h-2 w-1/2 rounded" :style="{ backgroundColor: styling.colors.secondary }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Preview -->
      <div class="p-4" :style="{ backgroundColor: styling.colors.dark }">
        <div class="flex justify-between items-center">
          <div class="text-sm" :style="{ color: styling.colors.light, fontFamily: styling.typography.bodyFont }">
            © 2025 GT Motor Sports
          </div>
          <div class="flex space-x-2">
            <div class="w-4 h-4 rounded-full" :style="{ backgroundColor: styling.colors.secondary }"></div>
            <div class="w-4 h-4 rounded-full" :style="{ backgroundColor: styling.colors.accent }"></div>
            <div class="w-4 h-4 rounded-full" :style="{ backgroundColor: styling.colors.light }"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import stylingStore from '../../store/styling';

// Get styling from store
const styling = computed(() => stylingStore.styling);
</script>
