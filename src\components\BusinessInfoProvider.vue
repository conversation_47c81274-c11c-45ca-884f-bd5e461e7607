<template>
  <!-- This is a renderless component that provides business information to the entire application -->
  <slot
    :title="businessTitle"
    :description="businessDescription"
    :mission="businessMission"
    :vision="businessVision"
    :history="businessHistory"
    :slogan="businessSlogan"
    :logo="businessLogo"
    :reviews="businessReviews"
    :services="businessServices"
    :address="businessAddress"
    :phone="businessPhone"
    :email="businessEmail"
    :hours="businessHours"
    :social="businessSocial"
    :isLoading="isLoading"
  ></slot>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import businessStore from '../store/business';

// Initialize the business store if not already initialized
onMounted(async () => {
  if (!businessStore.isInitialized.value) {
    await businessStore.initStore();
  }
});

// Computed properties for business information
const businessTitle = computed(() => businessStore.getBusinessTitle.value);
const businessDescription = computed(() => businessStore.getBusinessDescription.value);
const businessMission = computed(() => businessStore.getBusinessMission.value);
const businessVision = computed(() => businessStore.getBusinessVision.value);
const businessHistory = computed(() => businessStore.getBusinessHistory.value);
const businessSlogan = computed(() => businessStore.getBusinessSlogan.value);
const businessLogo = computed(() => businessStore.getBusinessLogo.value);
const businessReviews = computed(() => businessStore.getBusinessReviews.value);
const businessServices = computed(() => businessStore.getBusinessServices.value);
const businessAddress = computed(() => businessStore.getBusinessAddress.value);
const businessPhone = computed(() => businessStore.getBusinessPhone.value);
const businessEmail = computed(() => businessStore.getBusinessEmail.value);
const businessHours = computed(() => businessStore.getBusinessHours.value);
const businessSocial = computed(() => businessStore.getBusinessSocial.value);
const isLoading = computed(() => businessStore.isLoading.value);
</script>