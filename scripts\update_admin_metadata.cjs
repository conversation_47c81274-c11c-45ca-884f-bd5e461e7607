// One-time script (JavaScript version) to update app_metadata for a pre-existing admin user
// This script sets the tenant_id in the user's app_metadata
// Run this in a secure environment with access to the Supabase service role key

// Import the Supabase client library using require (standard for basic Node.js scripts)
const { createClient } = require('@supabase/supabase-js');

// Actual User UID and Tenant ID (replace if needed, but seems you already did)
const userIdToUpdate = '64dbf91c-d906-4156-b8d4-bea2e3e69739';
const tenantIdToSet = 'b316264d-e086-42c8-b50f-1ce5134514ee';

// Environment variables for Supabase connection
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// --- Validation ---
if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.error('Error: Missing required environment variables SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1); // Exit if env vars are missing
}
// No need for the placeholder validation anymore if you've put in real IDs

// --- Main Async Function ---
async function updateUserMetadata() {
  console.log(`Updating app_metadata for user: ${userIdToUpdate}`);
  console.log(`Setting tenant_id to: ${tenantIdToSet}`);

  try {
    // Initialize Supabase Admin Client
    // Note: No 'as string' needed in JS
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Update the user's app_metadata
    const { data, error } = await supabase.auth.admin.updateUserById(
      userIdToUpdate,
      {
        app_metadata: { tenant_id: tenantIdToSet }
      }
    );

    if (error) {
      console.error('Error updating user metadata:', error.message);
      process.exit(1); // Exit on error
    }

    console.log('Success! User metadata updated:');
    console.log(data); // Log the returned user data object

  } catch (error) {
    // Catch any unexpected errors during execution
    console.error('Unexpected error:', error.message || error);
    process.exit(1); // Exit on unexpected error
  }
}

// --- Execute the function ---
updateUserMetadata();

// Usage instructions:
// 1. Install dependency: npm install @supabase/supabase-js
// 2. Ensure User ID and Tenant ID constants above are correct.
// 3. Set environment variables in your terminal:
//    - export SUPABASE_URL="https://..."
//    - export SUPABASE_SERVICE_ROLE_KEY="your_secret_key..."
// 4. Run the script using Node.js: node update_admin_metadata.js