<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue';
import businessStore from '../store/business';

// Animation states
const pageLoaded = ref(false);
const contactInfoVisible = ref(false);
const businessHoursVisible = ref(false);
const mapVisible = ref(false);
const faqVisible = ref(false);
const isLoadingBusiness = ref(true);
const mapLoaded = ref(false);
const mapElement = ref(null);
let googleMap = null;
let mapLoader = null;

// Initialize the business store if not already initialized
onMounted(async () => {
  pageLoaded.value = true;
  
  // Initialize business store if needed
  if (!businessStore.isInitialized.value) {
    await businessStore.initStore();
  }
  isLoadingBusiness.value = false;
  
  // Staggered animations
  setTimeout(() => { contactInfoVisible.value = true; }, 200);
  setTimeout(() => { businessHoursVisible.value = true; }, 400);
  setTimeout(() => { mapVisible.value = true; }, 800);
  setTimeout(() => { faqVisible.value = true; }, 1000);
  
  // Load Google Maps after map container is visible
  setTimeout(() => { loadGoogleMaps(); }, 1000);
});

// Clean up Google Maps instance when component is unmounted
onUnmounted(() => {
  if (mapLoader) {
    window.google = undefined;
  }
});

// Load Google Maps API
const loadGoogleMaps = () => {
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_KEY || 'AIzaSyAsDqMTaN5wiY-sofVCk-KP0IRSW29nRb8';
  
  if (window.google && window.google.maps) {
    initMap();
    return;
  }
  
  mapLoader = document.createElement('script');
  mapLoader.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initGoogleMap`;
  mapLoader.async = true;
  mapLoader.defer = true;
  
  // Define the callback function
  window.initGoogleMap = () => {
    initMap();
  };
  
  document.head.appendChild(mapLoader);
};

// Initialize the map
const initMap = () => {
  if (!mapElement.value) return;
  
  const businessAddress = businessStore.getBusinessAddress.value || '40 Hopewell Way NE, Unit 10, Calgary, AB, T3J 5H7';
  
  // Default coordinates for Calgary
  const defaultPosition = { lat: 51.0447, lng: -114.0719 };
  
  // Create the map
  googleMap = new window.google.maps.Map(mapElement.value, {
    zoom: 15,
    center: defaultPosition,
    mapTypeControl: false,
    streetViewControl: false,
    fullscreenControl: true,
    zoomControl: true,
    styles: [
      {
        "featureType": "all",
        "elementType": "geometry",
        "stylers": [{ "color": "#f8f8f8" }]
      },
      {
        "featureType": "all",
        "elementType": "labels.text.fill",
        "stylers": [{ "color": "#616161" }]
      },
      {
        "featureType": "all",
        "elementType": "labels.text.stroke",
        "stylers": [{ "color": "#f5f5f5" }]
      },
      {
        "featureType": "administrative.land_parcel",
        "elementType": "labels.text.fill",
        "stylers": [{ "color": "#bdbdbd" }]
      },
      {
        "featureType": "poi",
        "elementType": "geometry",
        "stylers": [{ "color": "#eeeeee" }]
      },
      {
        "featureType": "poi",
        "elementType": "labels.text.fill",
        "stylers": [{ "color": "#757575" }]
      },
      {
        "featureType": "poi.park",
        "elementType": "geometry",
        "stylers": [{ "color": "#e5e5e5" }]
      },
      {
        "featureType": "poi.park",
        "elementType": "labels.text.fill",
        "stylers": [{ "color": "#9e9e9e" }]
      },
      {
        "featureType": "road",
        "elementType": "geometry",
        "stylers": [{ "color": "#ffffff" }]
      },
      {
        "featureType": "road.arterial",
        "elementType": "labels.text.fill",
        "stylers": [{ "color": "#757575" }]
      },
      {
        "featureType": "road.highway",
        "elementType": "geometry",
        "stylers": [{ "color": "#dadada" }]
      },
      {
        "featureType": "road.highway",
        "elementType": "labels.text.fill",
        "stylers": [{ "color": "#616161" }]
      },
      {
        "featureType": "water",
        "elementType": "geometry",
        "stylers": [{ "color": "#e9e9e9" }]
      },
      {
        "featureType": "water",
        "elementType": "labels.text.fill",
        "stylers": [{ "color": "#9e9e9e" }]
      },
      {
        "featureType": "transit.line",
        "elementType": "geometry",
        "stylers": [{ "color": "#e5e5e5" }]
      }
    ]
  });
  
  // Use Geocoder to get coordinates from address
  const geocoder = new window.google.maps.Geocoder();
  geocoder.geocode({ address: businessAddress }, (results, status) => {
    if (status === 'OK' && results[0]) {
      const position = results[0].geometry.location;
      googleMap.setCenter(position);
      
      // Add a marker
      const marker = new window.google.maps.Marker({
        map: googleMap,
        position: position,
        animation: window.google.maps.Animation.DROP,
        icon: {
          path: window.google.maps.SymbolPath.CIRCLE,
          fillColor: '#E11D48', // Red accent color
          fillOpacity: 1,
          strokeColor: '#FFFFFF',
          strokeWeight: 2,
          scale: 8
        }
      });
      
      // Add info window
      const infoWindow = new window.google.maps.InfoWindow({
        content: `<div style="font-family: 'Montserrat', sans-serif; padding: 8px;">
                    <h3 style="font-weight: bold; margin-bottom: 5px; color: #1E293B;">GT Motorsports</h3>
                    <p style="margin: 0; color: #64748B;">${businessAddress}</p>
                  </div>`
      });
      
      marker.addListener('click', () => {
        infoWindow.open(googleMap, marker);
      });
      
      // Open info window by default
      infoWindow.open(googleMap, marker);
    }
  });
  
  mapLoaded.value = true;
};

// Contact information from business store
const contactInfo = computed(() => {
  const address = businessStore.getBusinessAddress.value || '40 Hopewell Way NE, Unit 10, Calgary, AB, T3J 5H7';
  const phone = businessStore.getBusinessPhone.value || '************';
  const email = businessStore.getBusinessEmail.value || '<EMAIL>';
  
  return [
    {
      title: 'Visit Our Showroom',
      content: address,
      icon: 'M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z M15 11a3 3 0 11-6 0 3 3 0 016 0z',
      link: `https://maps.google.com/?q=${encodeURIComponent(address)}`,
      linkText: 'Get Directions'
    },
    {
      title: 'Call Us',
      content: phone,
      icon: 'M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z',
      link: `tel:+1${phone.replace(/\D/g, '')}`,
      linkText: 'Call Now'
    },
    {
      title: 'Email Us',
      content: email,
      icon: 'M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
      link: `mailto:${email}`,
      linkText: 'Send Email'
    }
  ];
});

// Business hours from business store
const businessHours = computed(() => {
  const hours = businessStore.getBusinessHours.value;
  
  if (!hours || !hours.length) {
    // Fallback to default hours if not available
    return [
      { day: 'Monday', hours: '10:00 AM - 7:00 PM' },
      { day: 'Tuesday', hours: '10:00 AM - 7:00 PM' },
      { day: 'Wednesday', hours: '10:00 AM - 7:00 PM' },
      { day: 'Thursday', hours: '10:00 AM - 7:00 PM' },
      { day: 'Friday', hours: '10:00 AM - 6:00 PM' },
      { day: 'Saturday', hours: '10:00 AM - 6:00 PM' },
      { day: 'Sunday', hours: '10:00 AM - 3:00 PM' }
    ];
  }
  
  return hours.map(day => ({
    day: day.day,
    hours: day.open && day.close ? `${day.open} - ${day.close}` : 'Closed'
  }));
});

// FAQ items
const faqItems = ref([
  {
    question: 'Do you offer financing options?',
    answer: 'Yes, we offer a variety of financing options to suit your needs. Our finance specialists work with multiple lenders to secure competitive rates and flexible terms tailored to your specific situation.',
    isOpen: false
  },
  {
    question: 'Can I trade in my current vehicle?',
    answer: 'Absolutely! We accept trade-ins and offer fair market value for your vehicle. Our appraisal process is transparent, and we will provide you with a competitive offer based on current market conditions.',
    isOpen: false
  },
  {
    question: 'Do you offer warranty coverage?',
    answer: 'Yes, many of our vehicles come with remaining manufacturer warranty. We also offer extended warranty options for additional peace of mind. Our team can explain the coverage details and help you choose the right protection plan for your needs.',
    isOpen: false
  },
  {
    question: 'Can I test drive a vehicle before purchasing?',
    answer: 'Of course! We encourage test drives to ensure the vehicle meets your expectations. You can schedule a test drive by calling us, using our online form, or visiting our showroom in person.',
    isOpen: false
  },
  {
    question: 'Do you ship vehicles to other provinces?',
    answer: 'Yes, we can arrange shipping to anywhere in Canada. The cost will depend on the destination and shipping method. Please contact us for a shipping quote to your location.',
    isOpen: false
  }
]);

const toggleFaq = (index) => {
  faqItems.value[index].isOpen = !faqItems.value[index].isOpen;
};

// Animation methods
const beforeEnter = (el) => {
  el.style.opacity = 0;
  el.style.transform = 'translateY(30px)';
};

const enter = (el, done) => {
  const delay = el.dataset.index * 150 || 0;
  
  setTimeout(() => {
    el.style.transition = 'all 0.6s ease';
    el.style.opacity = 1;
    el.style.transform = 'translateY(0)';
  }, delay);
  
  setTimeout(done, delay + 600);
};
</script>

<template>
  <!-- Contact Information Section -->
  <section class="section bg-light">
    <div class="container-custom">
      <!-- Loading state -->
      <div v-if="isLoadingBusiness" class="text-center py-10">
        <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-16 w-16 mx-auto mb-3" />
        <p class="text-gray-500">Loading Contact Information...</p>
      </div>
      
      <transition
        appear
        name="fade-in"
        v-bind:css="false"
        v-on:before-enter="beforeEnter"
        v-on:enter="enter"
      >
        <div v-if="contactInfoVisible && !isLoadingBusiness" class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div 
            v-for="(item, index) in contactInfo" 
            :key="index"
            class="bg-white rounded-lg shadow-custom p-6 text-center animate-item"
            :style="{ animationDelay: index * 150 + 'ms' }"
          >
            <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
              </svg>
            </div>
            <h3 class="text-xl font-heading font-bold mb-2">{{ item.title }}</h3>
            <p class="text-gray-600 mb-4">{{ item.content }}</p>
            <a :href="item.link" class="text-secondary font-medium hover:underline">{{ item.linkText }}</a>
          </div>
        </div>
      </transition>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Business Hours -->
        <transition
          appear
          name="slide-up-fade"
          v-bind:css="false"
          v-on:before-enter="beforeEnter"
          v-on:enter="enter"
        >
          <div v-if="businessHoursVisible && !isLoadingBusiness" class="bg-white rounded-lg shadow-custom p-6 h-full animate-slide-up">
            <h2 class="text-2xl font-heading font-bold mb-6">Business Hours</h2>
            <div class="space-y-3">
              <div
                v-for="(item, index) in businessHours"
                :key="index"
                class="flex justify-between items-center pb-2 animate-item"
                :class="index !== businessHours.length - 1 ? 'border-b border-gray-200' : ''"
                :style="{ animationDelay: index * 100 + 'ms' }"
              >
                <span class="font-medium" :class="item.day === 'Sunday' ? 'text-secondary' : ''">{{ item.day }}</span>
                <span class="text-gray-600">{{ item.hours }}</span>
              </div>
            </div>
          </div>
        </transition>
        
        <!-- Map -->
        <transition
          appear
          name="slide-up-fade"
          v-bind:css="false"
          v-on:before-enter="beforeEnter"
          v-on:enter="enter"
        >
          <div v-if="mapVisible" class="bg-white rounded-lg shadow-custom p-6 overflow-hidden h-full animate-slide-up">
            <h2 class="text-2xl font-heading font-bold mb-6">Our Location</h2>
            <div class="aspect-video rounded-lg overflow-hidden bg-gray-200 relative">
              <!-- Map loading indicator -->
              <div v-if="!mapLoaded" class="w-full h-full flex items-center justify-center absolute inset-0 z-10 bg-gray-200">
                <div class="text-center">
                  <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-10 w-10 mx-auto mb-2" />
                  <p class="text-gray-500">Loading map...</p>
                </div>
              </div>
              <!-- Google Maps container -->
              <div ref="mapElement" class="w-full h-full"></div>
            </div>
            <div class="mt-4 text-center">
              <a
                :href="`https://maps.google.com/?q=${encodeURIComponent(businessStore.getBusinessAddress.value || '40 Hopewell Way NE, Unit 10, Calgary, AB, T3J 5H7')}`"
                target="_blank"
                rel="noopener noreferrer"
                class="text-secondary font-medium hover:underline hover:text-primary transition-colors duration-300"
              >
                Get Directions
              </a>
            </div>
          </div>
        </transition>
      </div>
    </div>
  </section>
  
  <!-- FAQ Section -->
  <section class="section bg-white">
    <div class="container-custom">
      <transition
        appear
        name="fade-in"
        v-bind:css="false"
        v-on:before-enter="beforeEnter"
        v-on:enter="enter"
      >
        <div v-if="faqVisible" class="animate-fade-in">
          <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-heading font-bold mb-4">Frequently Asked Questions</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
              Find answers to common questions about our vehicles and services.
            </p>
          </div>
          
          <div class="max-w-3xl mx-auto">
            <div 
              v-for="(item, index) in faqItems" 
              :key="index"
              class="mb-4 animate-item"
              :style="{ animationDelay: index * 150 + 'ms' }"
            >
              <button 
                @click="toggleFaq(index)"
                class="w-full flex justify-between items-center bg-light p-4 rounded-lg text-left focus:outline-none transition-all duration-300 hover:bg-light/80"
                :class="item.isOpen ? 'rounded-b-none' : ''"
              >
                <span class="font-heading font-bold">{{ item.question }}</span>
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  class="h-5 w-5 text-secondary transition-transform duration-300" 
                  :class="item.isOpen ? 'transform rotate-180' : ''"
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              <transition name="slide-down">
                <div 
                  v-if="item.isOpen"
                  class="bg-white p-4 rounded-b-lg border-t border-gray-200"
                >
                  <p class="text-gray-600">{{ item.answer }}</p>
                </div>
              </transition>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </section>
</template>

<style scoped>
/* Base animations */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* Slide down animation for FAQ answers */
.slide-down-enter-active, .slide-down-leave-active {
  transition: all 0.3s ease;
  max-height: 500px;
  overflow: hidden;
}
.slide-down-enter-from, .slide-down-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

/* Staggered animations for lists */
.animate-item {
  opacity: 0;
  animation: fadeInUp 0.6s ease forwards;
}

.animate-slide-up {
  animation: slideUp 0.8s ease forwards;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Hover effects */
a, button {
  transition: all 0.3s ease;
}

input, textarea {
  transition: all 0.3s ease;
}

input:focus, textarea:focus {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>