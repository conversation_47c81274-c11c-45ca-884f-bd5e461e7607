/**
 * Styling Store for GT Motorsports
 * This store manages the site styling settings including colors, typography, and media
 */
import { ref, reactive } from 'vue';
import { getSettingByKey, updateSettingByKey } from '../database/settingsService';
import { supabase } from '../utils/supabase';

// State
const isLoading = ref(false);
const isSaving = ref(false); // This should be false by default
const saveSuccess = ref(false);
const saveError = ref(false);
const errorMessage = ref('');

// Safety timeout reference to clear it if needed
let savingTimeoutId = null;

// Default styling configuration
const defaultStyling = {
  colors: {
    primary: '#1E293B', // Dark blue
    secondary: '#E11D48', // Red accent
    accent: '#0EA5E9', // Light blue
    light: '#F8FAFC', // Light background
    dark: '#0F172A', // Dark background
  },
  typography: {
    headingFont: 'Montserrat',
    bodyFont: 'Inter',
  },
  media: {
    heroImage: '',
    heroVideo: '',
    aboutImage: '',
    footerImage: '',
    logo: '',
  },
  layout: {
    cardBorderRadius: '0.5rem',
    cardShadow: 'md',
    buttonBorderRadius: '0.375rem',
  }
};

// Styling object
const styling = reactive({...defaultStyling});

// Initialize the store
const initStore = async () => {
  try {
    // Clear any existing safety timeout
    if (savingTimeoutId) {
      clearTimeout(savingTimeoutId);
      savingTimeoutId = null;
    }

    // Reset all state flags EXCEPT isLoading
    // We'll set isLoading separately to avoid race conditions
    isSaving.value = false; // Explicitly ensure this is false
    saveSuccess.value = false;
    saveError.value = false;
    errorMessage.value = '';

    // Now set loading state
    isLoading.value = true;

    // Skip bucket creation - it's causing RLS policy issues
    // We'll assume the bucket already exists or will be created by an admin

    // Load styling from settings
    const stylingFromDB = await getSettingByKey('site_styling');

    if (stylingFromDB) {
      // Merge with default styling to ensure all properties exist
      Object.keys(defaultStyling).forEach(key => {
        if (stylingFromDB[key]) {
          Object.assign(styling[key], stylingFromDB[key]);
        }
      });

      console.log('Styling loaded from database:', styling);
    } else {
      console.log('No styling found in database, using defaults');
      // Reset to defaults
      Object.assign(styling, defaultStyling);
    }
  } catch (error) {
    console.error('Error initializing styling store:', error);
  } finally {
    isLoading.value = false;

    // Double-check isSaving is still false after initialization
    if (isSaving.value) {
      console.warn('initStore: isSaving was true after initialization, forcing reset');
      isSaving.value = false;
    }
  }
};

// Save styling to database
const saveStyling = async () => {
  // Force reset isSaving state if it's already true (handles stuck state)
  if (isSaving.value) {
    console.warn('isSaving was already true, resetting before starting new save operation');
    isSaving.value = false;
  }

  // Clear any existing safety timeout
  if (savingTimeoutId) {
    clearTimeout(savingTimeoutId);
  }

  // Ensure isSaving is reset after a timeout in case something goes wrong
  savingTimeoutId = setTimeout(() => {
    if (isSaving.value) {
      console.warn('Safety timeout triggered: resetting isSaving state');
      isSaving.value = false;
      saveError.value = true;
      errorMessage.value = 'Save operation timed out. Please try again.';
      savingTimeoutId = null;
    }
  }, 8000); // 8 second safety timeout

  try {
    // Reset all status flags first
    saveSuccess.value = false;
    saveError.value = false;
    errorMessage.value = '';

    // Set saving state to true
    isSaving.value = true;

    console.log('Saving styling to database:', styling);

    // Call the service function to update the database
    const success = await Promise.race([
      updateSettingByKey('site_styling', styling),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database operation timed out')), 8000)
      )
    ]);

    if (success) {
      // Set success flag but don't rely on the timeout to clear it
      // The component will handle showing the success message
      saveSuccess.value = true;
      console.log('Styling saved successfully');

      // Clear success message after a very short time
      // This ensures it doesn't stay stuck in true state
      setTimeout(() => {
        saveSuccess.value = false;
      }, 100);
    } else {
      throw new Error('Failed to save styling settings');
    }

    return success;
  } catch (error) {
    console.error('Error saving styling settings:', error);
    saveError.value = true;
    errorMessage.value = error.message || 'An error occurred while saving styling settings';
    return false;
  } finally {
    // Clear the safety timeout
    if (savingTimeoutId) {
      clearTimeout(savingTimeoutId);
      savingTimeoutId = null;
    }

    // Always ensure all state flags are reset
    isSaving.value = false;
    // Ensure saveSuccess is also reset to prevent it from getting stuck
    saveSuccess.value = false;
    console.log('Save operation completed, all state flags reset');

    // Double-check after a small delay to ensure states are reset
    setTimeout(() => {
      if (isSaving.value || saveSuccess.value) {
        console.warn('State flags still true after save operation, forcing reset');
        isSaving.value = false;
        saveSuccess.value = false;
      }
    }, 100);
  }
};

// Upload media file to Supabase storage
const uploadMedia = async (file, section) => {
  try {
    if (!file) return null;

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      throw new Error('File size exceeds 5MB limit');
    }

    // Validate file type
    const fileExt = file.name.split('.').pop().toLowerCase();
    const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExt);
    const isVideo = ['mp4', 'webm', 'ogg'].includes(fileExt);

    if (!isImage && !isVideo) {
      throw new Error('Unsupported file type. Please upload an image or video file.');
    }

    // Create a unique filename
    const fileName = `${section}_${Date.now()}.${fileExt}`;
    const filePath = `site-styling/${fileName}`;

    try {
      console.log(`Attempting to upload file to site-media bucket: ${filePath}`);

      // Add a timeout to the upload operation
      const uploadPromise = supabase.storage
        .from('site-media')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      // Race the upload against a timeout
      const { error } = await Promise.race([
        uploadPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Upload operation timed out')), 10000)
        )
      ]);

      if (error) {
        console.error('Supabase storage upload error details:', error);

        // If we get a bucket not found error or RLS policy error, show a more user-friendly message
        if (error.message && (error.message.includes('bucket not found') ||
                             error.message.includes('row-level security policy'))) {
          console.warn('Storage bucket issue detected. Using placeholder instead.');
          // Return a placeholder URL instead of throwing an error
          return {
            path: null,
            url: `/placeholder_${section}.jpg`
          };
        }
        throw error;
      }

      console.log('Upload successful, getting public URL');

      // Get the public URL
      const { data: urlData } = supabase.storage
        .from('site-media')
        .getPublicUrl(filePath);

      console.log(`Generated public URL: ${urlData.publicUrl}`);

      return {
        path: filePath,
        url: urlData.publicUrl
      };
    } catch (uploadError) {
      console.error('Error uploading to Supabase storage:', uploadError);

      // Return a placeholder URL for development purposes
      console.log(`Using placeholder image for ${section}`);
      return {
        path: null,
        url: `/placeholder_${section}.jpg`
      };
    }
  } catch (error) {
    console.error('Error in uploadMedia function:', error);
    // Return placeholder instead of throwing to prevent UI disruption
    return {
      path: null,
      url: `/placeholder_${section}.jpg`
    };
  }
};

// Remove media from Supabase storage
const removeMedia = async (path) => {
  try {
    if (!path) return true;

    try {
      const { error } = await supabase.storage
        .from('site-media')
        .remove([path]);

      if (error) {
        // If we get a bucket not found error or RLS policy error, log it but don't throw
        if (error.message && (error.message.includes('bucket not found') ||
                             error.message.includes('row-level security policy'))) {
          console.warn('Media removal skipped: Storage bucket not accessible.');
          return true; // Return success anyway to allow the UI to update
        }
        throw error;
      }

      return true;
    } catch (removeError) {
      console.error('Error removing from Supabase storage:', removeError);
      // Return true anyway to allow the UI to update
      return true;
    }
  } catch (error) {
    console.error('Error in removeMedia function:', error);
    // Return true anyway to allow the UI to update
    return true;
  }
};

// Reset styling to defaults
const resetStyling = () => {
  Object.assign(styling, defaultStyling);
};

export default {
  styling,
  isLoading,
  isSaving,
  saveSuccess,
  saveError,
  errorMessage,
  initStore,
  saveStyling,
  uploadMedia,
  removeMedia,
  resetStyling
};
