# GT Motorsports Vehicle Inventory Database

This directory contains the database implementation for the GT Motorsports vehicle inventory. The database is designed to store and manage all vehicle data for the admin dashboard and website.

## Database Structure

The database uses IndexedDB for client-side storage and is structured with the following tables:

### Core Tables

1. **vehicles** - Core vehicle information
   - id (Primary Key, Auto-increment)
   - title (Full title: year make model)
   - year
   - make
   - model
   - trim
   - price
   - specialPrice
   - stockNumber
   - vin
   - description
   - createdAt
   - updatedAt

2. **vehicleDetails** - Technical specifications
   - id (Primary Key, Auto-increment)
   - vehicleId (Foreign Key to vehicles.id)
   - bodyStyle
   - doors
   - engine
   - engineSize
   - drivetrain
   - transmission
   - exteriorColor
   - interiorColor
   - passengers
   - fuelType
   - cityFuel
   - hwyFuel
   - mileage
   - odometer
   - odometerUnit

3. **vehicleFeatures** - List of features for each vehicle
   - id (Primary Key, Auto-increment)
   - vehicleId (Foreign Key to vehicles.id)
   - feature

4. **vehicleHighlights** - Key selling points
   - id (Primary Key, Auto-increment)
   - vehicleId (Foreign Key to vehicles.id)
   - highlight

5. **vehicleImages** - All images associated with a vehicle
   - id (Primary Key, Auto-increment)
   - vehicleId (Foreign Key to vehicles.id)
   - url
   - isPrimary
   - sortOrder

### Vehicle Type Tables

6. **vehicleTypes** - For categorizing different vehicle types
   - id (Primary Key, Auto-increment)
   - name
   - description

7. **vehicleTypeMap** - Maps vehicles to their types
   - vehicleId (Foreign Key to vehicles.id)
   - typeId (Foreign Key to vehicleTypes.id)
   - Primary Key (vehicleId, typeId)

### Vehicle-Specific Tables

8. **motorcycleDetails** - Motorcycle specific details
   - vehicleId (Primary Key, Foreign Key to vehicles.id)
   - engineCC
   - cylinders
   - topSpeed
   - weight
   - seatHeight

9. **boatDetails** - Boat specific details
   - vehicleId (Primary Key, Foreign Key to vehicles.id)
   - length
   - beam
   - draft
   - hullMaterial
   - engineHours
   - passengerCapacity

10. **atvDetails** - ATV specific details
    - vehicleId (Primary Key, Foreign Key to vehicles.id)
    - engineCC
    - wheelbase
    - groundClearance
    - fuelCapacity
    - rackCapacity

## Database API

The database API provides the following functions:

### `initDatabase()`

Initializes the database and creates the necessary tables if they don't exist.

```javascript
await database.initDatabase();
```

### `addVehicle(vehicleData)`

Adds a new vehicle to the database.

```javascript
const vehicleId = await database.addVehicle({
  title: '2023 Porsche 911 GT3',
  year: 2023,
  make: 'Porsche',
  model: '911 GT3',
  // ... other vehicle data
});
```

### `getAllVehicles()`

Gets all vehicles from the database.

```javascript
const vehicles = await database.getAllVehicles();
```

### `getVehicleById(id)`

Gets a vehicle by ID with all related data.

```javascript
const vehicle = await database.getVehicleById(1);
```

### `updateVehicle(id, vehicleData)`

Updates an existing vehicle in the database.

```javascript
await database.updateVehicle(1, {
  price: 229900,
  // ... other updated data
});
```

### `deleteVehicle(id)`

Deletes a vehicle from the database.

```javascript
await database.deleteVehicle(1);
```

## Integration with Vehicle Store

The database is integrated with the vehicle store (`src/store/vehicles.js`) to provide a seamless experience for the admin dashboard. The store uses the database API to fetch, add, update, and delete vehicles.

## Import Process

The import process (`src/importInventory.js`) uses the database API to add vehicles from the GTINV.md file to the database. The process parses the markdown file, extracts vehicle information, and adds each vehicle to the database.

## Vehicle Types

The database supports different types of vehicles, including cars, motorcycles, boats, and ATVs. Each type has its own specific details table to store type-specific information.

## Example Usage

```javascript
import database from './database';

// Initialize the database
await database.initDatabase();

// Add a new vehicle
const vehicleId = await database.addVehicle({
  title: '2023 Porsche 911 GT3',
  year: 2023,
  make: 'Porsche',
  model: '911 GT3',
  price: 229900,
  // ... other vehicle data
});

// Get all vehicles
const vehicles = await database.getAllVehicles();

// Get a vehicle by ID
const vehicle = await database.getVehicleById(vehicleId);

// Update a vehicle
await database.updateVehicle(vehicleId, {
  price: 219900,
  // ... other updated data
});

// Delete a vehicle
await database.deleteVehicle(vehicleId);