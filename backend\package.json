{"name": "backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "form-handler": "node form-handler.js", "form-handler:dev": "nodemon form-handler.js", "test-server": "node test-server.js", "test-server:dev": "nodemon test-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Backend server for handling Stability AI API calls and form submissions", "dependencies": {"@supabase/supabase-js": "^2.39.0", "axios": "^1.8.4", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "express-list-endpoints": "^7.1.1", "form-data": "^4.0.2"}, "devDependencies": {"nodemon": "^3.1.9"}}