<template>
  <div class="about-game-preview">
    <div
      class="preview-container"
      @click="navigateToAboutPage"
    >
      <!-- Preview Video -->
      <div class="preview-image">
        <!-- Using the GAMEPREVIEW.mp4 video as the background -->
        <video
          ref="videoRef"
          src="/GAMEPREVIEW.mp4"
          alt="About Page 3D Experience"
          class="preview-video"
          autoplay
          loop
          muted
          playsinline
        ></video>

        <!-- Fallback image if the video doesn't load -->
        <div v-if="useDefaultImage" class="fallback-preview">
          <div class="fallback-content">
            <h3>GT Motor Sports</h3>
            <p>Interactive 3D Timeline Experience</p>
            <div class="mt-4 text-sm opacity-80">Drive through our history in an immersive 3D showroom</div>
          </div>
        </div>

        <!-- Overlay with gradient -->
        <div class="preview-overlay"></div>
      </div>

      <!-- Play Button -->
      <div class="play-button-container">
        <div class="play-button">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z" />
          </svg>
        </div>
        <div class="play-text">Drive Through Our History</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const useDefaultImage = ref(false);
const videoRef = ref(null);

// Handle navigation to the about page
const navigateToAboutPage = () => {
  router.push('/about');
};

// Handle video loading error
const handleImageError = () => {
  useDefaultImage.value = true;
};

onMounted(() => {
  // Add a small performance optimization - lower priority for video loading
  if (videoRef.value && 'fetchpriority' in videoRef.value) {
    videoRef.value.fetchpriority = 'low';
  }
});
</script>

<style scoped>
.about-game-preview {
  width: 100%;
  margin: 2rem 0;
}

.preview-container {
  position: relative;
  width: 100%;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  aspect-ratio: 16 / 9;
  will-change: transform, box-shadow;
}

.preview-container:hover {
  transform: translateY(-5px) scale(1.01);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.preview-image {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  will-change: transform;
}

.fallback-preview {
  position: absolute;
  inset: 0;
  background-color: #454545;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.fallback-content h3 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.fallback-content p {
  font-size: 1.2rem;
  opacity: 0.8;
}

.preview-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.3) 50%, rgba(0, 0, 0, 0.1) 100%);
  transition: background 0.3s ease;
}

.preview-container:hover .preview-overlay {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.2) 100%);
}

.play-button-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
}

.play-button {
  width: 80px;
  height: 80px;
  background-color: rgba(225, 29, 72, 0.9); /* Using the secondary color from tailwind config */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 0 20px rgba(225, 29, 72, 0.5);
  will-change: transform, box-shadow;
}

.play-button svg {
  width: 40px;
  height: 40px;
  margin-left: 5px; /* Slight adjustment for the play icon */
}

.preview-container:hover .play-button {
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(225, 29, 72, 0.7);
}

.play-text {
  margin-top: 1rem;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  will-change: opacity, transform;
}


.preview-container:hover .play-text {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .play-button {
    width: 60px;
    height: 60px;
  }

  .play-button svg {
    width: 30px;
    height: 30px;
  }

  .play-text {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .play-button {
    width: 50px;
    height: 50px;
  }

  .play-button svg {
    width: 25px;
    height: 25px;
  }

  .play-text {
    font-size: 0.9rem;
  }
}
</style>
