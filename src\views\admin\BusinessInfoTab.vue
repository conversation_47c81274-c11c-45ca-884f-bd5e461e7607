<template>
  <div>
    <div v-if="isLoadingBusiness" class="text-center py-10">
      <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-16 w-16 mx-auto mb-3" />
      <p class="text-gray-500">Loading Business Information...</p>
    </div>
    <div v-else>
      <!-- <PERSON> Header -->
      <div class="mb-6 flex justify-between items-start">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">Business Information</h2>
          <p class="mt-1 text-sm text-gray-600">
            Manage your business information shown on the public website.
          </p>
        </div>
        <button
          type="button"
          @click="saveBusinessInfo"
          :disabled="isSavingBusiness"
          class="inline-flex justify-center py-2 px-4 border border-transparent shadow-md text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          <svg v-if="isSavingBusiness" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isSavingBusiness ? 'Saving...' : 'Save Business Information' }}
        </button>
      </div>

      <!-- Alert Messages -->
      <div v-if="saveSuccessBusiness" class="mb-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm" role="alert">
        <p class="font-medium">Success!</p>
        <p>Business information saved successfully.</p>
      </div>
      <div v-if="saveErrorBusiness" class="mb-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm" role="alert">
        <p class="font-medium">Error</p>
        <p>{{ errorMessageBusiness || 'An unknown error occurred.' }}</p>
      </div>

      <form @submit.prevent="saveBusinessInfo">
        <!-- About Section -->
        <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
          <div
            class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
            @click="toggleSection('about')"
          >
            <div>
              <h3 class="text-lg leading-6 font-semibold text-gray-900">About Information</h3>
              <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Basic information about your business.
              </p>
            </div>
            <div class="text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 transition-transform duration-200"
                :class="{'rotate-180': !expandedSections.about}"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
          <div v-if="expandedSections.about" class="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6">
              <div class="sm:col-span-6">
                <label for="business-title" class="block text-sm font-medium text-gray-800">Business Title</label>
                <div class="mt-1">
                  <input
                    type="text"
                    id="business-title"
                    v-model="businessInfo.about.title"
                    required
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
              </div>

              <div class="sm:col-span-6">
                <label for="business-slogan" class="block text-sm font-medium text-gray-800">Company Slogan</label>
                <div class="mt-1">
                  <input
                    type="text"
                    id="business-slogan"
                    v-model="businessInfo.about.slogan"
                    placeholder="Your company's slogan or tagline"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
              </div>

              <div class="sm:col-span-6">
                <label for="business-logo" class="block text-sm font-medium text-gray-800">Company Logo URL</label>
                <div class="mt-1">
                  <input
                    type="url"
                    id="business-logo"
                    v-model="businessInfo.about.logo"
                    placeholder="https://example.com/logo.png"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
                <p class="mt-1 text-xs text-gray-600">Enter the full URL to your company logo image</p>
              </div>

              <div class="sm:col-span-6">
                <label for="business-description" class="block text-sm font-medium text-gray-800">Description</label>
                <div class="mt-1 relative">
                  <textarea
                    id="business-description"
                    v-model="businessInfo.about.description"
                    rows="4"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  ></textarea>
                  <AiWritingAssistant
                    fieldType="description"
                    :currentValue="businessInfo.about.description"
                    @update:content="businessInfo.about.description = $event"
                  />
                </div>
              </div>

              <div class="sm:col-span-6">
                <label for="business-mission" class="block text-sm font-medium text-gray-800">Mission Statement</label>
                <div class="mt-1 relative">
                  <textarea
                    id="business-mission"
                    v-model="businessInfo.about.mission"
                    rows="3"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  ></textarea>
                  <AiWritingAssistant
                    fieldType="mission"
                    :currentValue="businessInfo.about.mission"
                    @update:content="businessInfo.about.mission = $event"
                  />
                </div>
              </div>

              <div class="sm:col-span-6">
                <label for="business-vision" class="block text-sm font-medium text-gray-800">Vision</label>
                <div class="mt-1 relative">
                  <textarea
                    id="business-vision"
                    v-model="businessInfo.about.vision"
                    rows="3"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  ></textarea>
                  <AiWritingAssistant
                    fieldType="vision"
                    :currentValue="businessInfo.about.vision"
                    @update:content="businessInfo.about.vision = $event"
                  />
                </div>
              </div>

              <div class="sm:col-span-6">
                <label for="business-history" class="block text-sm font-medium text-gray-800">Company History</label>
                <div class="mt-1 relative">
                  <textarea
                    id="business-history"
                    v-model="businessInfo.about.history"
                    rows="4"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  ></textarea>
                  <AiWritingAssistant
                    fieldType="history"
                    :currentValue="businessInfo.about.history"
                    @update:content="businessInfo.about.history = $event"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information Section -->
        <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
          <div
            class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
            @click="toggleSection('contact')"
          >
            <div>
              <h3 class="text-lg leading-6 font-semibold text-gray-900">Contact Information</h3>
              <p class="mt-1 max-w-2xl text-sm text-gray-500">
                How customers can reach your business.
              </p>
            </div>
            <div class="text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 transition-transform duration-200"
                :class="{'rotate-180': !expandedSections.contact}"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
          <div v-if="expandedSections.contact" class="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-6">
              <div class="sm:col-span-6">
                <label for="contact-address" class="block text-sm font-medium text-gray-800">Business Address</label>
                <div class="mt-1">
                  <input
                    type="text"
                    id="contact-address"
                    v-model="businessInfo.contact.address"
                    required
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
              </div>

              <div class="sm:col-span-3">
                <label for="contact-phone" class="block text-sm font-medium text-gray-800">Phone Number</label>
                <div class="mt-1">
                  <input
                    type="tel"
                    id="contact-phone"
                    v-model="businessInfo.contact.phone"
                    required
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
              </div>

              <div class="sm:col-span-3">
                <label for="contact-email" class="block text-sm font-medium text-gray-800">Email Address</label>
                <div class="mt-1">
                  <input
                    type="email"
                    id="contact-email"
                    v-model="businessInfo.contact.email"
                    required
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
              </div>

              <div class="sm:col-span-6">
                <h4 class="text-md font-semibold text-gray-900 mb-4">Business Hours</h4>
                <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-7">
                  <div v-for="(day, index) in businessInfo.contact.hours" :key="day.day" class="sm:col-span-1 bg-gray-50 p-3 rounded-md border border-gray-200">
                    <div class="text-sm font-semibold text-gray-800 mb-3 text-center border-b border-gray-200 pb-2">{{ day.day }}</div>
                    <div class="grid grid-cols-1 gap-y-2">
                      <div>
                        <label :for="`hours-open-${index}`" class="block text-xs font-medium text-gray-600 mb-1">Opening Time</label>
                        <input
                          type="text"
                          :id="`hours-open-${index}`"
                          v-model="day.open"
                          placeholder="9:00 AM"
                          class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                        />
                      </div>
                      <div>
                        <label :for="`hours-close-${index}`" class="block text-xs font-medium text-gray-600 mb-1">Closing Time</label>
                        <input
                          type="text"
                          :id="`hours-close-${index}`"
                          v-model="day.close"
                          placeholder="5:00 PM"
                          class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <p class="mt-4 text-xs text-gray-600">Leave both fields empty for closed days</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Reviews Section -->
        <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
          <div
            class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
            @click="toggleSection('reviews')"
          >
            <div>
              <h3 class="text-lg leading-6 font-semibold text-gray-900">Customer Reviews</h3>
              <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Testimonials from your customers.
              </p>
            </div>
            <div class="text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 transition-transform duration-200"
                :class="{'rotate-180': !expandedSections.reviews}"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
          <div v-if="expandedSections.reviews" class="border-t border-gray-200 px-4 py-5 sm:p-6">
            <!-- Review Category Tabs -->
            <div class="mb-6 border-b border-gray-200">
              <nav class="-mb-px flex space-x-8" aria-label="Review Categories">
                <button
                  type="button"
                  v-for="tab in reviewTabs"
                  :key="tab.id"
                  @click="activeReviewTab = tab.id"
                  :class="[
                    activeReviewTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                    'whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm transition-colors duration-200'
                  ]"
                >
                  {{ tab.name }}
                </button>
              </nav>
            </div>

            <div v-for="(review, index) in businessInfo.reviews[activeReviewTab]" :key="review.id" class="mb-8 pb-6 border-b border-gray-200 last:border-b-0 last:mb-0 last:pb-0">
              <div class="flex justify-between items-center mb-4">
                <h4 class="text-md font-medium text-gray-900">Review #{{ index + 1 }}</h4>
                <button 
                  type="button" 
                  @click="removeReview(review.id, activeReviewTab)"
                  class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Remove
                </button>
              </div>
              
              <div class="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-6">
                <div class="sm:col-span-3">
                  <label :for="`review-author-${review.id}`" class="block text-sm font-medium text-gray-800">Author Name</label>
                  <div class="mt-1">
                    <input
                      type="text"
                      :id="`review-author-${review.id}`"
                      v-model="review.author"
                      class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                    />
                  </div>
                </div>

                <div class="sm:col-span-3">
                  <label :for="`review-vehicle-${review.id}`" class="block text-sm font-medium text-gray-800">Vehicle Purchased</label>
                  <div class="mt-1">
                    <input
                      type="text"
                      :id="`review-vehicle-${review.id}`"
                      v-model="review.vehicle"
                      class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                    />
                  </div>
                </div>

                <div class="sm:col-span-2">
                  <label :for="`review-rating-${review.id}`" class="block text-sm font-medium text-gray-800">Rating (1-5)</label>
                  <div class="mt-1">
                    <select
                      :id="`review-rating-${review.id}`"
                      v-model="review.rating"
                      class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                    >
                      <option value="1">1 Star</option>
                      <option value="2">2 Stars</option>
                      <option value="3">3 Stars</option>
                      <option value="4">4 Stars</option>
                      <option value="5">5 Stars</option>
                    </select>
                  </div>
                </div>

                <div class="sm:col-span-6">
                  <label :for="`review-text-${review.id}`" class="block text-sm font-medium text-gray-800">Review Text</label>
                  <div class="mt-1">
                    <textarea
                      :id="`review-text-${review.id}`"
                      v-model="review.text"
                      rows="3"
                      class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-4">
              <button
                type="button"
                @click="() => addReview(activeReviewTab)"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Review
              </button>
            </div>
          </div>
        </div>

        <!-- Services Section -->
        <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
          <div
            class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
            @click="toggleSection('services')"
          >
            <div>
              <h3 class="text-lg leading-6 font-semibold text-gray-900">Services Offered</h3>
              <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Key services your business provides.
              </p>
            </div>
            <div class="text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 transition-transform duration-200"
                :class="{'rotate-180': !expandedSections.services}"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
          <div v-if="expandedSections.services" class="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div v-for="(service, index) in businessInfo.services" :key="service.id" class="mb-8 pb-6 border-b border-gray-200 last:border-b-0 last:mb-0 last:pb-0">
              <div class="flex justify-between items-center mb-4">
                <h4 class="text-md font-medium text-gray-900">Service #{{ index + 1 }}</h4>
                <button 
                  type="button" 
                  @click="removeService(service.id)"
                  class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Remove
                </button>
              </div>
              
              <div class="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-6">
                <div class="sm:col-span-4">
                  <label :for="`service-title-${service.id}`" class="block text-sm font-medium text-gray-800">Service Title</label>
                  <div class="mt-1">
                    <input
                      type="text"
                      :id="`service-title-${service.id}`"
                      v-model="service.title"
                      class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                    />
                  </div>
                </div>

                <div class="sm:col-span-2">
                  <label :for="`service-icon-${service.id}`" class="block text-sm font-medium text-gray-800">Icon Name</label>
                  <div class="mt-1">
                    <input
                      type="text"
                      :id="`service-icon-${service.id}`"
                      v-model="service.icon"
                      placeholder="e.g. car, wrench, tools"
                      class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                    />
                  </div>
                  <p class="mt-1 text-xs text-gray-600">Font Awesome icon name</p>
                </div>

                <div class="sm:col-span-6">
                  <label :for="`service-description-${service.id}`" class="block text-sm font-medium text-gray-800">Description</label>
                  <div class="mt-1">
                    <textarea
                      :id="`service-description-${service.id}`"
                      v-model="service.description"
                      rows="3"
                      class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-4">
              <button 
                type="button" 
                @click="addService"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Service
              </button>
            </div>
          </div>
        </div>

        <!-- Contact Information Section moved above -->

        <!-- Timeline Events Section -->
        <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
          <div
            class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
            @click="toggleSection('timeline')"
          >
            <div>
              <h3 class="text-lg leading-6 font-semibold text-gray-900">Timeline Events</h3>
              <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Historical events displayed on the About page timeline.
              </p>
            </div>
            <div class="text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 transition-transform duration-200"
                :class="{'rotate-180': !expandedSections.timeline}"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
          <div v-if="expandedSections.timeline" class="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div v-for="(event, index) in businessInfo.about.timeline" :key="event.id" class="mb-8 pb-6 border-b border-gray-200 last:border-b-0 last:mb-0 last:pb-0">
              <div class="flex justify-between items-center mb-4">
                <h4 class="text-md font-medium text-gray-900">Event #{{ index + 1 }}</h4>
                <button
                  type="button"
                  @click="removeTimelineEvent(event.id)"
                  class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Remove
                </button>
              </div>
              
              <div class="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-6">
                <div class="sm:col-span-2">
                  <label :for="`event-year-${event.id}`" class="block text-sm font-medium text-gray-800">Year</label>
                  <div class="mt-1">
                    <input
                      type="text"
                      :id="`event-year-${event.id}`"
                      v-model="event.year"
                      class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                    />
                  </div>
                </div>

                <div class="sm:col-span-6">
                  <label :for="`event-description-${event.id}`" class="block text-sm font-medium text-gray-800">Event Description</label>
                  <div class="mt-1">
                    <textarea
                      :id="`event-description-${event.id}`"
                      v-model="event.event"
                      rows="3"
                      class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                    ></textarea>
                  </div>
                </div>
                
                <div class="sm:col-span-6">
                  <label :for="`event-image-${event.id}`" class="block text-sm font-medium text-gray-800">Event Image</label>
                  <div class="mt-1 flex items-start space-x-4">
                    <div class="flex-1">
                      <div class="flex items-center justify-center w-full">
                        <label :for="`file-upload-${event.id}`" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                          <div v-if="!event.imageUrl && !isUploading[event.id]" class="flex flex-col items-center justify-center pt-5 pb-6">
                            <svg class="w-8 h-8 mb-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <p class="mb-1 text-sm text-gray-500">Click to upload image</p>
                            <p class="text-xs text-gray-500">PNG, JPG or GIF (Max 2MB)</p>
                          </div>
                          <div v-else-if="isUploading[event.id]" class="flex flex-col items-center justify-center pt-5 pb-6">
                            <img src="/REDGTWHEEL.png" alt="Uploading..." class="animate-spin h-8 w-8 mb-3" />
                            <p class="text-sm text-gray-500">Uploading image...</p>
                          </div>
                          <div v-else class="relative w-full h-full">
                            <img :src="event.imageUrl" alt="Timeline event image" class="absolute inset-0 w-full h-full object-cover rounded-lg" />
                            <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                              <p class="text-white text-sm">Click to change image</p>
                            </div>
                          </div>
                          <input
                            :id="`file-upload-${event.id}`"
                            type="file"
                            class="hidden"
                            accept="image/*"
                            @change="(e) => handleImageUpload(e, event.id)"
                          />
                        </label>
                      </div>
                    </div>
                    <div v-if="event.imageUrl" class="flex-shrink-0">
                      <button
                        type="button"
                        @click="removeTimelineImage(event.id)"
                        class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Remove Image
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-4">
              <button
                type="button"
                @click="addTimelineEvent"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Timeline Event
              </button>
            </div>
          </div>
        </div>

        <!-- Social Media Section -->
        <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
          <div
            class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
            @click="toggleSection('social')"
          >
            <div>
              <h3 class="text-lg leading-6 font-semibold text-gray-900">Social Media</h3>
              <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Your business's social media profiles.
              </p>
            </div>
            <div class="text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 transition-transform duration-200"
                :class="{'rotate-180': !expandedSections.social}"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
          <div v-if="expandedSections.social" class="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-6">
              <div class="sm:col-span-3">
                <label for="social-facebook" class="block text-sm font-medium text-gray-800">Facebook</label>
                <div class="mt-1">
                  <input
                    type="url"
                    id="social-facebook"
                    v-model="businessInfo.social.facebook"
                    placeholder="https://facebook.com/yourbusiness"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
              </div>
              
              <div class="sm:col-span-3">
                <label for="social-instagram" class="block text-sm font-medium text-gray-800">Instagram</label>
                <div class="mt-1">
                  <input
                    type="url"
                    id="social-instagram"
                    v-model="businessInfo.social.instagram"
                    placeholder="https://instagram.com/yourbusiness"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
              </div>
              
              <div class="sm:col-span-3">
                <label for="social-twitter" class="block text-sm font-medium text-gray-800">Twitter</label>
                <div class="mt-1">
                  <input
                    type="url"
                    id="social-twitter"
                    v-model="businessInfo.social.twitter"
                    placeholder="https://twitter.com/yourbusiness"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
              </div>
              
              <div class="sm:col-span-3">
                <label for="social-linkedin" class="block text-sm font-medium text-gray-800">LinkedIn</label>
                <div class="mt-1">
                  <input
                    type="url"
                    id="social-linkedin"
                    v-model="businessInfo.social.linkedin"
                    placeholder="https://linkedin.com/company/yourbusiness"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
              </div>
              
              <div class="sm:col-span-3">
                <label for="social-youtube" class="block text-sm font-medium text-gray-800">YouTube</label>
                <div class="mt-1">
                  <input
                    type="url"
                    id="social-youtube"
                    v-model="businessInfo.social.youtube"
                    placeholder="https://youtube.com/c/yourbusiness"
                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

      </form>
    </div>
  </div>
</template>

<script setup>
import businessStore from '../../store/business';
import { onMounted, ref, reactive } from 'vue';
import aboutImageStorage from '../../utils/aboutImageStorage';
import AiWritingAssistant from '../../components/admin/AiWritingAssistant.vue';

// Review tabs configuration
const reviewTabs = [
  { id: 'home', name: 'Home Page' },
  { id: 'financing', name: 'Financing' },
  { id: 'detailing', name: 'Detailing' }
];

// Active tab state
const activeReviewTab = ref('home');

// Track expanded sections - all collapsed by default
const expandedSections = reactive({
  about: false,
  contact: false,
  reviews: false,
  services: false,
  timeline: false,
  social: false
});

// Toggle section expansion
const toggleSection = (section) => {
  expandedSections[section] = !expandedSections[section];
};

// Initialize the business store if not already initialized
onMounted(async () => {
  if (!businessStore.isInitialized.value) {
    await businessStore.initStore();
  }
});

// Use the business store directly instead of props
const businessInfo = businessStore.businessInfo;
const isLoadingBusiness = businessStore.isLoading;
const isSavingBusiness = businessStore.isSaving;
const saveSuccessBusiness = businessStore.saveSuccess;
const saveErrorBusiness = businessStore.saveError;
const errorMessageBusiness = businessStore.errorMessage;
const saveBusinessInfo = businessStore.saveBusinessInfo;
const addReview = businessStore.addReview;
const removeReview = businessStore.removeReview;
const addService = businessStore.addService;
const removeService = businessStore.removeService;

// Track upload status for each timeline event
const isUploading = ref({});

// Timeline event functions
const addTimelineEvent = () => {
  // Initialize timeline array if it doesn't exist
  if (!businessInfo.about.timeline) {
    businessInfo.about.timeline = [];
  }
  
  const newId = businessInfo.about.timeline.length > 0
    ? Math.max(0, ...businessInfo.about.timeline.map(e => e.id || 0)) + 1
    : 1;
  
  businessInfo.about.timeline.push({
    id: newId,
    year: '',
    event: '',
    imageUrl: '',
    image_path: ''
  });
};

const removeTimelineEvent = (id) => {
  if (!businessInfo.about.timeline) return;
  
  const index = businessInfo.about.timeline.findIndex(e => e.id === id);
  if (index !== -1) {
    // Get the event data
    const event = businessInfo.about.timeline[index];
    
    // If there's an image, delete it from storage
    if (event.imageUrl) {
      // Delete the image using the URL
      aboutImageStorage.deleteTimelineImage(event.imageUrl)
        .catch(error => console.error('Error deleting timeline image by URL:', error));
    }
    
    // If there's a year, also try to delete by year pattern to ensure all related files are removed
    if (event.year) {
      aboutImageStorage.deleteTimelineImageByYear(event.year)
        .catch(error => console.error('Error deleting timeline image by year:', error));
    }
    
    businessInfo.about.timeline.splice(index, 1);
  } else {
    console.warn(`Attempted to remove timeline event with non-existent ID: ${id}`);
  }
};

// Handle image upload for timeline events
const handleImageUpload = async (event, eventId) => {
  const file = event.target.files[0];
  if (!file) return;
  
  // Validate file size (max 2MB)
  if (file.size > 2 * 1024 * 1024) {
    alert('File size exceeds 2MB limit. Please choose a smaller file.');
    return;
  }
  
  // Find the timeline event to get the year
  const eventIndex = businessInfo.about.timeline.findIndex(e => e.id === eventId);
  if (eventIndex === -1) {
    alert('Timeline event not found.');
    return;
  }
  
  const timelineEvent = businessInfo.about.timeline[eventIndex];
  if (!timelineEvent.year) {
    alert('Please enter a year for this timeline event before uploading an image.');
    return;
  }
  
  // Set uploading state
  isUploading.value = { ...isUploading.value, [eventId]: true };
  
  try {
    // Upload the image to Supabase storage with the year parameter
    const result = await aboutImageStorage.uploadTimelineImage(file, eventId, timelineEvent.year);
    
    if (result && result.publicUrl) {
      // Update the timeline event with the image URL and image path
      businessInfo.about.timeline[eventIndex].imageUrl = result.publicUrl;
      businessInfo.about.timeline[eventIndex].image_path = result.imagePath;
    } else {
      alert('Failed to upload image. Please try again.');
    }
  } catch (error) {
    console.error('Error uploading timeline image:', error);
    alert('An error occurred while uploading the image.');
  } finally {
    // Clear uploading state
    const { [eventId]: _, ...rest } = isUploading.value;
    isUploading.value = rest;
  }
};

// Remove image from a timeline event
const removeTimelineImage = async (eventId) => {
  const eventIndex = businessInfo.about.timeline.findIndex(e => e.id === eventId);
  if (eventIndex === -1) return;
  
  const event = businessInfo.about.timeline[eventIndex];
  if (!event.imageUrl) return;
  
  try {
    // Delete the image from storage
    await aboutImageStorage.deleteTimelineImage(event.imageUrl);
    
    // Clear the imageUrl and image_path from the event
    businessInfo.about.timeline[eventIndex].imageUrl = '';
    businessInfo.about.timeline[eventIndex].image_path = '';
  } catch (error) {
    console.error('Error removing timeline image:', error);
    alert('An error occurred while removing the image.');
  }
};
</script>