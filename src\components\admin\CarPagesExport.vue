<template>
  <div class="bg-white shadow rounded-lg p-6 mb-6">
    <h2 class="text-xl font-semibold mb-4">CarPages Export</h2>
    
    <p class="mb-4">
      Export your vehicle inventory to CarPages.ca. This will generate a CSV file and upload it to the CarPages FTP server.
      <span class="font-medium text-blue-600">CarPages recommends daily or weekly updates rather than real-time updates.</span>
    </p>
    
    <div class="flex items-center mb-4">
      <div class="flex-1">
        <p class="text-sm text-gray-600">
          Last export: {{ lastExportDate ? formatDate(lastExportDate) : 'Never' }}
        </p>
      </div>
      
      <div class="flex space-x-2">
        <button
          @click="triggerExport"
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
          :disabled="isExporting"
        >
          <span v-if="isExporting">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Exporting...
          </span>
          <span v-else>Export Sample Data</span>
        </button>
        
        <button
          @click="triggerSupabaseExport"
          class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
          :disabled="isExporting"
        >
          <span v-if="isExporting">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Exporting...
          </span>
          <span v-else>Export Supabase Data</span>
        </button>
      </div>
    </div>
    
    <div v-if="exportMessage" :class="['p-3 rounded text-sm', exportSuccess ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800']">
      {{ exportMessage }}
    </div>
    
    <div class="mt-4 border-t pt-4">
      <h3 class="font-medium mb-2">Export Details</h3>
      <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
        <li>Exports all active vehicle listings to CarPages</li>
        <li>Includes vehicle details, features, and images</li>
        <li>Automatically maps your data to CarPages format</li>
        <li>Uploads via FTP to ftp.carpages.ca</li>
      </ul>
      
      <div class="mt-4 bg-green-50 p-4 rounded-lg border border-green-200">
        <h3 class="font-medium mb-2 text-green-800">Scheduled Exports (Recommended by CarPages)</h3>
        <p class="text-sm text-gray-700">
          <span class="text-green-600 font-medium">✓ BEST PRACTICE:</span> Set up automatic daily exports using the scheduler:
        </p>
        <ul class="list-disc pl-5 text-sm text-gray-700 space-y-1 mt-2">
          <li>Run <code class="bg-white px-1 rounded">schedule-export.bat</code> in the project root directory</li>
          <li>Or set up a scheduled task using Windows Task Scheduler</li>
          <li>See <a href="#" @click.prevent="openReadme" class="text-blue-600 hover:underline">documentation</a> for details</li>
        </ul>
        <p class="text-sm text-gray-700 mt-2 italic">
          According to the CarPages Data Provider Integration Guide, inventory updates are expected on a "daily or weekly basis" rather than in real-time.
        </p>
      </div>
      
      <!-- Auto-Export Toggle Section -->
      <div class="mt-4 border-t pt-4">
        <h3 class="font-medium mb-2">Auto-Export on Inventory Changes (Use Sparingly)</h3>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">
              Automatically export to CarPages when inventory is added, updated, or deleted.
            </p>
            <p class="text-xs text-gray-500 mt-1">
              <span class="text-amber-600 font-medium">⚠️ NOTE:</span> This sends your entire inventory to CarPages with each change. For most dealers, the scheduled daily export is more efficient.
            </p>
          </div>
          
          <div class="ml-4">
            <label for="auto-export-toggle" class="inline-flex relative items-center cursor-pointer">
              <input
                type="checkbox"
                id="auto-export-toggle"
                class="sr-only"
                v-model="autoExportEnabled"
                @change="toggleAutoExport"
              >
              <div class="w-11 h-6 bg-gray-200 rounded-full border border-gray-200 toggle-bg"></div>
              <span class="ml-3 text-sm font-medium text-gray-900">{{ autoExportEnabled ? 'Enabled' : 'Disabled' }}</span>
            </label>
          </div>
        </div>
        
        <p class="text-xs text-gray-500 mt-2" v-if="autoExportEnabled">
          Auto-export is <span class="font-semibold text-green-600">enabled</span>. Changes to inventory will automatically trigger exports to CarPages. Use this only for critical real-time updates.
        </p>
        <p class="text-xs text-gray-500 mt-2" v-else>
          Auto-export is <span class="font-semibold text-blue-600">disabled</span>. Using scheduled daily exports is the recommended approach according to CarPages documentation.
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import autoExportUtils from '../../utils/autoExport';

export default {
  name: 'CarPagesExport',
  
  setup() {
    const isExporting = ref(false);
    const exportMessage = ref('');
    const exportSuccess = ref(false);
    const lastExportDate = ref(localStorage.getItem('lastCarPagesExport') || null);
    const autoExportEnabled = ref(false);
    
    // Initialize auto-export status on component mount
    onMounted(() => {
      autoExportEnabled.value = autoExportUtils.getAutoExportStatus();
    });
    
    // Toggle auto-export functionality
    const toggleAutoExport = () => {
      autoExportUtils.setAutoExportEnabled(autoExportEnabled.value);
    };
    
    const triggerExport = async () => {
      isExporting.value = true;
      exportMessage.value = '';
      
      try {
        // Call the backend API to trigger the export
        const response = await fetch('/api/carpages/export', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        const result = await response.json();
        
        if (result.success) {
          exportSuccess.value = true;
          exportMessage.value = 'Export triggered successfully! The server is processing your sample inventory for upload to CarPages.';
          lastExportDate.value = new Date().toISOString();
          localStorage.setItem('lastCarPagesExport', lastExportDate.value);
        } else {
          exportSuccess.value = false;
          exportMessage.value = `Export failed: ${result.message || 'Unknown error'}`;
        }
      } catch (error) {
        console.error('Error triggering CarPages export:', error);
        exportSuccess.value = false;
        exportMessage.value = `Export failed: ${error.message || 'Network error'}. You can run the export manually using the export-to-carpages.bat file.`;
      } finally {
        isExporting.value = false;
      }
    };
    
    const triggerSupabaseExport = async () => {
      isExporting.value = true;
      exportMessage.value = '';
      
      try {
        // Call the backend API to trigger the Supabase export
        const response = await fetch('/api/carpages/export-supabase', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        const result = await response.json();
        
        if (result.success) {
          exportSuccess.value = true;
          exportMessage.value = 'Supabase export triggered successfully! The server is processing your Supabase inventory for upload to CarPages.';
          lastExportDate.value = new Date().toISOString();
          localStorage.setItem('lastCarPagesExport', lastExportDate.value);
        } else {
          exportSuccess.value = false;
          exportMessage.value = `Supabase export failed: ${result.message || 'Unknown error'}`;
        }
      } catch (error) {
        console.error('Error triggering Supabase to CarPages export:', error);
        exportSuccess.value = false;
        exportMessage.value = `Supabase export failed: ${error.message || 'Network error'}. You can run the export manually using the export-supabase-to-carpages.bat file.`;
      } finally {
        isExporting.value = false;
      }
    };
    
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    };
    
    const openReadme = () => {
      // Open the README file in a new window
      window.open('/CPFTP/README.md', '_blank');
    };
    
    return {
      isExporting,
      exportMessage,
      exportSuccess,
      lastExportDate,
      autoExportEnabled,
      triggerExport,
      triggerSupabaseExport,
      toggleAutoExport,
      formatDate,
      openReadme
    };
  }
}
</script>

<style scoped>
/* Toggle Switch Styles */
.toggle-bg {
  position: relative;
  transition: background-color 0.2s;
}

.toggle-bg:after {
  content: '';
  position: absolute;
  top: 0.15rem;
  left: 0.2rem;
  width: 1.1rem;
  height: 1.1rem;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.2s;
}

input:checked + .toggle-bg {
  background-color: #10B981; /* Green color when enabled */
}

input:checked + .toggle-bg:after {
  transform: translateX(100%);
}

input:focus + .toggle-bg {
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.5); /* Green focus ring */
}
</style>