/**
 * Supabase Database implementation for GT Motorsports vehicle inventory
 * This file provides functions to interact with the Supabase database
 */
import { supabase } from '../lib/supabaseClient';

/**
 * Initialize the database connection and create tables if they don't exist
 * @returns {Promise} Promise that resolves when the database is ready
 */
export const initDatabase = async () => {
  try {
    // Check if the tables exist, if not create them
    // This is a simplified approach - in a production app, you might use migrations
    
    // Check if vehicles table exists
    const { error: checkError } = await supabase
      .from('vehicles')
      .select('id')
      .limit(1);
    
    // If table doesn't exist, create the necessary tables
    if (checkError && checkError.code === '42P01') { // PostgreSQL error code for undefined_table
      console.log('Tables do not exist, creating schema...');
      await createTables();
    } else if (checkError) {
      console.error('Error checking tables:', checkError);
      throw checkError;
    }
    
    console.log('Supabase database initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing Supabase database:', error);
    throw error;
  }
};

/**
 * Create the necessary tables in Supabase
 * @returns {Promise} Promise that resolves when tables are created
 */
const createTables = async () => {
  // Note: In Supabase, you would typically create tables through the web interface
  // or using migrations. This is a simplified approach for demonstration.
  
  // For this implementation, we'll assume the tables are already created in Supabase
  // with the appropriate schema. If they're not, you would need to create them
  // through the Supabase dashboard or using SQL migrations.
  
  console.log('Tables should be created through the Supabase dashboard.');
  console.log('Please ensure the following tables exist:');
  console.log('- vehicles');
  console.log('- vehicle_details');
  console.log('- vehicle_features');
  console.log('- vehicle_highlights');
  console.log('- vehicle_images');
  console.log('- vehicle_types');
  console.log('- vehicle_type_map');
  console.log('- motorcycle_details');
  console.log('- boat_details');
  console.log('- atv_details');
  
  return true;
};

/**
 * Add a vehicle to the database
 * @param {Object} vehicleData - The vehicle data to add
 * @returns {Promise} Promise that resolves with the new vehicle ID
 */
export const addVehicle = async (vehicleData) => {
  try {
    // Extract data for different tables
    const {
      // Core vehicle data
      title, year, make, model, trim, price, specialPrice, stockNumber, vin, description,
      
      // Vehicle details
      bodyStyle, doors, engine, engineSize, drivetrain, transmission, 
      exteriorColor, interiorColor, passengers, fuelType, cityFuel, hwyFuel, mileage,
      
      // Arrays
      features = [], highlights = [], gallery = [],
      
      // Special vehicle type details
      vehicleType, typeSpecificDetails = {}
    } = vehicleData;
    
    // 1. Add to vehicles table
    const { data: vehicleResult, error: vehicleError } = await supabase
      .from('vehicles')
      .insert({
        title, 
        year, 
        make, 
        model, 
        trim, 
        price, 
        special_price: specialPrice, 
        stock_number: stockNumber, 
        vin, 
        description,
        created_at: new Date(),
        updated_at: new Date()
      })
      .select();
    
    if (vehicleError) {
      console.error('Error adding vehicle:', vehicleError);
      throw vehicleError;
    }
    
    if (!vehicleResult || vehicleResult.length === 0) {
      throw new Error('Failed to add vehicle: No ID returned');
    }
    
    const vehicleId = vehicleResult[0].id;
    
    // 2. Add to vehicle_details table
    const { error: detailsError } = await supabase
      .from('vehicle_details')
      .insert({
        vehicle_id: vehicleId,
        body_style: bodyStyle, 
        doors, 
        engine, 
        engine_size: engineSize, 
        drivetrain, 
        transmission, 
        exterior_color: exteriorColor, 
        interior_color: interiorColor, 
        passengers, 
        fuel_type: fuelType, 
        city_fuel: cityFuel, 
        hwy_fuel: hwyFuel, 
        mileage,
        odometer: mileage,
        odometer_unit: 'km'
      });
    
    if (detailsError) {
      console.error('Error adding vehicle details:', detailsError);
      // Continue anyway, we don't want to fail the whole operation
    }
    
    // 3. Add features
    if (features.length > 0) {
      const featureRows = features.map(feature => ({
        vehicle_id: vehicleId,
        feature
      }));
      
      const { error: featuresError } = await supabase
        .from('vehicle_features')
        .insert(featureRows);
      
      if (featuresError) {
        console.error('Error adding vehicle features:', featuresError);
      }
    }
    
    // 4. Add highlights
    if (highlights.length > 0) {
      const highlightRows = highlights.map(highlight => ({
        vehicle_id: vehicleId,
        highlight
      }));
      
      const { error: highlightsError } = await supabase
        .from('vehicle_highlights')
        .insert(highlightRows);
      
      if (highlightsError) {
        console.error('Error adding vehicle highlights:', highlightsError);
      }
    }
    
    // 5. Add images
    if (gallery.length > 0) {
      const imageRows = gallery.map((imagePath, index) => ({
        vehicle_id: vehicleId,
        image_path: imagePath,  // Store the path to the image in the bucket
        is_primary: index === 0, // First image is primary
        sort_order: index
      }));
      
      const { error: imagesError } = await supabase
        .from('GTINV')  // Changed to your actual table name
        .insert(imageRows);
      
      if (imagesError) {
        console.error('Error adding vehicle images:', imagesError);
      }
    }
    
    // 6. Handle vehicle type specific details if provided
    if (vehicleType) {
      await handleVehicleTypeDetails(vehicleId, vehicleType, typeSpecificDetails);
    }
    
    return vehicleId;
  } catch (error) {
    console.error('Error in addVehicle:', error);
    throw error;
  }
};

/**
 * Handle vehicle type specific details
 * @param {number} vehicleId - The vehicle ID
 * @param {string} vehicleType - The vehicle type (motorcycle, boat, atv)
 * @param {Object} details - The type-specific details
 */
const handleVehicleTypeDetails = async (vehicleId, vehicleType, details) => {
  try {
    // 1. First ensure the vehicle type exists
    let typeId;
    
    // Check if type exists
    const { data: existingType, error: typeError } = await supabase
      .from('vehicle_types')
      .select('id')
      .eq('name', vehicleType)
      .single();
    
    if (typeError && typeError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking vehicle type:', typeError);
      throw typeError;
    }
    
    if (existingType) {
      typeId = existingType.id;
    } else {
      // Type doesn't exist, create it
      const { data: newType, error: createTypeError } = await supabase
        .from('vehicle_types')
        .insert({
          name: vehicleType,
          description: `${vehicleType.charAt(0).toUpperCase() + vehicleType.slice(1)} vehicle type`
        })
        .select();
      
      if (createTypeError) {
        console.error('Error creating vehicle type:', createTypeError);
        throw createTypeError;
      }
      
      typeId = newType[0].id;
    }
    
    // 2. Map vehicle to type
    const { error: mapError } = await supabase
      .from('vehicle_type_map')
      .insert({
        vehicle_id: vehicleId,
        type_id: typeId
      });
    
    if (mapError) {
      console.error('Error mapping vehicle to type:', mapError);
      throw mapError;
    }
    
    // 3. Add type-specific details
    await addTypeSpecificDetails(vehicleId, vehicleType, details);
    
  } catch (error) {
    console.error('Error in handleVehicleTypeDetails:', error);
    throw error;
  }
};

/**
 * Add type-specific details for a vehicle
 * @param {number} vehicleId - The vehicle ID
 * @param {string} vehicleType - The vehicle type
 * @param {Object} details - The type-specific details
 */
const addTypeSpecificDetails = async (vehicleId, vehicleType, details) => {
  try {
    let tableName;
    
    switch (vehicleType.toLowerCase()) {
      case 'motorcycle':
        tableName = 'motorcycle_details';
        break;
      case 'boat':
        tableName = 'boat_details';
        break;
      case 'atv':
        tableName = 'atv_details';
        break;
      default:
        return; // Unknown type, nothing to do
    }
    
    const { error } = await supabase
      .from(tableName)
      .insert({
        vehicle_id: vehicleId,
        ...details
      });
    
    if (error) {
      console.error(`Error adding ${vehicleType} details:`, error);
      throw error;
    }
  } catch (error) {
    console.error('Error in addTypeSpecificDetails:', error);
    throw error;
  }
};

/**
 * Get all vehicles from the database
 * @returns {Promise} Promise that resolves with an array of vehicles
 */
export const getAllVehicles = async () => {
  try {
    const { data, error } = await supabase
      .from('vehicles')
      .select('*');
    
    if (error) {
      console.error('Error getting vehicles:', error);
      throw error;
    }
    
    return data || [];
  } catch (error) {
    console.error('Error in getAllVehicles:', error);
    throw error;
  }
};

/**
 * Get a vehicle by ID with all related data
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle data
 */
export const getVehicleById = async (id) => {
  try {
    // 1. Get the vehicle
    const { data: vehicle, error: vehicleError } = await supabase
      .from('vehicles')
      .select('*')
      .eq('id', id)
      .single();
    
    if (vehicleError) {
      console.error('Error getting vehicle:', vehicleError);
      throw vehicleError;
    }
    
    if (!vehicle) {
      return null;
    }
    
    // 2. Get vehicle details
    const { data: details, error: detailsError } = await supabase
      .from('vehicle_details')
      .select('*')
      .eq('vehicle_id', id)
      .single();
    
    if (detailsError && detailsError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error getting vehicle details:', detailsError);
    }
    
    // 3. Get features
    const { data: features, error: featuresError } = await supabase
      .from('vehicle_features')
      .select('feature')
      .eq('vehicle_id', id);
    
    if (featuresError) {
      console.error('Error getting vehicle features:', featuresError);
    }
    
    // 4. Get highlights
    const { data: highlights, error: highlightsError } = await supabase
      .from('vehicle_highlights')
      .select('highlight')
      .eq('vehicle_id', id);
    
    if (highlightsError) {
      console.error('Error getting vehicle highlights:', highlightsError);
    }
    
    // 5. Get images
    const { data: images, error: imagesError } = await supabase
      .from('GTINV')  // Changed to your actual table name
      .select('*')
      .eq('vehicle_id', id)
      .order('sort_order', { ascending: true });
    
    if (imagesError) {
      console.error('Error getting vehicle images:', imagesError);
    }
    
    // Get public URLs for each image
    const imageUrls = await Promise.all((images || []).map(async (image) => {
      const { data: publicUrl } = await supabase
        .storage
        .from('car-images')  // Your bucket name
        .getPublicUrl(image.image_path);
      
      return {
        ...image,
        url: publicUrl.publicUrl
      };
    }));
    
    // 6. Get vehicle type
    const { data: typeMap, error: typeMapError } = await supabase
      .from('vehicle_type_map')
      .select('type_id')
      .eq('vehicle_id', id)
      .single();
    
    let vehicleType = null;
    
    if (!typeMapError && typeMap) {
      const { data: type, error: typeError } = await supabase
        .from('vehicle_types')
        .select('name')
        .eq('id', typeMap.type_id)
        .single();
      
      if (!typeError && type) {
        vehicleType = type.name;
      }
    }
    
    // 7. Get type-specific details if applicable
    let typeSpecificDetails = null;
    
    if (vehicleType) {
      typeSpecificDetails = await getTypeSpecificDetails(id, vehicleType);
    }
    
    // 8. Convert snake_case to camelCase for frontend compatibility
    const camelCaseVehicle = {
      id: vehicle.id,
      title: vehicle.title,
      year: vehicle.year,
      make: vehicle.make,
      model: vehicle.model,
      trim: vehicle.trim,
      price: vehicle.price,
      specialPrice: vehicle.special_price,
      stockNumber: vehicle.stock_number,
      vin: vehicle.vin,
      description: vehicle.description,
      createdAt: vehicle.created_at,
      updatedAt: vehicle.updated_at
    };
    
    const camelCaseDetails = details ? {
      bodyStyle: details.body_style,
      doors: details.doors,
      engine: details.engine,
      engineSize: details.engine_size,
      drivetrain: details.drivetrain,
      transmission: details.transmission,
      exteriorColor: details.exterior_color,
      interiorColor: details.interior_color,
      passengers: details.passengers,
      fuelType: details.fuel_type,
      cityFuel: details.city_fuel,
      hwyFuel: details.hwy_fuel,
      mileage: details.mileage,
      odometer: details.odometer,
      odometerUnit: details.odometer_unit
    } : {};
    
    // 9. Combine all data
    const completeVehicle = {
      ...camelCaseVehicle,
      ...camelCaseDetails,
      features: features ? features.map(f => f.feature) : [],
      highlights: highlights ? highlights.map(h => h.highlight) : [],
      gallery: imageUrls ? imageUrls.map(i => i.url) : [],
      image: imageUrls && imageUrls.length > 0 ? 
        (imageUrls.find(i => i.is_primary)?.url || imageUrls[0].url) : '',
      vehicleType,
      typeSpecificDetails
    };
    
    return completeVehicle;
  } catch (error) {
    console.error('Error in getVehicleById:', error);
    throw error;
  }
};

/**
 * Get type-specific details
 * @param {number} vehicleId - The vehicle ID
 * @param {string} typeName - The vehicle type name
 * @returns {Promise} Promise that resolves with the type-specific details
 */
const getTypeSpecificDetails = async (vehicleId, typeName) => {
  try {
    let tableName;
    
    switch (typeName.toLowerCase()) {
      case 'motorcycle':
        tableName = 'motorcycle_details';
        break;
      case 'boat':
        tableName = 'boat_details';
        break;
      case 'atv':
        tableName = 'atv_details';
        break;
      default:
        return null;
    }
    
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('vehicle_id', vehicleId)
      .single();
    
    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error(`Error getting ${typeName} details:`, error);
      throw error;
    }
    
    return data || null;
  } catch (error) {
    console.error('Error in getTypeSpecificDetails:', error);
    throw error;
  }
};

/**
 * Update a vehicle in the database
 * @param {number} id - The vehicle ID
 * @param {Object} vehicleData - The updated vehicle data
 * @returns {Promise} Promise that resolves when the update is complete
 */
export const updateVehicle = async (id, vehicleData) => {
  try {
    // First get the existing vehicle to compare changes
    const existingVehicle = await getVehicleById(id);
    
    if (!existingVehicle) {
      throw new Error(`Vehicle with ID ${id} not found`);
    }
    
    // Extract data for different tables
    const {
      // Core vehicle data
      title, year, make, model, trim, price, specialPrice, stockNumber, vin, description,
      
      // Vehicle details
      bodyStyle, doors, engine, engineSize, drivetrain, transmission, 
      exteriorColor, interiorColor, passengers, fuelType, cityFuel, hwyFuel, mileage,
      
      // Arrays
      features, highlights, gallery,
      
      // Special vehicle type details
      vehicleType, typeSpecificDetails
    } = vehicleData;
    
    // 1. Update vehicles table
    const { error: vehicleError } = await supabase
      .from('vehicles')
      .update({
        title: title || existingVehicle.title,
        year: year || existingVehicle.year,
        make: make || existingVehicle.make,
        model: model || existingVehicle.model,
        trim: trim !== undefined ? trim : existingVehicle.trim,
        price: price !== undefined ? price : existingVehicle.price,
        special_price: specialPrice !== undefined ? specialPrice : existingVehicle.specialPrice,
        stock_number: stockNumber || existingVehicle.stockNumber,
        vin: vin || existingVehicle.vin,
        description: description || existingVehicle.description,
        updated_at: new Date()
      })
      .eq('id', id);
    
    if (vehicleError) {
      console.error('Error updating vehicle:', vehicleError);
      throw vehicleError;
    }
    
    // 2. Update vehicle details
    if (bodyStyle !== undefined || doors !== undefined || engine !== undefined || 
        engineSize !== undefined || drivetrain !== undefined || transmission !== undefined || 
        exteriorColor !== undefined || interiorColor !== undefined || passengers !== undefined || 
        fuelType !== undefined || cityFuel !== undefined || hwyFuel !== undefined || mileage !== undefined) {
      
      // Check if details exist
      const { data: existingDetails, error: checkError } = await supabase
        .from('vehicle_details')
        .select('*')
        .eq('vehicle_id', id)
        .single();
      
      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
        console.error('Error checking vehicle details:', checkError);
        throw checkError;
      }
      
      const detailsData = {
        body_style: bodyStyle !== undefined ? bodyStyle : existingVehicle.bodyStyle,
        doors: doors !== undefined ? doors : existingVehicle.doors,
        engine: engine !== undefined ? engine : existingVehicle.engine,
        engine_size: engineSize !== undefined ? engineSize : existingVehicle.engineSize,
        drivetrain: drivetrain !== undefined ? drivetrain : existingVehicle.drivetrain,
        transmission: transmission !== undefined ? transmission : existingVehicle.transmission,
        exterior_color: exteriorColor !== undefined ? exteriorColor : existingVehicle.exteriorColor,
        interior_color: interiorColor !== undefined ? interiorColor : existingVehicle.interiorColor,
        passengers: passengers !== undefined ? passengers : existingVehicle.passengers,
        fuel_type: fuelType !== undefined ? fuelType : existingVehicle.fuelType,
        city_fuel: cityFuel !== undefined ? cityFuel : existingVehicle.cityFuel,
        hwy_fuel: hwyFuel !== undefined ? hwyFuel : existingVehicle.hwyFuel,
        mileage: mileage !== undefined ? mileage : existingVehicle.mileage,
        odometer: mileage !== undefined ? mileage : existingVehicle.odometer,
        odometer_unit: 'km'
      };
      
      if (existingDetails) {
        // Update existing details
        const { error: detailsError } = await supabase
          .from('vehicle_details')
          .update(detailsData)
          .eq('vehicle_id', id);
        
        if (detailsError) {
          console.error('Error updating vehicle details:', detailsError);
          throw detailsError;
        }
      } else {
        // Create new details
        const { error: detailsError } = await supabase
          .from('vehicle_details')
          .insert({
            vehicle_id: id,
            ...detailsData
          });
        
        if (detailsError) {
          console.error('Error creating vehicle details:', detailsError);
          throw detailsError;
        }
      }
    }
    
    // 3. Update features (delete all and re-add)
    if (features && features.length > 0) {
      // Delete existing features
      const { error: deleteError } = await supabase
        .from('vehicle_features')
        .delete()
        .eq('vehicle_id', id);
      
      if (deleteError) {
        console.error('Error deleting vehicle features:', deleteError);
        throw deleteError;
      }
      
      // Add new features
      const featureRows = features.map(feature => ({
        vehicle_id: id,
        feature
      }));
      
      const { error: featuresError } = await supabase
        .from('vehicle_features')
        .insert(featureRows);
      
      if (featuresError) {
        console.error('Error adding vehicle features:', featuresError);
        throw featuresError;
      }
    }
    
    // 4. Update highlights (delete all and re-add)
    if (highlights && highlights.length > 0) {
      // Delete existing highlights
      const { error: deleteError } = await supabase
        .from('vehicle_highlights')
        .delete()
        .eq('vehicle_id', id);
      
      if (deleteError) {
        console.error('Error deleting vehicle highlights:', deleteError);
        throw deleteError;
      }
      
      // Add new highlights
      const highlightRows = highlights.map(highlight => ({
        vehicle_id: id,
        highlight
      }));
      
      const { error: highlightsError } = await supabase
        .from('vehicle_highlights')
        .insert(highlightRows);
      
      if (highlightsError) {
        console.error('Error adding vehicle highlights:', highlightsError);
        throw highlightsError;
      }
    }
    
    // 5. Update images (delete all and re-add)
    if (gallery && gallery.length > 0) {
      // Delete existing images
      const { error: deleteError } = await supabase
        .from('GTINV')  // Changed to your actual table name
        .delete()
        .eq('vehicle_id', id);
      
      if (deleteError) {
        console.error('Error deleting vehicle images:', deleteError);
        throw deleteError;
      }
      
      // Add new images
      const imageRows = gallery.map((imagePath, index) => ({
        vehicle_id: id,
        image_path: imagePath,
        is_primary: index === 0,
        sort_order: index
      }));
      
      const { error: imagesError } = await supabase
        .from('GTINV')  // Changed to your actual table name
        .insert(imageRows);
      
      if (imagesError) {
        console.error('Error adding vehicle images:', imagesError);
        throw imagesError;
      }
    }
    
    // 6. Update vehicle type if changed
    if (vehicleType && vehicleType !== existingVehicle.vehicleType) {
      // Delete existing type mappings
      const { error: deleteError } = await supabase
        .from('vehicle_type_map')
        .delete()
        .eq('vehicle_id', id);
      
      if (deleteError) {
        console.error('Error deleting vehicle type mappings:', deleteError);
        throw deleteError;
      }
      
      // Add new type mapping
      await handleVehicleTypeDetails(id, vehicleType, typeSpecificDetails || {});
    } else if (typeSpecificDetails && Object.keys(typeSpecificDetails).length > 0) {
      // Just update the type-specific details
      await updateTypeSpecificDetails(id, existingVehicle.vehicleType, typeSpecificDetails);
    }
    
    return id;
  } catch (error) {
    console.error('Error in updateVehicle:', error);
    throw error;
  }
};

/**
 * Update type-specific details
 * @param {number} vehicleId - The vehicle ID
 * @param {string} vehicleType - The vehicle type
 * @param {Object} details - The updated details
 */
const updateTypeSpecificDetails = async (vehicleId, vehicleType, details) => {
  try {
    if (!vehicleType) return;
    
    let tableName;
    
    switch (vehicleType.toLowerCase()) {
      case 'motorcycle':
        tableName = 'motorcycle_details';
        break;
      case 'boat':
        tableName = 'boat_details';
        break;
      case 'atv':
        tableName = 'atv_details';
        break;
      default:
        return;
    }
    
    // Check if details exist
    const { data: existingDetails, error: checkError } = await supabase
      .from(tableName)
      .select('*')
      .eq('vehicle_id', vehicleId)
      .single();
    
    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error(`Error checking ${vehicleType} details:`, checkError);
      throw checkError;
    }
    
    if (existingDetails) {
      // Update existing details
      const { error: updateError } = await supabase
        .from(tableName)
        .update(details)
        .eq('vehicle_id', vehicleId);
      
      if (updateError) {
        console.error(`Error updating ${vehicleType} details:`, updateError);
        throw updateError;
      }
    } else {
      // Create new details
      const { error: insertError } = await supabase
        .from(tableName)
        .insert({
          vehicle_id: vehicleId,
          ...details
        });
      
      if (insertError) {
        console.error(`Error creating ${vehicleType} details:`, insertError);
        throw insertError;
      }
    }
  } catch (error) {
    console.error('Error in updateTypeSpecificDetails:', error);
    throw error;
  }
};

/**
 * Delete a vehicle from the database
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves when the delete is complete
 */
export const deleteVehicle = async (id) => {
  try {
    // In Supabase with RLS and foreign key constraints, deleting the main record
    // should cascade to related tables if set up properly
    const { error } = await supabase
      .from('vehicles')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error('Error deleting vehicle:', error);
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('Error in deleteVehicle:', error);
    throw error;
  }
};

export default {
  initDatabase,
  addVehicle,
  getAllVehicles,
  getVehicleById,
  updateVehicle,
  deleteVehicle
};