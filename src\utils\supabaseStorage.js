/**
 * Supabase Storage Service
 * This file provides functions to interact with Supabase Storage for image uploads
 */
import { v4 as uuidv4 } from 'uuid';
import { createClient } from '@supabase/supabase-js';

// Supabase Configuration
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || 'https://wjqlfcxgrdfyqpjsbnyp.supabase.co';
const SUPABASE_KEY = import.meta.env.VITE_SUPABASE_KEY;
const SUPABASE_SERVICE_KEY = import.meta.env.VITE_SUPABASE_SERVICE_KEY;

// Use service key if available, otherwise use anon key
const API_KEY = SUPABASE_SERVICE_KEY || SUPABASE_KEY;

// The bucket name where vehicle images will be stored
const VEHICLE_IMAGES_BUCKET = 'car-images';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, API_KEY);

/**
 * Initialize the storage
 * This function is kept for API compatibility but doesn't need to create buckets
 * as the bucket should already be created in Supabase
 * @returns {Promise<boolean>} Promise that resolves to true if initialization was successful
 */
export const initStorage = async () => {
  try {
    console.log('Initializing storage...');
    
    // Verify credentials by making a simple API call
    const { error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('Error verifying Supabase credentials:', error);
      console.error('Error details:', error.message);
      return false;
    }
    
    console.log('Storage initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing storage:', error);
    console.error('Error details:', error.message);
    return false;
  }
};

/**
 * Upload an image to S3 Storage
 * @param {File} file - The file to upload
 * @param {string} vehicleId - The ID of the vehicle the image belongs to
 * @param {boolean} isPrimary - Whether this is the primary image for the vehicle
 * @returns {Promise<string|null>} Promise that resolves to the public URL of the uploaded image, or null if upload failed
 */
export const uploadVehicleImage = async (file, vehicleId, isPrimary = false) => {
  try {
    console.log(`Uploading image for vehicle ${vehicleId}, isPrimary: ${isPrimary}`);
    console.log(`File name: ${file.name}, File size: ${file.size}, File type: ${file.type}`);
    
    // Initialize storage if needed
    const storageInitialized = await initStorage();
    if (!storageInitialized) {
      console.error('Failed to initialize storage');
      return null;
    }
    
    // Sanitize the file name to remove special characters
    const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.]/g, '_');
    console.log(`Sanitized file name: ${sanitizedFileName}`);
    
    // Generate a more descriptive filename
    const fileExt = sanitizedFileName.split('.').pop().toLowerCase();
    
    // Use 'main' for primary image, or a timestamp-based name for other images
    const fileBaseName = isPrimary ? 'main' : `image-${Date.now().toString().slice(-6)}`;
    
    // Create the full path: folder/filename.ext
    const fileName = `${vehicleId}/${fileBaseName}.${fileExt}`;
    
    console.log(`Generated filename: ${fileName}`);
    
    try {
      // Upload the file to Supabase Storage
      const { data, error } = await supabase.storage
        .from(VEHICLE_IMAGES_BUCKET)
        .upload(fileName, file, {
          contentType: file.type || `image/${fileExt}`,
          cacheControl: '3600',
          upsert: true
        });
      
      if (error) {
        console.error('Error uploading file:', error);
        console.error('Error details:', error.message);
        return null;
      }
      
      console.log('Upload successful:', data);
      
      // Get the public URL
      const { data: publicUrlData } = supabase.storage
        .from(VEHICLE_IMAGES_BUCKET)
        .getPublicUrl(fileName);
      
      console.log(`Generated public URL: ${publicUrlData.publicUrl}`);
      
      return publicUrlData.publicUrl;
    } catch (uploadError) {
      console.error('Error processing file upload:', uploadError);
      console.error('Error details:', uploadError.message);
      return null;
    }
  } catch (error) {
    console.error('Error in uploadVehicleImage:', error);
    console.error('Error details:', error.message);
    
    return null;
  }
};

/**
 * Delete an image from S3 Storage
 * @param {string} url - The public URL of the image to delete
 * @returns {Promise<boolean>} Promise that resolves to true if deletion was successful
 */
export const deleteVehicleImage = async (url) => {
  try {
    // Extract the path from the URL
    // The URL format is: https://<project-id>.supabase.co/storage/v1/object/public/<bucket>/<path>
    const urlParts = url.split(`/public/${VEHICLE_IMAGES_BUCKET}/`);
    if (urlParts.length < 2) {
      console.error('Invalid image URL format:', url);
      return false;
    }
    
    const path = urlParts[1];
    
    // Delete the file from Supabase Storage
    const { error } = await supabase.storage
      .from(VEHICLE_IMAGES_BUCKET)
      .remove([path]);
    
    if (error) {
      console.error('Error deleting file:', error);
      console.error('Error details:', error.message);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in deleteVehicleImage:', error);
    return false;
  }
};

/**
 * Delete all images for a vehicle
 * @param {string} vehicleId - The ID of the vehicle
 * @returns {Promise<boolean>} Promise that resolves to true if deletion was successful
 */
export const deleteAllVehicleImages = async (vehicleId) => {
  try {
    // List all files in the vehicle's folder
    const { data, error } = await supabase.storage
      .from(VEHICLE_IMAGES_BUCKET)
      .list(`${vehicleId}/`);
    
    if (error) {
      console.error('Error listing files:', error);
      console.error('Error details:', error.message);
      return false;
    }
    
    if (!data || data.length === 0) {
      // No images to delete
      return true;
    }
    
    // Get paths of all files to delete
    const filesToDelete = data.map(file => `${vehicleId}/${file.name}`);
    
    // Delete all files in one operation
    const { error: deleteError } = await supabase.storage
      .from(VEHICLE_IMAGES_BUCKET)
      .remove(filesToDelete);
    
    if (deleteError) {
      console.error('Error deleting files:', deleteError);
      console.error('Error details:', deleteError.message);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in deleteAllVehicleImages:', error);
    return false;
  }
};

export default {
  initStorage,
  uploadVehicleImage,
  deleteVehicleImage,
  deleteAllVehicleImages
};
