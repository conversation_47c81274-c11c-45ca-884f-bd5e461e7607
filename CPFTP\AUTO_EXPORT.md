# Automatic CarPages Export

This document describes the automatic export functionality that sends vehicle inventory data to the CarPages FTP bucket whenever a vehicle is added, updated, or deleted.

## Overview

The system can automatically trigger a CarPages export whenever a significant change is made to the vehicle inventory. However, according to the official CarPages Data Provider Integration Guide, inventory updates are expected on a "daily or weekly basis" rather than in real-time.

**IMPORTANT:** The recommended approach is to use scheduled daily exports rather than real-time auto-exports.

## How It Works

1. **Trigger Points**: The export is automatically triggered at the following points:
   - When a new vehicle is added to the inventory
   - When a vehicle is updated with significant changes
   - When a vehicle is deleted from the inventory

2. **Significant Changes**: For updates, only changes to fields that would affect the CarPages listing will trigger an export. These include:
   - Basic vehicle information (year, make, model, trim)
   - Pricing information (price, special price)
   - Vehicle details (mileage, VIN, stock number, body style, etc.)
   - Features and descriptions
   - Images

3. **Asynchronous Processing**: The export process runs asynchronously in the background, so it doesn't block the user interface. This means users can continue working while the export is in progress.

4. **Error Handling**: If an error occurs during the export process, it is logged but doesn't affect the vehicle operation. This ensures that vehicle management operations complete successfully even if the export fails.

## Configuration

The auto-export functionality can be enabled or disabled through the admin dashboard. By default, it is disabled, which is the recommended setting for most dealers.

### Using the Admin Dashboard Toggle

1. Navigate to the admin dashboard
2. Go to the CarPages Export section
3. Find the "Auto-Export on Inventory Changes" toggle
4. Click the toggle to enable or disable the feature

The setting is stored in your browser's localStorage, so it will persist between sessions on the same device.

### Throttling

To prevent excessive exports, the system now includes a throttling mechanism that limits auto-exports to once every 5 minutes, even when enabled. This helps reduce unnecessary load on both your server and the CarPages FTP server.

### Manual Configuration (Advanced)

If needed, you can also modify the setting programmatically through the `src/utils/autoExport.js` file:

```javascript
// To enable auto-export
import autoExportUtils from './utils/autoExport';
autoExportUtils.setAutoExportEnabled(true);

// To disable auto-export
import autoExportUtils from './utils/autoExport';
autoExportUtils.setAutoExportEnabled(false);
```

## Logs

Export logs are stored in the `scripts/logs` directory:
- API-triggered exports: `api-supabase-export-TIMESTAMP.log`
- Auto-triggered exports: Logged in the browser console with the prefix `[AutoExport]`

## Recommended Export Approaches (In Order of Preference)

1. **Scheduled Daily Exports (RECOMMENDED)**: Set up the scheduler to run exports once daily during off-hours (e.g., 2:00 AM). This is the approach recommended by CarPages and is the most efficient.

2. **Manual Exports**: Trigger exports manually through the admin dashboard when you've made significant changes to your inventory that you want to publish immediately.

3. **Auto-Export (Use Sparingly)**: Enable auto-export only when you need real-time updates for critical inventory changes. This option sends your entire inventory to CarPages with each change, which may be inefficient for large inventories or frequent changes.

## Manual Export

The automatic export functionality doesn't replace the manual export option. Users can still trigger exports manually through the admin dashboard using the CarPages Export component. This is useful for immediate updates when needed.

## Troubleshooting

If automatic exports are not working as expected:

1. Check the browser console for error messages with the prefix `[AutoExport]` or `[Store]`
2. Verify that the `isAutoExportEnabled()` function is returning `true`
3. Check the server logs for any errors related to the export process
4. Ensure the FTP credentials in the export script are correct
5. Verify that the backend API endpoints for triggering exports are working correctly

## Technical Implementation

The auto-export functionality is implemented using the following components:

1. **Auto-Export Utility** (`src/utils/autoExport.js`): Provides functions for triggering exports and checking if auto-export is enabled.

2. **Vehicle Store** (`src/store/vehicles.js`): Integrates with the auto-export utility to trigger exports at the appropriate times.

3. **Backend API** (`backend/carpages-export.js`): Handles the actual export process, executing the export script as a separate process.

4. **Export Script** (`scripts/export-supabase-to-carpages.js`): Fetches vehicle data from Supabase, formats it as a CSV, and uploads it to the CarPages FTP server.