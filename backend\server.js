// server.js
import express from 'express';
import axios from 'axios';
import FormData from 'form-data'; // Keep for Stability AI
import dotenv from 'dotenv';
import cors from 'cors';
import { Buffer } from 'buffer';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'node:fs';
import path from 'node:path';
import listEndpoints from 'express-list-endpoints';
import { URLSearchParams } from 'node:url'; // Import URLSearchParams for form encoding

// Import the CarPages export handler (ES module)
import * as carPagesExport from './carpages-export.js';

// Load environment variables from .env file in the root directory
dotenv.config({ path: '../.env' });

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
// Path to the dist directory (frontend build)
const distPath = join(__dirname, '..', 'dist');

const app = express();
const port = process.env.PORT || 3001;

// Log the dist path for debugging
console.log(`Frontend dist path: ${distPath}`);

// --- Stability AI Key Check ---
const stabilityApiKey = process.env.STABILITY_API_KEY;
if (!stabilityApiKey) {
  console.warn("WARNING: STABILITY_API_KEY is not defined in the .env file. Stability AI calls will fail.");
} else {
  console.log("STABILITY_API_KEY loaded.");
}

// --- DataOne Key Check ---
const dataOneAccessKeyId = process.env.DATAONE_ACCESS_KEY_ID;
const dataOneSecretAccessKey = process.env.DATAONE_SECRET_ACCESS_KEY;
if (!dataOneAccessKeyId || !dataOneSecretAccessKey) {
    console.error("ERROR: DATAONE_ACCESS_KEY_ID or DATAONE_SECRET_ACCESS_KEY is not defined in the .env file. VIN Decoding will fail.");
    // Optionally exit if critical: process.exit(1);
} else {
    console.log("DataOne API Keys loaded.");
}
const DATAONE_API_URL = 'https://api.dataonesoftware.com/webservices/vindecoder/decode';


// --- Middleware ---
app.use(cors({ origin: '*' })); // TODO: Restrict origin for production
app.use(express.json({ limit: '10mb' })); // For receiving JSON from frontend

// Serve static files from the dist directory
app.use(express.static(distPath));

// --- Helper function for polling Stability AI results (Keep existing function) ---
const pollForResult = async (jobId, apiKey, requestedOutputFormat, apiTaskPath) => {
    // ... your existing pollForResult function code ...
    // (No changes needed here for the VIN decoder)
    const pollUrl = `https://api.stability.ai/v2beta/results/${jobId}`;
    console.log(`[Job ${jobId}] Starting polling. Primary URL: ${pollUrl}`);

    const maxAttempts = 45;
    const initialPollDelay = 10000;
    const pollInterval = 10000;
    let attempt = 0;

    const debugDir = path.join(process.cwd(), 'debug_responses');
    try {
        if (!fs.existsSync(debugDir)){
            fs.mkdirSync(debugDir, { recursive: true });
            console.log(`Created directory for debug responses: ${debugDir}`);
        }
    } catch (mkdirError) {
        console.error(`[Job ${jobId}] ERROR: Could not create debug directory ${debugDir}:`, mkdirError);
    }

    console.log(`[Job ${jobId}] Waiting ${initialPollDelay / 1000}s before first poll for task '${apiTaskPath}'...`);
    await new Promise(resolve => setTimeout(resolve, initialPollDelay));

    while (attempt < maxAttempts) {
        attempt++;
        console.log(`[Job ${jobId}] Polling attempt ${attempt}/${maxAttempts} for URL: ${pollUrl}`);

        try {
        if (attempt > 1) {
            await new Promise(resolve => setTimeout(resolve, pollInterval));
        }

        const pollResponse = await axios.get(pollUrl, {
            headers: {
            Authorization: `Bearer ${apiKey}`,
            Accept: 'application/json, image/*', // Accept JSON for status/errors, image for results
            'stability-client-id': 'gt-rebuild-app',
            'stability-client-user-id': 'LocalTest#1234',
            'stability-client-version': '1.2.1',
            },
            validateStatus: undefined, // Handle all statuses manually
            responseType: 'arraybuffer', // Get raw data first
            timeout: 30000,
        });

        const contentType = pollResponse.headers['content-type'];
        console.log(`[Job ${jobId}] > Poll attempt ${attempt} status: ${pollResponse.status}, Content-Type: ${contentType || 'N/A'}`);

        // Save raw response for debugging
        if (pollResponse.data) {
            const filename = path.join(debugDir, `raw_poll_${jobId}_attempt_${attempt}_status_${pollResponse.status}.bin`);
            try {
                fs.writeFileSync(filename, Buffer.from(pollResponse.data)); // Save raw buffer
                // console.log(`[Job ${jobId}] > Saved raw response buffer for attempt ${attempt} to: ${filename}`);
            } catch (saveError) {
                console.error(`[Job ${jobId}] > Error saving raw response buffer for attempt ${attempt} to ${filename}:`, saveError);
            }
        }

        // --- SUCCESS CASE HANDLING (Status 200) ---
        if (pollResponse.status === 200) {
            if (contentType && contentType.startsWith('application/json')) {
            let jsonData;
            let rawJsonString = '';
            try {
                rawJsonString = Buffer.from(pollResponse.data).toString('utf-8');
                jsonData = JSON.parse(rawJsonString);
            } catch (e) {
                console.error(`[Job ${jobId}] > Error: Received 200 OK with JSON Content-Type, but failed to parse JSON payload. Raw: ${rawJsonString.substring(0,500)}`, e);
                return { success: false, error: "Failed to parse successful JSON response from API.", status: 500, details: { parseErrorMessage: e.message, rawDataSnippet: rawJsonString.substring(0,500)} };
            }

            let finishReason = null;
            let base64Data = null;
            const artifacts = jsonData.artifacts;
            if (Array.isArray(artifacts) && artifacts.length > 0 && typeof artifacts[0] === 'object') {
                finishReason = artifacts[0].finish_reason;
                base64Data = artifacts[0].base64;
            } else if (jsonData.finish_reason && (jsonData.image || jsonData.base64 || jsonData.result)) {
                finishReason = jsonData.finish_reason;
                base64Data = jsonData.result || jsonData.image || jsonData.base64;
            }

            if (finishReason === "SUCCESS" && base64Data) {
                console.log(`[Job ${jobId}] > SUCCESS: Condition met. Processing base64 artifact.`);
                try {
                    const imageBuffer = Buffer.from(base64Data, 'base64');
                    let resultContentType = 'image/png'; // Default
                    if (requestedOutputFormat === 'jpeg') resultContentType = 'image/jpeg';
                    else if (requestedOutputFormat === 'webp') resultContentType = 'image/webp';
                     return { success: true, imageDataBuffer: imageBuffer, contentType: resultContentType };
                } catch (bufferError) {
                    console.error(`[Job ${jobId}] > Error creating buffer from base64 data: ${bufferError.message}`);
                    return { success: false, error: "Failed to decode base64 data received from API.", status: 500, details: { errorMessage: bufferError.message } };
                }
            } else {
                const errorMessage = `Job ${jobId} finished but failed validation. Finish Reason: '${finishReason || 'N/A'}', Base64 Data Found: ${!!base64Data}.`;
                console.error(`[Job ${jobId}] > JOB FAILURE (Validation Failed): ${errorMessage} Full JSON: ${JSON.stringify(jsonData).substring(0,1000)}`);
                let status = 422; // Unprocessable Entity
                if (finishReason) {
                    const upperFinishReason = finishReason.toUpperCase();
                    if (upperFinishReason === "CONTENT_FILTERED") status = 400; // Bad Request (content filter)
                    else if (upperFinishReason === "ERROR") status = 500; // Internal Server Error (API side)
                }
                return { success: false, error: errorMessage, status: status, details: jsonData };
            }
            } else if (contentType && contentType.startsWith('image/')) {
            console.log(`[Job ${jobId}] > SUCCESS: Received direct image data (${contentType}).`);
            return { success: true, imageDataBuffer: Buffer.from(pollResponse.data), contentType: contentType }; // Ensure it's a Buffer
            } else {
            const rawData = Buffer.from(pollResponse.data).toString('utf-8').substring(0, 500);
            const errorMessage = `Job ${jobId} completed (200 OK) but returned unexpected Content-Type: ${contentType || 'N/A'}.`;
            console.error(`[Job ${jobId}] > Poll Error: ${errorMessage} Raw Data Snippet: ${rawData}`);
            return { success: false, error: errorMessage, status: 500, details: { rawDataSnippet: rawData } };
            }
        } else if (pollResponse.status === 202) {
            console.log(`[Job ${jobId}] > Job still processing (202 Accepted). Continuing poll...`);
            // continue loop implicitly
        } else { // Handle 4xx/5xx errors from poll
            let errorMessage = `Polling failed for job ${jobId} with status ${pollResponse.status}`;
            let errorDetails = null;
            let rawErrorData = '(no error data)';
            try {
                rawErrorData = Buffer.from(pollResponse.data).toString('utf-8');
                if (contentType && contentType.startsWith('application/json') && rawErrorData) {
                    errorDetails = JSON.parse(rawErrorData);
                    const specificError = errorDetails.message || (errorDetails.errors ? JSON.stringify(errorDetails.errors) : JSON.stringify(errorDetails));
                    errorMessage += ` - ${specificError}`;
                } else if (rawErrorData) {
                    errorMessage += ` - ${rawErrorData.substring(0, 500)}`;
                    errorDetails = { rawErrorDataSnippet: rawErrorData.substring(0, 500) };
                }
            } catch (e) {
                errorMessage += ` - Failed to parse error response: ${rawErrorData.substring(0, 500)}`;
                errorDetails = { rawErrorDataSnippet: rawErrorData.substring(0, 500), parseError: e.message };
            }
            console.error(`[Job ${jobId}] > Poll Error: ${errorMessage}`);
             // Don't give up immediately on 404, could be temporary propagation delay
             if (pollResponse.status === 404 && attempt < 5) {
                 console.warn(`[Job ${jobId}] > Received 404 on attempt ${attempt}, will retry...`);
                 continue; // Continue polling
             }
            return { success: false, error: errorMessage, status: pollResponse.status, details: errorDetails };
        }
        } catch (networkError) {
        console.error(`[Job ${jobId}] > Poll Network Error (Attempt ${attempt}):`, networkError.message);
         if (networkError.code === 'ECONNABORTED' || networkError.message.includes('timeout')) {
             console.error(`[Job ${jobId}] > Poll request timed out.`);
         }
         // Don't return immediately, let the loop retry unless it's the last attempt
         if (attempt >= maxAttempts) {
             console.error(`[Job ${jobId}] > Failing after network error on final attempt.`);
             return { success: false, error: `Polling network error after ${attempt} attempts: ${networkError.message}`, status: 504 }; // Gateway Timeout
         }
        }
    } // End while loop

    console.error(`[Job ${jobId}] > Poll Error: Job timed out after ${maxAttempts} attempts.`);
    return { success: false, error: `Polling timed out for job ${jobId} after ${maxAttempts} attempts.`, status: 408 }; // Request Timeout
};


// --- API Routes ---

app.get('/api/test', (req, res) => {
  console.log('Received request for /api/test');
  res.json({ success: true, message: 'API is working!' });
});

// --- CarPages Export API Routes ---
app.post('/api/carpages/export', (req, res) => {
  console.log('Received request for /api/carpages/export');
  carPagesExport.triggerExport(req, res);
});

// --- Supabase to CarPages Export API Route ---
app.post('/api/carpages/export-supabase', (req, res) => {
  console.log('Received request for /api/carpages/export-supabase');
  carPagesExport.triggerSupabaseExport(req, res);
});

// --- NEW: DataOne VIN Decoder Route ---
app.post('/api/decode-vin', async (req, res) => {
    console.log(`Received request for /api/decode-vin`);
    const { vin } = req.body;

    // 1. Input Validation
    if (!vin || typeof vin !== 'string' || vin.trim().length !== 17) {
        console.error('Validation Error: Invalid or missing VIN received.');
        return res.status(400).json({ success: false, error: 'Invalid or missing VIN. A 17-character VIN is required.' });
    }
    const cleanVin = vin.trim().toUpperCase();
    console.log(`Processing VIN: ${cleanVin}`);

    // 2. Check for API Keys (essential for this endpoint)
    if (!dataOneAccessKeyId || !dataOneSecretAccessKey) {
        console.error('Configuration Error: DataOne API keys are missing on the server.');
        return res.status(500).json({ success: false, error: 'Server configuration error: VIN decoding service keys are missing.' });
    }

    // 3. Construct DataOne `decoder_query` Payload
    const commonDataPacksToRequest = {
        "basic_data": "on",
        "engines": "on",
        "transmissions": "on",
        "standard_specifications": "on", // Assuming this is valid
        "standard_generic_equipment": "on", // Assuming this is valid
        "colors": "on", // Gets both exterior/interior if available
        "optional_equipment": "off" // Keep off unless needed
        // Add other packs like "pricing" if needed: "pricing": "on"
    };

    const decoderQueryObject = {
        decoder_settings: {
            version: "7.2.0", // Use latest documented or "latest"
            display: "full",
            styles: "off",
            common_data: "on",
            common_data_packs: commonDataPacksToRequest,
            // style_data_packs: {} // Only if styles='on'
            // d1_use_case_identifier: "YOUR_USE_CASE" // If required
        },
        query_requests: {
            "query_request_1": { // Unique key for the request
                "identifier": cleanVin, // Optional identifier
                "vin": cleanVin
            }
        }
    };
    const decoderQueryString = JSON.stringify(decoderQueryObject);

    // 4. Prepare POST parameters for DataOne (x-www-form-urlencoded)
    const params = new URLSearchParams();
    params.append('access_key_id', dataOneAccessKeyId);
    params.append('secret_access_key', dataOneSecretAccessKey);
    params.append('decoder_query', decoderQueryString);

    console.log('Calling DataOne VIN Decoder API...');
    console.log('DataOne Query:', decoderQueryString); // Log the query being sent

    try {
        // 5. Make the API Call to DataOne
        const dataoneResponse = await axios.post(DATAONE_API_URL, params, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json' // Request JSON response
            },
            validateStatus: undefined, // Handle all statuses manually
            timeout: 20000 // 20 second timeout
        });

        console.log(`DataOne API response status: ${dataoneResponse.status}`);


        // Log specific sections of the response that we're interested in
        if (dataoneResponse.data && dataoneResponse.data.query_responses) {
            const queryResponseKey = Object.keys(dataoneResponse.data.query_responses)[0];
            const queryResponse = dataoneResponse.data.query_responses[queryResponseKey];

            if (queryResponse) {
            }
        }

        // 6. Process DataOne Response
        if (dataoneResponse.status !== 200) {
            let errorMessage = `DataOne API Error: Status ${dataoneResponse.status}`;
            let errorDetails = dataoneResponse.data; // Assume data might contain error info
            try {
                // Try parsing if it looks like JSON, otherwise use raw data
                if (typeof dataoneResponse.data === 'object') {
                    errorMessage += ` - ${dataoneResponse.data?.message || JSON.stringify(dataoneResponse.data)}`;
                } else if (dataoneResponse.data) {
                    const parsed = JSON.parse(dataoneResponse.data); // Data might be stringified JSON
                    errorDetails = parsed;
                    errorMessage += ` - ${parsed?.message || JSON.stringify(parsed)}`;
                }
            } catch (e) {
                 errorMessage += ` - Unable to parse error response. Raw: ${String(dataoneResponse.data).substring(0, 200)}`;
            }
            console.error(errorMessage);
            // Map DataOne status codes to appropriate client responses if needed
            const clientStatus = dataoneResponse.status >= 500 ? 502 : dataoneResponse.status; // Treat 5xx as Bad Gateway
            return res.status(clientStatus).json({ success: false, error: errorMessage, details: errorDetails });
        }

        // --- Successful Response (200 OK) ---
        const decodedData = dataoneResponse.data; // Should be parsed JSON if Accept header worked
        // console.log('DataOne Response Data:', JSON.stringify(decodedData, null, 2)); // DEBUG: Log full response

        // Extract data according to DataOne API documentation
        // The response structure should have decoder_messages and query_responses
        const queryResponses = decodedData?.query_responses;

        if (!queryResponses) {
            console.error('DataOne Success Response Error: Invalid structure received.', decodedData);
            return res.status(500).json({ success: false, error: 'Received success status from VIN service, but response structure was unexpected.' });
        }

        // Get the first query response (using the VIN as the key)
        const queryResponseKey = Object.keys(queryResponses)[0];
        const queryResponse = queryResponses[queryResponseKey];

        if (!queryResponse) {
            console.error('DataOne Success Response Error: No query response found.', decodedData);
            return res.status(500).json({ success: false, error: 'No vehicle data found for the provided VIN.' });
        }

        // Check for query error
        if (queryResponse.query_error && queryResponse.query_error.error_code) {
            const errMsg = queryResponse.query_error.error_message || 'VIN not found or lookup failed.';
            console.warn(`VIN Lookup Warning: ${errMsg} (VIN: ${cleanVin})`);
            return res.status(404).json({ success: false, error: errMsg });
        }

        // Extract specific fields needed by the frontend from the correct structure
        const usMarketData = queryResponse.us_market_data;
        if (!usMarketData || !usMarketData.common_us_data) {
            console.error('DataOne Success Response Error: No US market data found.', queryResponse);
            return res.status(404).json({ success: false, error: 'No vehicle data found for the provided VIN.' });
        }

        const commonUsData = usMarketData.common_us_data;

        // Extract basic data
        const basicData = commonUsData.basic_data || commonUsData;

        // Extract features from standard_generic_equipment if available
        let featuresList = [];
        if (queryResponse.standard_generic_equipment &&
            queryResponse.standard_generic_equipment.us_market_data &&
            queryResponse.standard_generic_equipment.us_market_data.common_us_data) {

            const equipmentData = queryResponse.standard_generic_equipment.us_market_data.common_us_data;

            // Process equipment categories
            if (equipmentData.categories && Array.isArray(equipmentData.categories)) {
                equipmentData.categories.forEach(category => {
                    // Each category has a name and an array of generic_equipment
                    const categoryName = category.generic_equipment_category;

                    if (category.generic_equipment && Array.isArray(category.generic_equipment)) {
                        category.generic_equipment.forEach(equipment => {
                            // Each equipment has a name and possibly values
                            let featureName = equipment.generic_equipment_name;

                            // Check if there are specific values to add
                            if (equipment.generic_equipment_values &&
                                Array.isArray(equipment.generic_equipment_values) &&
                                equipment.generic_equipment_values.length > 0) {

                                // Get non-empty values
                                const values = equipment.generic_equipment_values
                                    .map(val => val.generic_equipment_value)
                                    .filter(val => val && val.trim() !== '');

                                // Add values to the feature name if they exist
                                if (values.length > 0) {
                                    featureName += `: ${values.join(', ')}`;
                                }
                            }

                            // Add the feature to our list
                            featuresList.push(featureName);
                        });
                    }
                });
            }
        }

        // Get specifications data if available
        const specsData = queryResponse.standard_specifications &&
                         queryResponse.standard_specifications.us_market_data &&
                         queryResponse.standard_specifications.us_market_data.common_us_data;

        // Get colors data if available
        const colorsData = queryResponse.colors &&
                          queryResponse.colors.us_market_data &&
                          queryResponse.colors.us_market_data.common_us_data;

        // Map to the structure expected by VehicleForm.vue
        // Map only the fields needed for the VehicleForm component

        // Extract engine data from the first engine in the engines array
        const engineData = queryResponse.us_market_data?.common_us_data?.engines &&
                          queryResponse.us_market_data.common_us_data.engines.length > 0 ?
                          queryResponse.us_market_data.common_us_data.engines[0] : null;

        // Extract transmission data from the first transmission in the transmissions array
        const transmissionData = queryResponse.us_market_data?.common_us_data?.transmissions &&
                                queryResponse.us_market_data.common_us_data.transmissions.length > 0 ?
                                queryResponse.us_market_data.common_us_data.transmissions[0] : null;

        // Extract seating data from standard_specifications
        let seatingData = null;
        let doorsData = null;
        let fuelEconomyData = null;

        if (queryResponse.us_market_data?.common_us_data?.standard_specifications) {
            // Find seating specifications
            const seatingSpecs = queryResponse.us_market_data.common_us_data.standard_specifications.find(
                spec => spec.specification_category === "Seating"
            );

            if (seatingSpecs && seatingSpecs.specification_values) {
                // Find standard seating value
                const standardSeating = seatingSpecs.specification_values.find(
                    val => val.specification_name === "Standard Seating"
                );

                if (standardSeating) {
                    seatingData = standardSeating.specification_value;
                }
            }

            // Find doors data from basic_data or specifications
            if (basicData.doors) {
                doorsData = basicData.doors;
            } else {
                // Try to find doors in specifications
                const exteriorDimensions = queryResponse.us_market_data.common_us_data.standard_specifications.find(
                    spec => spec.specification_category === "Exterior Dimensions"
                );

                if (exteriorDimensions && exteriorDimensions.specification_values) {
                    const doorsSpec = exteriorDimensions.specification_values.find(
                        val => val.specification_name === "Doors" || val.specification_name.includes("Door")
                    );

                    if (doorsSpec) {
                        doorsData = doorsSpec.specification_value;
                    }
                }
            }

            // Find fuel economy data
            const fuelEconomy = queryResponse.us_market_data.common_us_data.standard_specifications.find(
                spec => spec.specification_category === "Fuel Economy" ||
                       spec.specification_category === "EPA Fuel Economy"
            );

            if (fuelEconomy && fuelEconomy.specification_values) {
                const citySpec = fuelEconomy.specification_values.find(
                    val => val.specification_name.includes("City") || val.specification_name.includes("Urban")
                );

                const hwySpec = fuelEconomy.specification_values.find(
                    val => val.specification_name.includes("Highway") || val.specification_name.includes("Hwy")
                );

                fuelEconomyData = {
                    city: citySpec ? citySpec.specification_value : null,
                    highway: hwySpec ? hwySpec.specification_value : null
                };
            }
        }

        // Log the basic data for debugging
        console.log("Basic Data:", JSON.stringify(basicData, null, 2));

        // Create a title based on year, make, model, and trim
        const titleParts = [];
        if (basicData.year) titleParts.push(basicData.year);
        if (basicData.make) titleParts.push(basicData.make);
        if (basicData.model) titleParts.push(basicData.model);
        if (basicData.trim) titleParts.push(basicData.trim);
        const generatedTitle = titleParts.join(' ');

        // For fuel economy, if we don't have specific data, create a placeholder
        let cityFuelValue = '';
        let hwyFuelValue = '';

        if (fuelEconomyData && fuelEconomyData.city) {
            cityFuelValue = fuelEconomyData.city;
        } else if (engineData && engineData.fuel_type === 'Gasoline') {
            // Provide a reasonable estimate based on vehicle type and year
            if (basicData.vehicle_type === 'Truck' || basicData.body_type === 'Pickup') {
                cityFuelValue = '15 MPG';
            } else {
                cityFuelValue = '20 MPG';
            }
        }

        if (fuelEconomyData && fuelEconomyData.highway) {
            hwyFuelValue = fuelEconomyData.highway;
        } else if (engineData && engineData.fuel_type === 'Gasoline') {
            // Provide a reasonable estimate based on vehicle type and year
            if (basicData.vehicle_type === 'Truck' || basicData.body_type === 'Pickup') {
                hwyFuelValue = '20 MPG';
            } else {
                hwyFuelValue = '25 MPG';
            }
        }

        const frontendData = {
            // Basic vehicle info
            title: generatedTitle || 'Vehicle Details',
            year: basicData.year || '',
            make: basicData.make || '',
            model: basicData.model || '',
            trim: basicData.trim || '',

            // Body Style
            bodyStyle: basicData.body_type || basicData.body_subtype || basicData.vehicle_type || '',

            // Engine data
            engine: engineData ? engineData.name ||
                   `${engineData.ice_cylinders || ''} Cylinder ${engineData.ice_displacement || ''}L` : '',

            engineSize: engineData ?
                       (engineData.ice_displacement ? `${engineData.ice_displacement} L` : '') : '',

            // Transmission data
            transmission: transmissionData ?
                         transmissionData.name ||
                         `${transmissionData.gears || ''}-Speed ${transmissionData.type === 'A' ? 'Automatic' : 'Manual'}` : '',

            // Drivetrain data
            drivetrain: basicData.drive_type || '',

            // Fuel information
            fuelType: engineData ? engineData.fuel_type || '' : '',

            cityFuel: cityFuelValue,
            hwyFuel: hwyFuelValue,

            // Dimensions and seating
            doors: doorsData ? parseInt(doorsData, 10) || null : null,

            passengers: seatingData ? parseInt(seatingData, 10) || null : null,

            // Colors - Try to get from both sources
            exteriorColor: colorsData && colorsData.exterior_colors && colorsData.exterior_colors.length > 0 ?
                          colorsData.exterior_colors[0].generic_color_name ||
                          colorsData.exterior_colors[0].mfr_color_name || '' : '',

            interiorColor: colorsData && colorsData.interior_colors && colorsData.interior_colors.length > 0 ?
                          colorsData.interior_colors[0].generic_color_name ||
                          colorsData.interior_colors[0].mfr_color_name || '' : '',

            // Features
            features: featuresList.length > 0 ? featuresList : []
        };

        // Log the mapped data for debugging
        console.log('Mapped vehicle data for frontend:', JSON.stringify(frontendData, null, 2));


        console.log(`Successfully decoded VIN ${cleanVin}. Sending data to frontend.`);
        return res.json(frontendData); // Send only the mapped data

    } catch (error) {
        console.error(`Error calling DataOne API for VIN ${cleanVin}:`, error.message);
        if (axios.isAxiosError(error)) {
            if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                 console.error('DataOne API call timed out.');
                 return res.status(504).json({ success: false, error: 'VIN decoding service timed out.' });
            }
             // Log details if available
             console.error('Axios Error Details:', error.response?.status, error.response?.data);
             return res.status(502).json({ success: false, error: `Failed to communicate with VIN decoding service: ${error.message}` }); // Bad Gateway
        } else {
             // General server error during the process
             return res.status(500).json({ success: false, error: `Internal server error during VIN decoding: ${error.message}` });
        }
    }
});


// --- Existing Stability AI Routes (Keep them) ---
app.post('/api/ai/remove-background', async (req, res) => {
    // ... your existing remove-background code ...
    console.log('Received request for /api/ai/remove-background');
    const { imageUrl, outputFormat = 'webp' } = req.body;

    if (!imageUrl) {
        return res.status(400).json({ success: false, error: 'Missing imageUrl in request body' });
    }
    const validFormats = ['webp', 'png', 'jpeg'];
    if (!validFormats.includes(outputFormat)) {
        console.error(`Invalid outputFormat requested: ${outputFormat}`);
        return res.status(400).json({ success: false, error: `Invalid outputFormat: must be one of ${validFormats.join(', ')}` });
    }

    console.log('Processing image URL:', imageUrl);
    console.log('Requested Output Format:', outputFormat);

    try {
        console.log('Fetching image...');
        const imageResponse = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000,
        });

        if (imageResponse.status !== 200) {
        throw new Error(`Failed to fetch image from URL. Status: ${imageResponse.status}`);
        }
        console.log(`Image fetched successfully. Size: ${imageResponse.data.length} bytes`);

        const imageBuffer = Buffer.from(imageResponse.data);

        const formData = new FormData();
        formData.append('image', imageBuffer, { filename: 'input_image.png' }); // Use Buffer directly
        formData.append('output_format', outputFormat);

        console.log('Calling Stability AI Remove Background API...');

        const stabilityResponse = await axios.post(
        `https://api.stability.ai/v2beta/stable-image/edit/remove-background`,
        formData, // Pass FormData object directly
        {
            headers: {
            ...formData.getHeaders(), // Let axios set Content-Type from FormData
            Authorization: `Bearer ${stabilityApiKey}`,
            Accept: 'image/*', // Expect an image back
            'stability-client-id': 'gt-rebuild-app',
            'stability-client-user-id': 'LocalTest#1234',
            'stability-client-version': '1.2.1',
            },
            validateStatus: undefined,
            responseType: 'arraybuffer', // Expect raw image data
            timeout: 60000,
        }
        );

        console.log('Stability AI response status:', stabilityResponse.status);

        if (stabilityResponse.status === 200) {
        const contentType = stabilityResponse.headers['content-type'];
        if (contentType && contentType.startsWith('image/')) {
            console.log(`Background removal successful. Content-Type: ${contentType}`);
            const resultBuffer = Buffer.from(stabilityResponse.data);
            const base64Image = resultBuffer.toString('base64');
            const dataUrl = `data:${contentType};base64,${base64Image}`;
            res.json({ success: true, imageData: dataUrl });
        } else {
            const rawData = stabilityResponse.data?.toString('utf-8').substring(0, 500);
            console.error(`Stability AI Remove Background Error: Received 200 OK but unexpected Content-Type: ${contentType || 'N/A'}. Data: ${rawData}`);
            res.status(500).json({ success: false, error: `API returned 200 OK but with unexpected content: ${contentType || 'N/A'}`});
        }
        } else {
        let errorMessage = `Stability AI Remove Background Error: Status ${stabilityResponse.status}`;
        let errorDetails = null;
        let rawErrorData = '(no error data)';
        try {
            rawErrorData = stabilityResponse.data?.toString('utf-8') || '(empty error data)';
            if (rawErrorData && stabilityResponse.headers['content-type']?.includes('json')) {
                errorDetails = JSON.parse(rawErrorData);
                errorMessage += ` - ${errorDetails.message || JSON.stringify(errorDetails.errors || errorDetails)}`;
            } else {
                 errorMessage += ` - ${rawErrorData.substring(0, 500)}`;
            }
        } catch (e) {
            errorMessage += ` - ${rawErrorData.substring(0, 500)}`;
        }
        console.error(errorMessage);
        res.status(stabilityResponse.status).json({ success: false, error: errorMessage, details: errorDetails });
        }
    } catch (error) {
        console.error('Error processing background removal:', error.message);
        if (axios.isAxiosError(error)) {
            if (error.response) {
                console.error('Axios response error status:', error.response.status);
                const responseDataString = error.response.data?.toString() || '(no response data)';
                console.error('Axios response error data:', responseDataString.substring(0,500));
                const status = error.response.status >= 500 ? 502 : error.response.status;
                res.status(status).json({ success: false, error: `API Error: ${error.message}`, details: responseDataString.substring(0,500) });
            } else if (error.request) {
                console.error('Axios request error (no response received):', error.request);
                res.status(504).json({ success: false, error: `API request failed: No response received. ${error.message}` });
            } else {
                res.status(500).json({ success: false, error: `Internal Server Error during API call setup: ${error.message}` });
            }
        } else {
            res.status(500).json({ success: false, error: `Internal Server Error: ${error.message}` });
        }
    }
});

app.post('/api/ai/replace-background-and-relight', async (req, res) => {
    // ... your existing replace-background code ...
    console.log('Received request for /api/ai/replace-background-and-relight');
    const {
        imageUrl,
        backgroundPrompt,
        lightSourceDirection,
        lightSourceStrength,
        preserveOriginalSubject,
        outputFormat = 'webp',
        seed // Get seed from request body
    } = req.body;

    // --- Input Validation ---
    if (!imageUrl) return res.status(400).json({ success: false, error: 'Missing imageUrl' });
    if (!backgroundPrompt) return res.status(400).json({ success: false, error: 'Missing backgroundPrompt' });

    const validLightDirections = ['above', 'below', 'left', 'right', undefined, null, ''];
    if (lightSourceDirection && !validLightDirections.includes(lightSourceDirection)) {
        console.error(`Invalid lightSourceDirection received: ${lightSourceDirection}`);
        return res.status(400).json({ success: false, error: 'Invalid lightSourceDirection: must be one of above, below, left, right' });
    }
    if (lightSourceDirection && lightSourceStrength !== undefined && (typeof lightSourceStrength !== 'number' || lightSourceStrength < 0 || lightSourceStrength > 1)) {
        console.error(`Invalid lightSourceStrength received: ${lightSourceStrength}`);
        return res.status(400).json({ success: false, error: 'Invalid lightSourceStrength: must be a number between 0 and 1 (and requires light_source_direction)' });
    }
    if (preserveOriginalSubject !== undefined && (typeof preserveOriginalSubject !== 'number' || preserveOriginalSubject < 0 || preserveOriginalSubject > 1)) {
        console.error(`Invalid preserveOriginalSubject received: ${preserveOriginalSubject}`);
        return res.status(400).json({ success: false, error: 'Invalid preserveOriginalSubject: must be a number between 0 and 1' });
    }
    const validFormats = ['webp', 'png', 'jpeg'];
    if (!validFormats.includes(outputFormat)) {
        console.error(`Invalid outputFormat received: ${outputFormat}`);
        return res.status(400).json({ success: false, error: `Invalid outputFormat: must be one of ${validFormats.join(', ')}` });
    }

    console.log('Processing image URL:', imageUrl);
    console.log('Background prompt:', backgroundPrompt);
    console.log('Requested Output Format:', outputFormat);
    console.log('Optional Params:', { lightSourceDirection, lightSourceStrength, preserveOriginalSubject, seed });

    let jobId = null;

    try {
        // 1. Fetch the image
        console.log('Fetching image...');
        let imageBuffer;
        try {
        const imageResponse = await axios.get(imageUrl, {
            responseType: 'arraybuffer',
            timeout: 30000
        });
        if (imageResponse.status !== 200) throw new Error(`Failed to fetch image. Status: ${imageResponse.status}`);
        console.log(`Image fetched successfully. Size: ${imageResponse.data.length} bytes`);
        imageBuffer = Buffer.from(imageResponse.data);
        } catch (fetchError) {
        console.error('Error fetching image:', fetchError.message);
        if (axios.isAxiosError(fetchError) && fetchError.response) {
            throw new Error(`Failed to fetch image: Status ${fetchError.response.status} from ${imageUrl}`);
        } else {
            throw new Error(`Failed to fetch image: ${fetchError.message}`);
        }
        }

        // 2. Prepare FormData for Stability AI
        const formData = new FormData();
        formData.append('subject_image', imageBuffer, { filename: 'input_image.png' });
        formData.append('background_prompt', backgroundPrompt);
        formData.append('output_format', outputFormat);
        const seedValue = String(seed || Math.floor(Math.random() * 4294967294)); // Use provided seed or random
        formData.append('seed', seedValue);

        if (lightSourceDirection) {
        formData.append('light_source_direction', lightSourceDirection);
        if (lightSourceStrength !== undefined) {
            formData.append('light_source_strength', String(lightSourceStrength));
        }
        }
        if (preserveOriginalSubject !== undefined) {
        formData.append('preserve_original_subject', String(preserveOriginalSubject));
        }

        // Log fields being sent
        const formFields = {};
        formFields['subject_image'] = `[Image Buffer, size: ${imageBuffer.length}]`;
        formFields['background_prompt'] = backgroundPrompt;
        formFields['output_format'] = outputFormat;
        formFields['seed'] = seedValue;
        if (lightSourceDirection) formFields['light_source_direction'] = lightSourceDirection;
        if (lightSourceDirection && lightSourceStrength !== undefined) {
            formFields['light_source_strength'] = String(lightSourceStrength);
        }
        if (preserveOriginalSubject !== undefined) {
            formFields['preserve_original_subject'] = String(preserveOriginalSubject);
        }
        console.log('Calling Stability AI to start Replace Background job with fields:', formFields);

        // 3. Call Stability AI API - Initial request to start the job
        const initialResponse = await axios.post( // Use post, not postForm if sending FormData directly
        `https://api.stability.ai/v2beta/stable-image/edit/replace-background-and-relight`,
        formData, // Send FormData directly
        {
            headers: {
             ...formData.getHeaders(), // Let axios set Content-Type
            Authorization: `Bearer ${stabilityApiKey}`,
            Accept: 'application/json', // Expect JSON job ID back
            'stability-client-id': 'gt-rebuild-app',
            'stability-client-user-id': 'LocalTest#1234',
            'stability-client-version': '1.2.1',
            },
            validateStatus: undefined, // Handle manually
            responseType: 'arraybuffer', // Get raw response first
            maxBodyLength: Infinity,
            maxContentLength: Infinity,
            timeout: 60000,
        }
        );

        console.log('Initial Stability AI response status:', initialResponse.status);

        // 4. Process Initial Response
        if (initialResponse.status === 200 || initialResponse.status === 202) { // Accept 200 or 202 for job start
        try {
            const jsonResponse = JSON.parse(initialResponse.data.toString('utf-8'));
            jobId = jsonResponse.id;
            if (!jobId) {
                console.error('Initial response missing job ID.', jsonResponse);
                throw new Error('Job ID missing in the successful initial response JSON.');
            }
            console.log(`Job started successfully. Job ID: ${jobId}`);
        } catch (parseError) {
            const rawData = initialResponse.data?.toString('utf-8').substring(0, 500);
            console.error(`Failed to parse Job ID from initial response: ${rawData}`, parseError);
            return res.status(500).json({ success: false, error: 'Failed to get valid Job ID from Stability AI despite success status.', details: { rawDataSnippet: rawData } });
        }

        // 5. Start Polling
        console.log(`Starting polling for Job ID: ${jobId}...`);
        const taskPath = 'stable-image/edit/replace-background-and-relight'; // For logging

        try {
            const pollResult = await pollForResult(jobId, stabilityApiKey, outputFormat, taskPath);

            // 6. Process Polling Result
            if (pollResult.success) {
            console.log(`[Job ${jobId}] Polling complete, sending image to client.`);
            const base64Image = pollResult.imageDataBuffer.toString('base64');
            const dataUrl = `data:${pollResult.contentType};base64,${base64Image}`;
            res.json({ success: true, imageData: dataUrl, jobId: jobId });
            } else {
            console.error(`[Job ${jobId}] Polling/Job failed. Status: ${pollResult.status}, Error: ${pollResult.error}`);
            res.status(pollResult.status || 500).json({
                success: false,
                error: pollResult.error,
                details: pollResult.details,
                jobId: jobId
            });
            }
        } catch (pollingError) {
            console.error(`[Job ${jobId}] Unexpected error during polling process: ${pollingError.message}`, pollingError.stack);
            res.status(500).json({
            success: false,
            error: `Internal server error during result polling: ${pollingError.message}`,
            jobId: jobId
            });
        }

        } else {
        // Initial request failed
        let errorMessage = `Stability AI Initial Request Error: Status ${initialResponse.status}`;
        let errorDetails = null;
        let rawErrorData = '(no error data)';
        try {
            rawErrorData = initialResponse.data?.toString('utf-8') || '(empty error data)';
             if (rawErrorData && initialResponse.headers['content-type']?.includes('json')) {
                 errorDetails = JSON.parse(rawErrorData);
                 errorMessage += ` - ${errorDetails.message || JSON.stringify(errorDetails.errors || errorDetails)}`;
             } else {
                 errorMessage += ` - ${rawErrorData.substring(0, 500)}`;
             }
        } catch (e) {
            errorMessage += ` - ${rawErrorData.substring(0, 500)}`;
        }
        console.error(errorMessage);
        res.status(initialResponse.status).json({ success: false, error: errorMessage, details: errorDetails });
        }

    } catch (error) {
        console.error(`Error processing background replacement (Job ID: ${jobId || 'N/A'}): ${error.message}`, error.stack);
        if (axios.isAxiosError(error)) {
            if (error.response) {
                console.error('Axios response error status (Initial POST):', error.response.status);
                const responseDataString = error.response.data?.toString() || '(no response data)';
                console.error('Axios response error data (Initial POST):', responseDataString.substring(0, 500));
                res.status(error.response.status).json({ success: false, error: `API Error (Initial Request): ${error.message}`, details: responseDataString.substring(0,500), jobId: jobId });
            } else if (error.request) {
                console.error('Axios request error (Initial POST - no response):', error.request);
                res.status(504).json({ success: false, error: `API request failed (Initial Request): No response received. ${error.message}`, jobId: jobId });
            } else {
                res.status(500).json({ success: false, error: `Internal Server Error during API call setup: ${error.message}`, jobId: jobId });
            }
        } else {
            res.status(500).json({ success: false, error: `Internal Server Error: ${error.message}`, jobId: jobId });
        }
    }
});

app.post('/api/ai/handle-image-generation', async (req, res) => {
    // ... your existing handle-image-generation code ...
    console.log('Received request for /api/ai/handle-image-generation');
    const { apiResponse, filename, saveToFile = false } = req.body;

    if (!apiResponse) {
        return res.status(400).json({ success: false, error: 'Missing apiResponse in request body' });
    }

    try {
        // Parse the API response if it's a string
        const parsedResponse = typeof apiResponse === 'string' ? JSON.parse(apiResponse) : apiResponse;

        let base64Data = null;
        let foundField = null;

        if (parsedResponse.result) {
        base64Data = parsedResponse.result; foundField = 'result';
        } else if (parsedResponse.image) {
        base64Data = parsedResponse.image; foundField = 'image';
        } else if (parsedResponse.base64) {
        base64Data = parsedResponse.base64; foundField = 'base64';
        } else if (parsedResponse.artifacts && Array.isArray(parsedResponse.artifacts) && parsedResponse.artifacts.length > 0) {
        const artifact = parsedResponse.artifacts[0];
        if (artifact.base64) { base64Data = artifact.base64; foundField = 'artifacts[0].base64'; }
        }

        if (!base64Data) {
        console.error('Could not find image data in response:', parsedResponse);
        return res.status(400).json({ success: false, error: 'Could not find image data in API response.' });
        }

        const seed = parsedResponse.seed || parsedResponse.artifacts?.[0]?.seed || 'unknown';
        console.log(`Processing image generation result with seed: ${seed}. Found Base64 in field: ${foundField}`);

        const imageBuffer = Buffer.from(base64Data, 'base64');
        let contentType = 'image/png'; // Default
        if (base64Data.startsWith('UklGR')) contentType = 'image/webp';
        else if (base64Data.startsWith('/9j/')) contentType = 'image/jpeg';

        let savedFilePath = null;
        if (saveToFile) {
        if (!filename) return res.status(400).json({ success: false, error: 'Filename required for saving' });
        const imagesDir = path.join(process.cwd(), 'generated_images');
        if (!fs.existsSync(imagesDir)) fs.mkdirSync(imagesDir, { recursive: true });
        let finalFilename = filename;
        // Add extension logic if needed
        savedFilePath = path.join(imagesDir, finalFilename);
        fs.writeFileSync(savedFilePath, imageBuffer);
        console.log(`Image saved to: ${savedFilePath}`);
        }

        const dataUrl = `data:${contentType};base64,${imageBuffer.toString('base64')}`;
        res.json({ success: true, contentType, imageSize: imageBuffer.length, seed, dataUrl, savedFilePath });

    } catch (error) {
        console.error('Error processing image generation result:', error.message);
        res.status(500).json({ success: false, error: `Internal Server Error: ${error.message}` });
    }
});


// --- Check Endpoints Route (Keep existing) ---
app.get('/api/check-endpoints', (req, res) => {
  console.log('Received request for /api/check-endpoints');
  const endpoints = listEndpoints(app);
  res.json({
    success: true,
    message: 'Available API endpoints:',
    endpoints: endpoints.map(ep => ({ path: ep.path, methods: ep.methods })),
  });
});

// Handle all non-API routes by serving index.html
app.use((req, res, next) => {
  // Skip API routes and static files (which are already handled by express.static)
  if (req.path.startsWith('/api/')) {
    return next();
  }
  // For all other routes, serve the index.html file
  res.sendFile(join(distPath, 'index.html'));
});

// --- Global Error Handler (Keep existing) ---
app.use((err, req, res, next) => {
  console.error("Unhandled Error:", err.stack || err);
  res.status(500).json({ success: false, error: 'Internal Server Error (Unhandled)', details: err.message });
});


// --- Start Server ---
const server = app.listen(port, () => {
  console.log(`Backend server listening at http://localhost:${port}`);
  console.log('Available endpoints:');
  try {
    const endpoints = listEndpoints(app);
    endpoints.forEach(endpoint => {
        // Simple formatting for alignment
        const methods = endpoint.methods.join(', ');
        console.log(`  ${methods.padEnd(18)} ${endpoint.path}`);
    });
  } catch (listError) {
      console.error("Could not list endpoints:", listError);
  }

  // Check required keys on startup
  if (!stabilityApiKey) console.warn("\nWarning: STABILITY_API_KEY is missing.");
  if (!dataOneAccessKeyId || !dataOneSecretAccessKey) console.warn("\nWarning: DataOne API keys are missing. VIN Decoding will fail.");

});

process.on('SIGTERM', () => {
    console.log('SIGTERM signal received: closing HTTP server');
    server.close(() => {
        console.log('HTTP server closed');
    });
});