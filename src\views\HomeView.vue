<script setup>
import { ref, onMounted, reactive, computed, watch, nextTick } from 'vue'; // Added nextTick
import vehicleStore from '../store/vehicles';
import BusinessInfoProvider from '../components/BusinessInfoProvider.vue';
import AboutGamePreview from '../components/AboutGamePreview.vue';

// Vehicle categories - simplified for featured vehicles
const vehicleCategories = ref([
  { id: 'cars', name: 'Cars & Minivan' },
  { id: 'trucks', name: 'Trucks & Vans' },
  { id: 'powersports', name: 'Powersports' },
  { id: 'suvs', name: 'Crossovers & SUVs' }
]);

// Active category
const activeCategory = ref('cars');

// Replace hardcoded vehicles with an empty array that will be populated from Supabase
const allVehicles = ref([]);
const isLoadingVehicles = ref(true);

// Function to fetch vehicles from the store
const fetchVehicles = async () => {
  try {
    isLoadingVehicles.value = true;
    // Initialize the vehicle store if needed
    if (!vehicleStore.isInitialized.value) {
      await vehicleStore.initStore();
    }
    const vehicles = vehicleStore.vehicles.value;

    // Map the vehicles to match the structure expected by the UI
    allVehicles.value = vehicles.map(vehicle => {
      // Log the raw vehicle data to help with debugging
      console.log('Raw vehicle data:', {
        id: vehicle.id,
        make: vehicle.make,
        model: vehicle.model,
        bodyStyle: vehicle.bodyStyle
      });

      return {
        id: vehicle.id,
        // Create a main title (Year Make Model)
        mainTitle: `${vehicle.year || ''} ${vehicle.make || ''} ${vehicle.model || ''}`.trim(),
        // Store the trim separately, handle potential null/undefined values
        trim: vehicle.trim || null, // Use null if trim is not available
        price: vehicle.price || 0,
        image: vehicle.image || 'https://via.placeholder.com/400x300?text=No+Image',
        year: vehicle.year || new Date().getFullYear(),
        // Pass model to the categorization function for more accurate categorization
        category: mapVehicleTypeToCategory(vehicle.bodyStyle, vehicle.make, vehicle.model),
        vehicleType: vehicle.bodyStyle || 'Sedan',
        // Keep tagline short or adjust as needed
        tagline: vehicle.description ? vehicle.description.substring(0, 55) + '...' : '',
        mpgCity: vehicle.cityFuel || 25,
        mpgHighway: vehicle.hwyFuel || 30,
        msrp: vehicle.price || 0
      };
    });
  } catch (error) {
    console.error('Error fetching vehicles:', error);
    allVehicles.value = [];
  } finally {
    isLoadingVehicles.value = false;
  }
};

// Helper function to map vehicle types to categories
const mapVehicleTypeToCategory = (bodyStyle, make, model) => {
  if (!bodyStyle) return 'cars';

  const bodyStyleLower = bodyStyle?.toLowerCase() || '';
  const makeLower = make?.toLowerCase() || '';
  const modelLower = model?.toLowerCase() || '';

  // Debug log to help diagnose categorization issues
  console.log(`Categorizing vehicle: bodyStyle=${bodyStyleLower}, make=${makeLower}, model=${modelLower}`);

  // Check for ATVs - they should go in the powersports category
  if (bodyStyleLower.includes('atv') ||
      bodyStyleLower.includes('quad') ||
      bodyStyleLower.includes('blade') ||
      makeLower.includes('tcb') ||
      modelLower.includes('blade') ||
      modelLower.includes('ranger') && !bodyStyleLower.includes('pickup') ||
      modelLower.includes('rzr') ||
      modelLower.includes('can-am') ||
      modelLower.includes('polaris')) {
    console.log('Categorized as: powersports (ATV)');
    return 'powersports';
  }

  // Check for motorcycles - they should go in the powersports category
  if (bodyStyleLower.includes('motorcycle') ||
      bodyStyleLower.includes('bike') ||
      makeLower.includes('harley') ||
      makeLower.includes('yamaha') ||
      makeLower.includes('kawasaki') ||
      makeLower.includes('suzuki') ||
      makeLower.includes('ducati') ||
      makeLower.includes('triumph') ||
      makeLower.includes('bmw') && (modelLower.includes('r') || modelLower.includes('s1000')) ||
      makeLower.includes('ktm') ||
      (makeLower.includes('honda') && (bodyStyleLower.includes('cbr') || modelLower.includes('cbr')))) {
    console.log('Categorized as: powersports (motorcycle)');
    return 'powersports';
  }

  // Check for other powersports vehicles
  if (bodyStyleLower.includes('jet ski') ||
      bodyStyleLower.includes('watercraft') ||
      bodyStyleLower.includes('snowmobile') ||
      bodyStyleLower.includes('dirt bike') ||
      modelLower.includes('seadoo') ||
      modelLower.includes('waverunner')) {
    console.log('Categorized as: powersports (other)');
    return 'powersports';
  }

  // Check for vans and cargo vehicles - they should go in the trucks category
  if (bodyStyleLower.includes('van') ||
      bodyStyleLower.includes('cargo') ||
      modelLower.includes('transit') ||
      modelLower.includes('sprinter') ||
      modelLower.includes('promaster') ||
      modelLower.includes('express') ||
      modelLower.includes('savana') ||
      modelLower.includes('nv') ||
      (makeLower.includes('ford') && modelLower.includes('transit')) ||
      (makeLower.includes('mercedes') && modelLower.includes('sprinter')) ||
      (makeLower.includes('ram') && modelLower.includes('promaster'))) {
    console.log('Categorized as: trucks (van/cargo)');
    return 'trucks';
  }

  // Check for trucks and pickups
  if (bodyStyleLower.includes('truck') ||
      bodyStyleLower.includes('pickup') ||
      bodyStyleLower.includes('ute') ||
      modelLower.includes('f-150') ||
      modelLower.includes('f-250') ||
      modelLower.includes('f-350') ||
      modelLower.includes('silverado') ||
      modelLower.includes('sierra') ||
      modelLower.includes('ram 1500') ||
      modelLower.includes('ram 2500') ||
      modelLower.includes('ram 3500') ||
      modelLower.includes('tundra') ||
      modelLower.includes('tacoma') ||
      modelLower.includes('frontier') ||
      modelLower.includes('titan') ||
      (modelLower.includes('ranger') && !makeLower.includes('polaris'))) {
    console.log('Categorized as: trucks (pickup/truck)');
    return 'trucks';
  }

  // Check for SUVs and crossovers
  if (bodyStyleLower.includes('suv') ||
      bodyStyleLower.includes('crossover') ||
      bodyStyleLower.includes('4x4') ||
      bodyStyleLower.includes('4wd') ||
      bodyStyleLower.includes('utility') ||
      modelLower.includes('explorer') ||
      modelLower.includes('escape') ||
      modelLower.includes('equinox') ||
      modelLower.includes('tahoe') ||
      modelLower.includes('suburban') ||
      modelLower.includes('expedition') ||
      modelLower.includes('highlander') ||
      modelLower.includes('rav4') ||
      modelLower.includes('cr-v') ||
      modelLower.includes('pilot')) {
    console.log('Categorized as: suvs');
    return 'suvs';
  }

  // For electric vehicles, we'll categorize them based on their body style
  // We'll check if it's electric, but continue with the categorization
  const isElectric = bodyStyleLower.includes('hybrid') ||
      bodyStyleLower.includes('electric') ||
      bodyStyleLower.includes('ev') ||
      makeLower.includes('tesla') ||
      bodyStyleLower.includes('phev') ||
      modelLower.includes('leaf') ||
      modelLower.includes('bolt') ||
      modelLower.includes('prius') ||
      modelLower.includes('ioniq') ||
      modelLower.includes('model s') ||
      modelLower.includes('model 3') ||
      modelLower.includes('model x') ||
      modelLower.includes('model y');

  if (isElectric) {
    console.log('Vehicle is electric/hybrid, but will be categorized by body style');

    // Special handling for Tesla models
    if (makeLower.includes('tesla')) {
      if (modelLower.includes('model s') || modelLower.includes('model 3')) {
        console.log('Categorized as: cars (Tesla sedan)');
        return 'cars';
      } else if (modelLower.includes('model x') || modelLower.includes('model y')) {
        console.log('Categorized as: suvs (Tesla SUV)');
        return 'suvs';
      } else if (modelLower.includes('cybertruck')) {
        console.log('Categorized as: trucks (Tesla truck)');
        return 'trucks';
      }
    }

    // Special handling for other popular electric vehicles
    if (modelLower.includes('leaf') || modelLower.includes('bolt') || modelLower.includes('ioniq')) {
      console.log('Categorized as: cars (electric compact)');
      return 'cars';
    } else if (modelLower.includes('mach-e') || modelLower.includes('id.4') || modelLower.includes('ev6')) {
      console.log('Categorized as: suvs (electric SUV)');
      return 'suvs';
    } else if (modelLower.includes('f-150 lightning') || modelLower.includes('rivian r1t') || modelLower.includes('silverado ev')) {
      console.log('Categorized as: trucks (electric truck)');
      return 'trucks';
    }

    // For other electric vehicles, we'll continue with the categorization based on body style
  }

  // Check for minivans specifically - they should go in cars & minivan category
  if (bodyStyleLower.includes('minivan') ||
      modelLower.includes('odyssey') ||
      modelLower.includes('sienna') ||
      modelLower.includes('pacifica') ||
      modelLower.includes('carnival') ||
      modelLower.includes('sedona')) {
    console.log('Categorized as: cars (minivan)');
    return 'cars';
  }

  // Check for cars, sedans, coupes, etc.
  if (bodyStyleLower.includes('sedan') ||
      bodyStyleLower.includes('coupe') ||
      bodyStyleLower.includes('convertible') ||
      bodyStyleLower.includes('hatchback') ||
      bodyStyleLower.includes('wagon') ||
      bodyStyleLower.includes('sports car') ||
      modelLower.includes('civic') ||
      modelLower.includes('accord') ||
      modelLower.includes('camry') ||
      modelLower.includes('corolla') ||
      modelLower.includes('altima') ||
      modelLower.includes('maxima') ||
      modelLower.includes('malibu') ||
      modelLower.includes('mustang') ||
      modelLower.includes('challenger') ||
      modelLower.includes('charger') ||
      modelLower.includes('911') ||
      modelLower.includes('corvette')) {
    console.log('Categorized as: cars (sedan/coupe/etc)');
    return 'cars';
  }

  // If we can't determine the category, log it and default to cars
  console.log(`Could not determine category for bodyStyle=${bodyStyleLower}, make=${makeLower}, model=${modelLower}, defaulting to: cars`);
  return 'cars';
};

// Computed property to filter vehicles by active category
const featuredVehicles = computed(() => {
  return allVehicles.value.filter(vehicle => vehicle.category === activeCategory.value);
});

// Pagination
const currentPage = ref(1);
const itemsPerPage = 3;
const totalPages = computed(() => Math.ceil(featuredVehicles.value.length / itemsPerPage));

// Reset page when active category changes
watch(activeCategory, () => {
  currentPage.value = 1;
});

// Computed property to get current page items
const currentPageVehicles = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return featuredVehicles.value.slice(start, end);
});

// Debug log for category changes
const logCategoryChange = (categoryId) => {
  console.log(`Category changed to: ${categoryId}`);
  console.log(`Featured vehicles count: ${featuredVehicles.value.length}`);
  console.log(`Current page: ${currentPage.value}`);
  console.log(`Total pages: ${totalPages.value}`);
};

// Navigation functions
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

// Store references to category tabs for position calculations
const categoryTabRefs = reactive({});

// Function to get the position and width of the active tab for the animated indicator
const getActiveTabPosition = () => {
  const activeTab = categoryTabRefs[activeCategory.value];
  if (!activeTab) {
    return { left: 0, width: 0 };
  }
  
  return {
    left: activeTab.offsetLeft,
    width: activeTab.offsetWidth
  };
};

// Reset page when category changes with a clean, elegant animation
const changeCategory = (categoryId) => {
  // Update active category and reset page
  activeCategory.value = categoryId;
  currentPage.value = 1;
  logCategoryChange(categoryId);

  // Wait for DOM to update with new cards using nextTick
  nextTick(() => {
    // First, temporarily remove the animate class to reset animations
    const section = document.querySelector('.featured-vehicles-section');
    if (section) {
      section.classList.remove('animate');

      // Force a reflow to ensure the class removal takes effect
      void section.offsetWidth;

      // Add the animate class back to trigger animations again
      section.classList.add('animate');
    }
  });
};


// Luxury Services data
const services = [
  {
    title: 'Quality Vehicles',
    description: 'Top-quality luxury and performance vehicles, hand-selected to give you the best driving experience possible. Every car meets our strict standards.',
    points: [
      { text: 'Premium vehicles at competitive prices', icon: 'car' },
      { text: 'Thorough quality inspection on all cars', icon: 'shield-check' },
      { text: 'Personalized buying experience', icon: 'user' },
      { text: 'Fair and transparent pricing', icon: 'tag' }
    ],
    ctaText: 'Explore Collection',
    ctaLink: '/inventory',
    image: '/SALESGIRL.png',
    color: '#E11D48', // Updated to match the secondary color
    bgPattern: 'radial-gradient(circle at 10% 20%, rgba(225, 29, 72, 0.05) 0%, rgba(225, 29, 72, 0.1) 90%)'
  },
  {
    title: 'Expert Detailing',
    description: 'Expert detailing services that make your vehicle look its absolute best. Our skilled technicians use top-quality products and proven techniques.',
    points: [
      { text: 'Complete interior and exterior packages', icon: 'sparkles' },
      { text: 'Paint correction and protection', icon: 'brush' },
      { text: 'Interior deep cleaning and restoration', icon: 'home' },
      { text: 'Maintenance plans available', icon: 'calendar' }
    ],
    ctaText: 'Reserve Service',
    ctaLink: '/detailing',
    image: '/DETAILGUY.png',
    color: '#0EA5E9', // Updated to match the accent color
    bgPattern: 'radial-gradient(circle at 90% 20%, rgba(14, 165, 233, 0.05) 0%, rgba(14, 165, 233, 0.1) 90%)'
  },
  {
    title: 'Easy Financing',
    description: 'Get the vehicle you want with financing that works for your budget. We offer competitive rates and flexible terms to make ownership easy.',
    points: [
      { text: 'Custom financing plans', icon: 'chart-bar' },
      { text: 'Competitive lease options', icon: 'credit-card' },
      { text: 'Quick approval process', icon: 'clock' },
      { text: 'Trade-in value maximization', icon: 'refresh' }
    ],
    ctaText: 'Discover Solutions',
    ctaLink: '/financing',
    image: '/FINANCING.png',
    color: '#1E293B', // Updated to match the primary color instead of green
    bgPattern: 'radial-gradient(circle at 50% 80%, rgba(30, 41, 59, 0.05) 0%, rgba(30, 41, 59, 0.1) 90%)'
  }
];

// Reference for services section
const servicesRef = ref(null);
const serviceObservers = ref([]); // Store observers to disconnect later if needed

// Service card hover state
const hoveredService = ref(null);

// Function to handle service card hover
const handleServiceHover = (index) => {
  hoveredService.value = index;
};

// Function to handle service card leave
const handleServiceLeave = () => {
  hoveredService.value = null;
};

// Function to get icon component based on icon name
const getServiceIcon = (iconName) => {
  const icons = {
    'car': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" /><path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-1h3a1 1 0 001-1v-3.05a2.5 2.5 0 010-4.9V4a1 1 0 00-1-1H3z" /></svg>`,
    'shield-check': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>`,
    'user': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" /></svg>`,
    'tag': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" /></svg>`,
    'sparkles': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z" clip-rule="evenodd" /></svg>`,
    'brush': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M4 1h12v2H4z"></path><path d="M7 5V3h6v2l3 3-3 3v8H7v-8L4 8l3-3z"></path></svg>`,
    'home': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" /></svg>`,
    'calendar': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" /></svg>`,
    'chart-bar': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" /></svg>`,
    'credit-card': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" /><path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd" /></svg>`,
    'clock': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" /></svg>`,
    'refresh': `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" /></svg>`
  };

  return icons[iconName] || '';
};

// Simplified function to setup intersection observers for service cards using CSS classes
const setupServiceObservers = () => {
  if (!servicesRef.value) return;

  // Clean up existing observers
  serviceObservers.value.forEach(observer => observer.disconnect());
  serviceObservers.value = [];

  // Get all service cards within the services section
  const serviceCards = Array.from(servicesRef.value.querySelectorAll('.service-card'));
  console.log('Found service cards for observation:', serviceCards.length);

  if (serviceCards.length === 0) {
    console.warn('No service cards found to observe.');
    return; // Exit if no cards are found
  }

  const observerOptions = {
    threshold: 0.15, // Trigger when 15% of the card is visible
    rootMargin: '0px 0px -50px 0px' // Trigger animation slightly before the card is fully in view
  };

  const observer = new IntersectionObserver((entries, obs) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const card = entry.target;
        const index = serviceCards.indexOf(card); // Get index for potential stagger

        console.log(`Service card ${index} intersecting, triggering animation.`);

        // Apply stagger delay using inline style (overrides CSS if needed)
        card.style.transitionDelay = `${index * 120}ms`; // Stagger animation start (120ms interval)

        // Add the 'visible' class to trigger the CSS transition
        card.classList.add('service-card-visible');

        // Unobserve the card after the animation has been triggered
        obs.unobserve(card);
      }
    });
  }, observerOptions);

  // Observe each service card
  serviceCards.forEach(card => {
    observer.observe(card);
  });

  // Store the observer instance for potential cleanup later
  serviceObservers.value.push(observer);
};

// Simplified initializeServices function
const initializeServices = () => {
  // Use nextTick to ensure the DOM is updated before trying to find elements
  nextTick(() => {
      if (!servicesRef.value) {
          console.error('Services ref not found during initialization');
          return;
      }
      console.log('Initializing services section observers for 3D animation.');
      setupServiceObservers(); // Call the observer setup function
  });
};


// Testimonials data
const testimonials = [
  { name: 'Michael Thompson', role: 'Business Owner', content: 'GT Motor Sports provided an exceptional car buying experience. Their knowledge and attention to detail made finding my dream car a pleasure.', image: 'https://randomuser.me/api/portraits/men/32.jpg', rating: 5 },
  { name: 'Sarah Johnson', role: 'Marketing Executive', content: 'I have purchased multiple vehicles from GT Motor Sports over the years. Their inventory is unmatched and the service is always top-notch.', image: 'https://randomuser.me/api/portraits/women/44.jpg', rating: 5 },
  { name: 'David Chen', role: 'Tech Entrepreneur', content: 'The team at GT Motor Sports went above and beyond to help me find the perfect performance car. Their expertise and passion is evident in everything they do.', image: 'https://randomuser.me/api/portraits/men/67.jpg', rating: 4 },
  { name: 'Emily Rodriguez', role: 'Financial Advisor', content: 'The financing process was seamless and transparent. GT Motor Sports helped me secure a great rate and made the paperwork hassle-free.', image: 'https://randomuser.me/api/portraits/women/28.jpg', rating: 5 },
  { name: 'James Wilson', role: 'Doctor', content: 'As a busy professional, I appreciated the efficiency and personalized service. They respected my time and found exactly what I was looking for.', image: 'https://randomuser.me/api/portraits/men/52.jpg', rating: 4 },
  { name: 'Sophia Kim', role: 'Architect', content: 'The attention to detail and quality of vehicles at GT Motor Sports is impressive. They truly understand what luxury car buyers are looking for.', image: 'https://randomuser.me/api/portraits/women/60.jpg', rating: 5 }
];

// Stats data
const statsData = [
  { rawValue: 15, suffix: '+', label: 'Years of Experience' },
  { rawValue: 1000, suffix: '+', label: 'Vehicles Sold' },
  { rawValue: 98, suffix: '%', label: 'Customer Satisfaction' },
  { rawValue: 24, suffix: '/7', label: 'Customer Support' }
];

// Reactive stats for animation
const stats = reactive(statsData.map(stat => ({
  displayValue: '0', finalValue: stat.rawValue + stat.suffix, rawValue: stat.rawValue, suffix: stat.suffix, label: stat.label, hasAnimated: false
})));

// Refs for animation elements
const aboutHeadingRef = ref(null);
const aboutSectionRef = ref(null);
const statsContainerRef = ref(null);
const featuredVehiclesRef = ref(null);

// Animation states
const initialAnimationPlayed = ref(false);

const navigateToInventory = () => { window.location.href = '/inventory'; };
const goTo = (route) => { window.location.href = route; };

const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'CAD', maximumFractionDigits: 0 }).format(price);
};

// Title fade-in animation
const fadeInTitle = (element) => {
  // If element doesn't exist or has already been animated, don't proceed
  if (!element || element.dataset.animated) return;

  // Mark as animated to prevent future animations
  element.dataset.animated = true;

  // Get the text container
  const textContainer = element.querySelector('.text-container');
  
  // Set the full text immediately
  textContainer.textContent = 'About GT Motor Sports';
  
  // Add the fade-in class to animate it
  textContainer.classList.add('title-fade-in');
};

// Count-up animation
const animateStat = (stat, index) => {
  if (stat.hasAnimated) return;
  stat.hasAnimated = true;
  const duration = 1200, frameDuration = 1000 / 60, totalFrames = Math.round(duration / frameDuration);
  const easeOutQuad = t => t * (2 - t);
  let frame = 0; const countTo = stat.rawValue;
  const counter = setInterval(() => {
    frame++; const progress = easeOutQuad(frame / totalFrames);
    const currentCount = Math.round(countTo * progress);
    if (frame >= totalFrames) { // Use >= for safety
      clearInterval(counter); stat.displayValue = stat.rawValue + stat.suffix;
    } else {
      stat.displayValue = currentCount + stat.suffix;
    }
  }, frameDuration);
  return new Promise(resolve => setTimeout(resolve, 100 * index)); // Stagger stats
};

// Animation for featured vehicles is now handled directly in the Intersection Observer

// This section previously contained video playback setup code
// It has been removed since we're now using the AboutGamePreview component

// Set up intersection observers for various sections
const setupObservers = () => {
  const observerOptions = (threshold = 0.3) => ({ root: null, rootMargin: '0px', threshold });

  // About Section Observer (Fade-in and Content Animation)
  if (aboutSectionRef.value) {
    const aboutObserver = new IntersectionObserver((entries, obs) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && aboutHeadingRef.value) {
          // Add a small delay to ensure the section is properly in view
          setTimeout(() => {
            // Fade in the title when the section comes into view
            fadeInTitle(aboutHeadingRef.value);
            
            // Animate the about content
            const aboutContent = document.querySelector('.about-content');
            if (aboutContent) {
              aboutContent.classList.add('visible');
            }
            
            // Animate the 3D preview with a slight delay
            setTimeout(() => {
              const about3DPreview = document.querySelector('.about-3d-preview');
              if (about3DPreview) {
                about3DPreview.classList.add('visible');
              }
            }, 200);
          }, 150);

          // Unobserve after triggering the animation
          obs.unobserve(entry.target);
        }
      });
    }, {
      root: null,
      rootMargin: '100px 0px', // Start observing a bit earlier
      threshold: 0.15 // Trigger with 15% visibility
    });
    aboutObserver.observe(aboutSectionRef.value);
  }

  // Stats Observer (Count-up)
  if (statsContainerRef.value) {
    const statsObserver = new IntersectionObserver((entries, obs) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          stats.forEach((stat, index) => animateStat(stat, index));
          obs.unobserve(entry.target);
        }
      });
    }, observerOptions(0.5)); // Trigger when half visible
    statsObserver.observe(statsContainerRef.value);
  }

  // This section previously contained video observer code
  // It has been removed since we're now using the AboutGamePreview component

  // Featured Vehicles Observer (Initial Load Animation)
  // We'll set up the observer in onMounted to ensure it works properly

  // Services Observer (Handled by initializeServices)
  // No need to observe servicesRef here directly anymore
};


// Hero title animation
const animateHeroTitle = () => {
  const words = document.querySelectorAll('.hero-title-word');
  const separators = document.querySelectorAll('.hero-title-separator');
  words.forEach((word, index) => { word.style.animationDelay = `${0.5 + (index * 0.4)}s`; });
  separators.forEach((separator, index) => { separator.style.animationDelay = `${0.7 + (index * 0.4)}s`; });
};

onMounted(async () => {
  // Fetch vehicles first to ensure they're loaded
  await fetchVehicles();
  // --- 1. ADD DYNAMIC CSS STYLES FIRST ---
  // This ensures the @keyframes and animation rules exist before JS tries to use them.
  const style = document.createElement('style');
  style.textContent = `
    /* Base Styles & Utilities */
    .title-fade-in {
      opacity: 0;
      animation: fadeIn 0.6s ease-out forwards;
      will-change: opacity;
    }
    .line-clamp-2 { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; }
    .line-clamp-4 { display: -webkit-box; -webkit-line-clamp: 4; -webkit-box-orient: vertical; overflow: hidden; }
    .blur-3xl { filter: blur(64px); }
    
    /* About Section Animations */
    .about-content {
      opacity: 0;
      transform: translateY(30px);
      transition: opacity 0.7s ease-out, transform 0.7s ease-out;
      will-change: opacity, transform;
    }
    .about-content.visible {
      opacity: 1;
      transform: translateY(0);
    }
    .about-content .flex-col > * {
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.5s ease-out, transform 0.5s ease-out;
      will-change: opacity, transform;
    }
    .about-content.visible .flex-col > *:nth-child(1) {
      opacity: 1;
      transform: translateY(0);
      transition-delay: 0.15s;
    }
    .about-content.visible .flex-col > *:nth-child(2) {
      opacity: 1;
      transform: translateY(0);
      transition-delay: 0.3s;
    }
    .about-content.visible .flex-col > *:nth-child(3) {
      opacity: 1;
      transform: translateY(0);
      transition-delay: 0.45s;
    }
    .about-content.visible .flex-col > *:nth-child(4) {
      opacity: 1;
      transform: translateY(0);
      transition-delay: 0.6s;
    }
    
    /* About 3D Preview Animation */
    .about-3d-preview {
      opacity: 0;
      transform: translateX(30px);
      transition: opacity 0.8s ease-out, transform 0.8s ease-out;
      will-change: opacity, transform;
    }
    .about-3d-preview.visible {
      opacity: 1;
      transform: translateX(0);
    }

    /* Hero Animations */
    .hero-title-word {
      display: inline-block;
      opacity: 0;
      transform: translateY(40px);
      /* Animation rule applied directly */
      animation: fadeInUp 1.2s cubic-bezier(0.19, 1, 0.22, 1) forwards;
      letter-spacing: 0.05em;
      font-weight: 700;
      /* Delay will be overridden by JS */
    }
    .hero-title-separator {
      display: inline-block;
      opacity: 0;
      margin: 0 0.5rem;
      color: #e30b13;
      /* Animation rule applied directly */
      animation: fadeIn 0.8s ease forwards;
      font-size: 0.8em;
      vertical-align: middle;
      /* Delay will be overridden by JS */
    }
    .animated-border-button { position: relative; border: none; background-color: transparent; z-index: 1; overflow: visible; }
    .animated-border-button::before, .animated-border-button::after, .animated-border-button span { content: ''; position: absolute; background-color: #e30b13; }
    /* Keep original button delays to maintain relative timing */
    .animated-border-button::before { top: 0; left: 0; width: 0; height: 3px; animation: drawTopBorder 0.6s ease forwards 1.5s; }
    .animated-border-button span:nth-child(1) { top: 0; left: 0; width: 3px; height: 0; animation: drawLeftBorder 0.6s ease forwards 2.1s; }
    .animated-border-button::after { bottom: 0; right: 0; width: 0; height: 3px; animation: drawBottomBorder 0.6s ease forwards 2.7s; }
    .animated-border-button span:nth-child(2) { bottom: 0; right: 0; width: 3px; height: 0; animation: drawRightBorder 0.6s ease forwards 3.3s; }

    /* Featured Vehicle Animations - Refined */
    .featured-vehicle-card {
      opacity: 0;
      will-change: transform, opacity, box-shadow;
      transition: transform 0.8s cubic-bezier(0.19, 1, 0.22, 1),
                  opacity 0.8s cubic-bezier(0.19, 1, 0.22, 1),
                  box-shadow 0.3s ease;
      animation: cardReveal 1.2s cubic-bezier(0.22, 1, 0.36, 1) forwards;
      animation-play-state: paused;
    }

    /* Staggered animation delays for cards */
    .featured-vehicle-card:nth-child(1) { animation-delay: 0.1s; }
    .featured-vehicle-card:nth-child(2) { animation-delay: 0.3s; }
    .featured-vehicle-card:nth-child(3) { animation-delay: 0.5s; }

    /* Animation plays when section is in view */
    .featured-vehicles-section.animate .featured-vehicle-card {
      animation-play-state: running;
    }

    .featured-vehicle-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }

    @keyframes cardReveal {
      0% {
        opacity: 0;
        transform: scale(0.95);
        filter: blur(8px);
      }
      100% {
        opacity: 1;
        transform: scale(1);
        filter: blur(0);
      }
    }

    /* Section Animation */
    .featured-vehicles-header {
      opacity: 0;
      transform: translateY(30px);
      animation: headerFadeIn 1s cubic-bezier(0.19, 1, 0.22, 1) forwards;
      animation-play-state: paused;
    }

    .featured-vehicles-section.animate .featured-vehicles-header {
      animation-play-state: running;
    }

    @keyframes headerFadeIn {
      0% {
        opacity: 0;
        transform: translateY(30px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .category-tab {
      opacity: 0;
      position: relative;
      overflow: visible;
      transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      animation: tabReveal 0.8s cubic-bezier(0.22, 1, 0.36, 1) forwards;
      animation-play-state: paused;
    }

    /* Staggered animation delays for tabs */
    .category-tab:nth-child(1) { animation-delay: 0.1s; }
    .category-tab:nth-child(2) { animation-delay: 0.2s; }
    .category-tab:nth-child(3) { animation-delay: 0.3s; }
    .category-tab:nth-child(4) { animation-delay: 0.4s; }

    .featured-vehicles-section.animate .category-tab {
      animation-play-state: running;
    }

    @keyframes tabReveal {
      0% {
        opacity: 0;
        transform: scale(0.9);
        filter: blur(5px);
      }
      100% {
        opacity: 1;
        transform: scale(1);
        filter: blur(0);
      }
    }

    .pagination-indicator {
      opacity: 0;
      transform: scaleX(0.5);
      transform-origin: center;
      animation: indicatorFadeIn 0.6s cubic-bezier(0.19, 1, 0.22, 1) forwards;
      animation-delay: 0.8s;
      animation-play-state: paused;
    }

    .featured-vehicles-section.animate .pagination-indicator {
      animation-play-state: running;
    }

    @keyframes indicatorFadeIn {
      0% {
        opacity: 0;
        transform: scaleX(0.5);
      }
      100% {
        opacity: 1;
        transform: scaleX(1);
      }
    }

    /* Animation trigger */
    .animation-trigger {
      position: absolute;
      top: -100px;
      left: 0;
      width: 100%;
      height: 1px;
      z-index: -1;
    }

    /* Loading Animation Styles */
    .dot-1, .dot-2, .dot-3 {
      opacity: 0;
      animation: dotFade 1.5s infinite;
    }

    .dot-2 {
      animation-delay: 0.5s;
    }

    .dot-3 {
      animation-delay: 1s;
    }

    @keyframes dotFade {
      0%, 100% { opacity: 0; }
      50% { opacity: 1; }
    }

    .loading-shimmer {
      background-size: 200% 100%;
      animation: loadingShimmer 2s infinite linear;
    }

    @keyframes loadingShimmer {
      0% {
        transform: translateX(-100%);
      }
      100% {
        transform: translateX(100%);
      }
    }

    /* Featured section animation */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Luxury Service Section Animations */
    @keyframes moveGradient { 0% { background-position: 0% 50%; } 100% { background-position: 100% 50%; } }
    .animate-gradient-x { animation: gradient-x 8s ease-in-out infinite; }
    @keyframes gradient-x { 0% { background-position: 0% 50%; } 50% { background-position: 100% 50%; } 100% { background-position: 0% 50%; } }

    /* --- Premium Service Card Animation --- */
    .service-grid { perspective: 1500px; }
    .service-card {
      transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);
      will-change: transform, opacity;
      opacity: 1;
      transform: translateY(0);
      transition-delay: 0ms !important;
    }
    .js-animation-ready .service-card {
      opacity: 0;
      transform: translateY(40px);
    }
    .js-animation-ready .service-card.service-card-visible {
      opacity: 1;
      transform: translateY(0);
    }

    /* Refined hover effects */
    .service-card:hover {
      z-index: 10;
    }

    /* Subtle shimmer effect for luxury feel */
    @keyframes shimmer {
      0% { background-position: -100% 0; }
      100% { background-position: 200% 0; }
    }

    .service-card::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      transform: rotate(30deg);
      background-size: 50% 100%;
      background-repeat: no-repeat;
      background-position: -100% 0;
      animation: shimmer 6s infinite;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.5s ease;
    }

    .service-card:hover::after {
      opacity: 1;
    }
    /* --- End Service Card 3D Animation --- */

    /* Tab transition effect */
    .tab-transition {
      animation: tabPulse 0.5s ease-out;
    }
    
    @keyframes tabPulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }
    
    /* General Animations & Keyframes */
    @keyframes float { 0% { transform: translateY(0) translateX(0); } 25% { transform: translateY(-10px) translateX(10px); } 50% { transform: translateY(-20px) translateX(0); } 75% { transform: translateY(-10px) translateX(-10px); } 100% { transform: translateY(0) translateX(0); } }
    .animate-float { animation: float 6s ease-in-out infinite; }
    @keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.05); } 100% { transform: scale(1); } }
    @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
    @keyframes textGradient { 0% { background-position: 0% center; } 100% { background-position: 200% center; } }
    .text-gradient-animate { background-size: 200% auto; background-clip: text; -webkit-background-clip: text; -webkit-text-fill-color: transparent; animation: textGradient 3s linear infinite; }
    @keyframes drawTopBorder { 0% { width: 0; } 100% { width: 100%; } }
    @keyframes drawBottomBorder { 0% { width: 0; } 100% { width: 100%; } }
    @keyframes drawLeftBorder { 0% { height: 0; } 100% { height: 100%; } }
    @keyframes drawRightBorder { 0% { height: 0; } 100% { height: 100%; } }
    @keyframes fadeInUp { from { opacity: 0; transform: translateY(40px); } to { opacity: 1; transform: translateY(0); } }
    @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
    .floating { animation: floating 3s ease-in-out infinite; }
    @keyframes floating { 0% { transform: translateY(0px); } 50% { transform: translateY(-10px); } 100% { transform: translateY(0px); } }

    /* Shine animation for premium elements */
    @keyframes shine {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }
    .animate-shine {
      animation: shine 3s infinite;
      animation-delay: 2s;
    }

    /* Metric Card Glowing Border Animation */
    .metric-card-wrapper {
      transform-style: preserve-3d;
      perspective: 1000px;
      overflow: visible;
      position: relative;
      height: 100%;
    }

    .metric-card-border {
      position: absolute;
      inset: -3px;
      border-radius: 2px;
      z-index: 1;
      pointer-events: none;
      background: linear-gradient(90deg,
                rgba(var(--color-secondary-rgb), 0.5),
                rgba(var(--color-accent-rgb), 0.5),
                rgba(var(--color-primary-rgb), 0.3),
                rgba(var(--color-secondary-rgb), 0.5));
      background-size: 300% 100%;
      background-position: 0% 0%;
      opacity: 0.5;
      transition: all 0.5s ease;
      animation: borderGlow 4s infinite alternate, borderMove 8s linear infinite;
    }

    .group:hover .metric-card-border {
      opacity: 0.7;
      animation: borderGlowHover 2s infinite alternate, borderMoveHover 4s linear infinite;
    }

    @keyframes borderMove {
      0% { background-position: 0% 0%; }
      100% { background-position: 300% 0%; }
    }

    @keyframes borderMoveHover {
      0% { background-position: 0% 0%; }
      100% { background-position: 300% 0%; }
    }

    @keyframes borderGlow {
      0% {
        filter: blur(5px);
        box-shadow: 0 0 5px rgba(var(--color-secondary-rgb), 0.2),
                    0 0 10px rgba(var(--color-accent-rgb), 0.2);
      }
      100% {
        filter: blur(7px);
        box-shadow: 0 0 10px rgba(var(--color-secondary-rgb), 0.3),
                    0 0 15px rgba(var(--color-accent-rgb), 0.3);
      }
    }

    @keyframes borderGlowHover {
      0% {
        filter: blur(6px);
        box-shadow: 0 0 10px rgba(var(--color-secondary-rgb), 0.3),
                    0 0 15px rgba(var(--color-accent-rgb), 0.3);
      }
      100% {
        filter: blur(8px);
        box-shadow: 0 0 15px rgba(var(--color-secondary-rgb), 0.4),
                    0 0 20px rgba(var(--color-accent-rgb), 0.4);
      }
    }

    /* Testimonials Scroll */
    .testimonials-scroll-container { overflow: hidden; }
    .testimonials-scroll { display: flex; width: max-content; animation: scroll 60s linear infinite; }
    .testimonials-scroll:hover { animation-play-state: paused; }
    @keyframes scroll { 0% { transform: translateX(0); } 100% { transform: translateX(-50%); } }
    .testimonial-card { flex-shrink: 0; }
  `;
  document.head.appendChild(style);

  // Add JS readiness class (can be here or later, but early is fine)
  document.body.classList.add('js-animation-ready');

  // --- 2. TRIGGER HERO ANIMATION ---
  // Use nextTick to ensure the DOM elements are rendered after mount AND CSS is applied.
  nextTick(() => {
    console.log("Attempting to animate hero title...");
    animateHeroTitle(); // Apply delays now
  });

  // --- 3. FETCH DATA AND DO OTHER SETUP (can run concurrently with animation) ---
  try {
    // Fetch vehicles - this no longer blocks the hero animation start
    await fetchVehicles();
  } catch (error) {
    console.error("Error during initial vehicle fetch:", error);
  }

  // Use nextTick for observer setup to ensure elements dependent on fetched data are ready
  nextTick(() => {
    // Set up the featured vehicles observer directly here for better reliability
    const featuredVehiclesSection = document.querySelector('.featured-vehicles-section');
    if (featuredVehiclesSection) {
      console.log('Setting up featured vehicles observer');
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !initialAnimationPlayed.value) {
            console.log('Featured vehicles section is visible, adding animate class');
            featuredVehiclesSection.classList.add('animate');
            initialAnimationPlayed.value = true;
            observer.unobserve(entry.target);
          }
        });
      }, {
        threshold: 0.1, // Trigger when 10% of the section is visible
        rootMargin: '0px 0px -10% 0px' // Trigger animation before the section is fully in view
      });

      observer.observe(featuredVehiclesSection);
    }

    setupObservers();
    initializeServices(); // This uses nextTick internally
  });
});

</script>

<template>
  <!-- Hero Section - Mobile Optimized -->
  <section class="relative h-[85vh] sm:h-screen flex items-end justify-center overflow-hidden pb-16 sm:pb-20">
    <!-- Background Video - Optimized for mobile -->
    <div class="absolute inset-0 z-0">
      <video autoplay loop muted playsinline class="w-full h-full object-cover">
        <source src="/HEROBG.mp4" type="video/mp4">
        <img src="https://images.unsplash.com/photo-1552519507-da3b142c6e3d?auto=format&fit=crop&w=1470&q=80" alt="Luxury car" class="w-full h-full object-cover"/>
      </video>
      <div class="absolute inset-x-0 bottom-0 h-1/3 bg-gradient-to-t from-black/80 to-transparent z-10"></div>
    </div>
    <!-- Hero Content - Mobile Optimized -->
    <div class="container-custom relative z-20 text-white text-center px-4 sm:px-6">
      <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-4 leading-tight overflow-hidden">
        <span class="hero-title-word opacity-0">Sales</span>
        <span class="hero-title-separator opacity-0 mx-1 sm:mx-2">•</span>
        <span class="hero-title-word opacity-0">Finance</span>
        <span class="hero-title-separator opacity-0 mx-1 sm:mx-2">•</span>
        <span class="hero-title-word opacity-0">Trade</span>
      </h1>
      <div class="flex justify-center mt-6 sm:mt-8">
        <button @click="navigateToInventory" class="animated-border-button px-6 sm:px-8 py-3 text-white hover:bg-secondary hover:text-white transition-all duration-300 tracking-wider uppercase text-xs sm:text-sm font-medium flex items-center">
          <span></span>
          <span></span>
          EXPLORE INVENTORY
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 ml-2 sm:ml-3 animate-bounce" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" /></svg>
        </button>
      </div>
    </div>
  </section>

  <!-- Featured Vehicles Section - Premium Design - Mobile Optimized -->
  <section class="relative overflow-hidden py-16 sm:py-20 md:py-24 featured-vehicles-section" ref="featuredVehiclesRef">
    <!-- Premium background with refined gradient -->
    <div class="absolute inset-0 bg-gradient-to-b from-gray-50 via-gray-100/90 to-white"></div>
    
    <!-- Luxury pattern overlay - matching other sections -->
    <div class="absolute inset-0 opacity-5"
         style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23000000\' fill-opacity=\'0.15\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');">
    </div>
    
    <!-- Premium accent elements - consistent with other sections -->
    <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary/40 to-transparent"></div>
    <div class="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary/20 to-transparent"></div>
    
    <!-- Animation trigger element -->
    <div class="animation-trigger"></div>

    <div class="relative z-10 container-custom px-4 sm:px-6">
      <!-- Luxury section header - matched to other sections - Mobile Optimized -->
      <div class="text-center mb-10 sm:mb-16 relative max-w-6xl mx-auto featured-vehicles-header">
        <div class="flex justify-center items-center mb-3">
          <div class="h-px w-8 sm:w-12 bg-secondary opacity-50"></div>
          <span class="mx-2 sm:mx-4 text-xs sm:text-sm uppercase tracking-[0.2em] sm:tracking-[0.25em] font-medium text-secondary">
            Exceptional Collection
          </span>
          <div class="h-px w-8 sm:w-12 bg-secondary opacity-50"></div>
        </div>

        <h2 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-4 sm:mb-6 text-gray-900 relative inline-block tracking-tight">
          Featured Vehicles
        </h2>
        
        <div class="h-px w-16 sm:w-24 bg-secondary mx-auto mb-6 sm:mb-8"></div>
      </div>

      <!-- Enhanced category navigation with premium styling - Mobile Optimized -->
      <div class="flex justify-center mb-8 sm:mb-12 overflow-x-auto pb-2 featured-categories-nav -mx-4 px-4 sm:mx-0 sm:px-0">
        <div class="inline-flex bg-white shadow-lg border border-gray-200 rounded-full relative">
          <!-- Clean, subtle background indicator for active tab -->
          <div
            class="absolute h-full bg-white/90 rounded-full z-0 transition-all duration-500 ease-out"
            :style="{
              left: `${getActiveTabPosition().left}px`,
              width: `${getActiveTabPosition().width}px`,
              opacity: '1',
              transform: 'scale(1)',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
            }"
          ></div>
          
          <button
            v-for="category in vehicleCategories" :key="category.id"
            @click="changeCategory(category.id)"
            :class="[
              'py-2 sm:py-3 px-3 sm:px-6 md:px-8 text-center text-xs sm:text-sm md:text-base font-medium transition-all duration-300 category-tab relative z-10 whitespace-nowrap',
              activeCategory === category.id
                ? 'text-secondary font-bold active'
                : 'text-gray-700 hover:text-secondary hover:scale-105'
            ]"
            :ref="el => { if (el) categoryTabRefs[category.id] = el }"
          >
            <!-- Category name -->
            <span>{{ category.name }}</span>
          </button>
        </div>
      </div>

      <!-- Enhanced Loading State with Premium Animation -->
      <div v-if="isLoadingVehicles" class="text-center py-16 relative">
        <!-- Animated background glow -->
        <div class="absolute inset-0 overflow-hidden">
          <div class="absolute -inset-10 bg-gradient-to-r from-secondary/5 via-accent/5 to-secondary/5 blur-3xl opacity-50 animate-pulse"></div>
        </div>

        <!-- Premium loading animation -->
        <div class="relative z-10">
          <!-- Animated spinner with glow effect -->
          <div class="relative inline-block mb-6">
            <div class="absolute -inset-1 rounded-full bg-gradient-to-r from-secondary/30 via-accent/30 to-secondary/30 blur-md opacity-70 animate-pulse"></div>
            <div class="relative">
              <!-- Outer spinner -->
              <div class="animate-spin rounded-full h-16 w-16 border-4 border-t-secondary border-r-accent border-b-secondary border-l-accent opacity-80"></div>
              <!-- Inner spinner (counter-rotating) -->
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="animate-spin rounded-full h-10 w-10 border-4 border-t-accent border-r-secondary border-b-accent border-l-secondary opacity-80" style="animation-direction: reverse; animation-duration: 1.5s;"></div>
              </div>
              <!-- Center dot -->
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="h-4 w-4 rounded-full bg-secondary"></div>
              </div>
            </div>
          </div>

          <!-- Loading text with shimmer effect -->
          <div class="relative">
            <p class="text-2xl font-heading font-bold text-gray-700 mb-2">Loading Premium Vehicles</p>
            <p class="text-gray-500 relative overflow-hidden">
              <span>Preparing your luxury experience</span>
              <span class="inline-block w-6">
                <span class="dot-1">.</span>
                <span class="dot-2">.</span>
                <span class="dot-3">.</span>
              </span>
            </p>

            <!-- Shimmer overlay -->
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent loading-shimmer"></div>
          </div>
        </div>
      </div>
      <div v-else-if="allVehicles.length === 0" class="text-center py-16">
        <p class="text-xl text-gray-600">No vehicles found in our inventory.</p>
      </div>
      <div v-else-if="featuredVehicles.length === 0" class="text-center py-16">
        <p class="text-xl text-gray-600">No vehicles found in this category.</p>
      </div>
      <div v-else-if="currentPageVehicles.length === 0" class="text-center py-16">
        <p class="text-xl text-gray-600">No vehicles found on this page.
          <button @click="currentPage = 1" class="text-secondary underline">Go to first page</button>
        </p>
      </div>

      <!-- REFINED PREMIUM VEHICLE DISPLAY -->
      <div v-else class="relative mb-16 max-w-7xl mx-auto">
        <!-- Featured Vehicle Showcase - Spotlight Design -->
        <div v-if="currentPageVehicles.length > 0" class="mb-16">
          <!-- Spotlight Vehicle (First vehicle gets special treatment) - Mobile Optimized -->
          <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6 lg:gap-8 featured-vehicle-spotlight">
            <!-- Large Image Column - Mobile Optimized -->
            <div class="lg:col-span-7 relative overflow-hidden featured-vehicle-card group">
              <!-- Premium card styling with border gradient - matching other sections -->
              <div class="absolute -inset-px bg-gradient-to-r from-secondary/20 via-secondary/10 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              
              <div class="relative h-72 sm:h-96 lg:h-[500px] overflow-hidden rounded-lg shadow-xl border border-gray-100 group-hover:border-secondary/30 transition-all duration-500">
                <!-- Premium image display -->
                <img
                  :src="currentPageVehicles[0].image || '/images/no-image-available.jpg'"
                  :alt="currentPageVehicles[0].mainTitle"
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                  @error="$event.target.src = '/images/no-image-available.jpg'"
                />
                
                <!-- Premium gradient overlay -->
                <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                
                <!-- Floating badges with premium styling - Mobile Optimized -->
                <div class="absolute top-3 sm:top-4 right-3 sm:right-4 z-20 flex space-x-2 sm:space-x-3">
                  <!-- Year badge -->
                  <div class="bg-secondary text-white py-1 sm:py-1.5 px-3 sm:px-4 rounded-sm shadow-lg backdrop-blur-sm">
                    <span class="text-white text-xs sm:text-sm font-bold">{{ currentPageVehicles[0].year }}</span>
                  </div>
                  
                  <!-- Category badge -->
                  <div class="bg-black/80 text-white px-3 sm:px-4 py-1 sm:py-1.5 rounded-sm shadow-lg backdrop-blur-sm border-l-2 border-secondary">
                    <span class="text-xs sm:text-sm font-medium uppercase tracking-wider">
                      {{ currentPageVehicles[0].vehicleType }}
                    </span>
                  </div>
                </div>
                
                <!-- Floating price tag with premium styling - Mobile Optimized -->
                <div class="absolute bottom-3 sm:bottom-4 right-3 sm:right-4 z-20">
                  <div class="bg-white/90 backdrop-blur-sm text-secondary px-3 sm:px-5 py-2 sm:py-3 rounded-sm shadow-lg">
                    <div class="text-xl sm:text-2xl font-bold">{{ formatPrice(currentPageVehicles[0].price) }}</div>
                    <div class="text-xs text-gray-600 uppercase text-center tracking-wider">MSRP</div>
                  </div>
                </div>
                
                <!-- Floating specs with premium styling - Mobile Optimized -->
                <div class="absolute bottom-3 sm:bottom-4 left-3 sm:left-4 z-20 flex space-x-2 sm:space-x-3">
                  <div class="bg-white/90 backdrop-blur-sm px-3 sm:px-4 py-1.5 sm:py-2 rounded-sm shadow-lg">
                    <div class="text-base sm:text-lg font-bold text-gray-800">{{ currentPageVehicles[0].mpgCity }}</div>
                    <div class="text-[10px] sm:text-xs text-gray-600 uppercase tracking-wider">City MPG</div>
                  </div>
                  <div class="bg-white/90 backdrop-blur-sm px-3 sm:px-4 py-1.5 sm:py-2 rounded-sm shadow-lg">
                    <div class="text-base sm:text-lg font-bold text-gray-800">{{ currentPageVehicles[0].mpgHighway }}</div>
                    <div class="text-[10px] sm:text-xs text-gray-600 uppercase tracking-wider">Highway MPG</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Vehicle Details Column - Mobile Optimized -->
            <div class="lg:col-span-5 flex flex-col justify-center featured-vehicle-card group mt-4 lg:mt-0">
              <!-- Premium card styling with border gradient - matching other sections -->
              <div class="absolute -inset-px bg-gradient-to-r from-secondary/20 via-secondary/10 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              
              <div class="bg-white p-5 sm:p-6 lg:p-8 shadow-lg rounded-lg border border-gray-100 group-hover:border-secondary/30 transition-all duration-500 h-full flex flex-col relative">
                <!-- Premium vehicle title - Mobile Optimized -->
                <div class="mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-gray-100">
                  <h3 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-1 sm:mb-2">{{ currentPageVehicles[0].mainTitle }}</h3>
                  <p v-if="currentPageVehicles[0].trim" class="text-base sm:text-lg text-gray-600">{{ currentPageVehicles[0].trim }}</p>
                </div>
                
                <!-- Premium vehicle description - Mobile Optimized -->
                <p class="text-gray-700 text-base sm:text-lg mb-4 sm:mb-6 lg:mb-8 flex-grow font-light leading-relaxed">
                  {{ currentPageVehicles[0].tagline || 'Experience the perfect blend of performance, comfort, and style with this exceptional vehicle.' }}
                </p>
                
                <!-- Key features list - Mobile Optimized -->
                <div class="mb-5 sm:mb-6 lg:mb-8">
                  <h4 class="text-base sm:text-lg font-bold text-gray-900 mb-3 sm:mb-4">Key Features</h4>
                  <ul class="grid grid-cols-2 gap-2 sm:gap-3">
                    <li class="flex items-center text-gray-700 text-sm sm:text-base group-hover:translate-x-1 transition-transform duration-300">
                      <div class="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-secondary/10 flex items-center justify-center mr-2 sm:mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <span>Premium Interior</span>
                    </li>
                    <li class="flex items-center text-gray-700 text-sm sm:text-base group-hover:translate-x-1 transition-transform duration-300">
                      <div class="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-secondary/10 flex items-center justify-center mr-2 sm:mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <span>Advanced Safety</span>
                    </li>
                    <li class="flex items-center text-gray-700 text-sm sm:text-base group-hover:translate-x-1 transition-transform duration-300">
                      <div class="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-secondary/10 flex items-center justify-center mr-2 sm:mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <span>Fuel Efficient</span>
                    </li>
                    <li class="flex items-center text-gray-700 text-sm sm:text-base group-hover:translate-x-1 transition-transform duration-300">
                      <div class="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-secondary/10 flex items-center justify-center mr-2 sm:mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <span>Powerful Engine</span>
                    </li>
                  </ul>
                </div>
                
                <!-- Premium action buttons - Mobile Optimized, Desktop Side-by-Side -->
                <div class="flex flex-col md:flex-row gap-3 sm:gap-4">
                  <a
                    :href="`/vehicle/${currentPageVehicles[0].id}`"
                    class="relative overflow-hidden bg-secondary text-white px-4 sm:px-6 py-3 sm:py-3.5 font-medium hover:bg-secondary/90 transition-all duration-300 flex-grow text-center flex items-center justify-center rounded-sm shadow-lg group"
                  >
                    <span class="relative z-10 flex items-center text-sm sm:text-base">
                      View Details
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </span>
                    <!-- Shine effect -->
                    <div class="absolute top-0 -left-full w-1/2 h-full bg-white/20 transform skew-x-12 transition-transform duration-1000 group-hover:left-full"></div>
                  </a>
                  <button
                    class="relative overflow-hidden bg-white text-gray-700 px-4 sm:px-6 py-3 sm:py-3.5 font-medium hover:bg-gray-50 transition-all duration-300 flex items-center justify-center border border-gray-200 rounded-sm shadow-sm group"
                  >
                    <span class="relative z-10 flex items-center text-sm sm:text-base">
                      Inquire Now
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </span>
                    <!-- Subtle shine effect -->
                    <div class="absolute top-0 -left-full w-1/2 h-full bg-secondary/10 transform skew-x-12 transition-transform duration-1000 group-hover:left-full"></div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        

        <!-- Navigation buttons - Mobile Optimized -->
        <button
          @click="prevPage"
          :disabled="currentPage === 1"
          class="absolute left-0 sm:-left-2 md:-left-4 top-1/2 -translate-y-1/2 z-10 w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-white text-secondary border border-gray-200 flex items-center justify-center disabled:opacity-0 hover:bg-secondary hover:text-white transition-all duration-300"
          :class="{ 'opacity-0': currentPage === 1, 'opacity-100': currentPage > 1 }"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="absolute right-0 sm:-right-2 md:-right-4 top-1/2 -translate-y-1/2 z-10 w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-white text-secondary border border-gray-200 flex items-center justify-center disabled:opacity-0 hover:bg-secondary hover:text-white transition-all duration-300"
          :class="{ 'opacity-0': currentPage === totalPages, 'opacity-100': currentPage < totalPages }"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>

        <!-- Premium pagination indicator - Mobile Optimized -->
        <div v-if="totalPages > 1" class="flex justify-center mt-8 sm:mt-10 lg:mt-12 space-x-3 sm:space-x-4 max-w-7xl mx-auto">
          <div
            v-for="page in totalPages"
            :key="page"
            @click="currentPage = page"
            class="cursor-pointer group"
          >
            <div class="relative">
              <!-- Indicator background with glow effect for active page -->
              <div
                v-if="currentPage === page"
                class="absolute -inset-1 bg-gradient-to-r from-secondary/30 to-secondary/20 rounded-full blur-sm opacity-70">
              </div>
              
              <!-- Indicator dot - Mobile Optimized -->
              <div
                :class="[
                  'transition-all duration-300 rounded-full',
                  currentPage === page
                    ? 'bg-secondary w-2.5 h-2.5 sm:w-3 sm:h-3 relative z-10'
                    : 'bg-gray-300 w-2 h-2 group-hover:bg-gray-400'
                ]"
              ></div>
            </div>
          </div>
        </div>
      </div>

      
    </div>
  </section>

  <!-- About Section - Ferrari-inspired Premium Design -->
  <BusinessInfoProvider v-slot="{ description, history }">
    <section id="about" class="py-28 relative overflow-hidden" ref="aboutSectionRef">
      <!-- Luxury background with subtle pattern -->
      <div class="absolute inset-0 bg-gradient-to-b from-white to-gray-50"></div>
      <div class="absolute inset-0 opacity-3" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23000000\' fill-opacity=\'0.05\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>



      <div class="container-custom relative z-10">
        <!-- Luxury Section Header -->
        <div class="text-center mb-16">
          <div class="flex justify-center items-center mb-3">
            <div class="h-px w-12 bg-secondary opacity-50"></div>
            <span class="mx-4 text-sm uppercase tracking-[0.25em] font-medium text-secondary">Our Legacy</span>
            <div class="h-px w-12 bg-secondary opacity-50"></div>
          </div>
          <h2 class="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-6 text-gray-900 relative inline-block tracking-tight" ref="aboutHeadingRef">
            <!-- Text container for the fade-in animation -->
            <span class="text-container" style="min-height: 1.2em; min-width: 100%; display: inline-block;"></span>
            <!-- Hidden text that maintains dimensions without using absolute positioning -->
            <span aria-hidden="true" style="visibility: hidden; height: 0; display: block; overflow: hidden;">About GT Motor Sports</span>
          </h2>
          <div class="h-px w-24 bg-secondary mx-auto mb-8"></div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-16 items-center">
          <div class="about-content lg:col-span-1 opacity-0">
            <!-- Use business description if available, otherwise fallback to default text -->
            <div v-if="description" class="prose max-w-none">
              <div class="flex flex-col space-y-6">
                <!-- Stylish headline -->
                <h3 class="text-2xl font-bold text-secondary tracking-wide">ELEVATE YOUR <span class="text-primary">DRIVING EXPERIENCE</span></h3>
                
                
                
                
                
                <!-- 3D Experience Highlight -->
                <div class="bg-gradient-to-r from-primary/5 to-secondary/5 p-5 rounded-md border-l-4 border-secondary mt-6">
                  <p class="text-lg font-semibold text-gray-800">
                    Experience our revolutionary <span class="text-secondary">interactive 3D showroom</span> where cutting-edge technology meets automotive passion, allowing you to explore every detail of our exceptional vehicles from anywhere.
                  </p>
                </div>
              </div>
            </div>
            <div v-else-if="history" class="prose max-w-none">
              <div class="flex flex-col space-y-6">
                <!-- Stylish headline -->
                <h3 class="text-2xl font-bold text-secondary tracking-wide">ELEVATE YOUR <span class="text-primary">DRIVING EXPERIENCE</span></h3>
                
                <!-- Feature points with icons -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 animate-fade-in" style="animation-delay: 0.4s; animation-play-state: paused;">
                  <div class="flex items-start">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <p class="text-base text-gray-700 font-medium">Curated Collection of Elite Performance & Luxury Vehicles, Each Handpicked for Excellence</p>
                  </div>
                  
                  <div class="flex items-start">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                      </svg>
                    </div>
                    <p class="text-base text-gray-700 font-medium">Passionate Automotive Specialists with Decades of Combined Industry Expertise</p>
                  </div>
                </div>
                
                <!-- Additional feature points -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div class="flex items-start">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <p class="text-base text-gray-700 font-medium">Personalized Concierge Service Tailored to Your Unique Automotive Preferences</p>
                  </div>
                  
                  <div class="flex items-start">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <p class="text-base text-gray-700 font-medium">Innovative Approach to Automotive Sales, Financing, and Ownership Experience</p>
                  </div>
                </div>
                
                <!-- 3D Experience Highlight -->
                <div class="bg-gradient-to-r from-primary/5 to-secondary/5 p-5 rounded-md border-l-4 border-secondary mt-6">
                  <p class="text-lg font-semibold text-gray-800">
                    Experience our revolutionary <span class="text-secondary">interactive 3D showroom</span> where cutting-edge technology meets automotive passion, allowing you to explore every detail of our exceptional vehicles from anywhere.
                  </p>
                </div>
              </div>
            </div>
            <div v-else class="prose max-w-none">
              <div class="flex flex-col space-y-6">
                <!-- Stylish headline -->
                <h3 class="text-2xl font-bold text-secondary tracking-wide">EXPERIENCE <span class="text-primary">EXCELLENCE</span></h3>
                
                <!-- Feature points with icons -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="flex items-start">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <p class="text-base text-gray-700 font-medium">Premium Selection of Performance & Luxury Vehicles</p>
                  </div>
                  
                  <div class="flex items-start">
                    <div class="flex-shrink-0 w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                      </svg>
                    </div>
                    <p class="text-base text-gray-700 font-medium">Expert Team of Automotive Enthusiasts</p>
                  </div>
                </div>
                
                <!-- 3D Experience Highlight -->
                <div class="bg-gradient-to-r from-primary/5 to-secondary/5 p-4 rounded-md border-l-4 border-secondary">
                  <p class="text-lg font-semibold text-gray-800">
                    Step into our <span class="text-secondary">immersive 3D showroom</span> and discover our world-class collection like never before.
                  </p>
                </div>
              </div>
            </div>

            <!-- Premium Call to action button -->
            <div class="mt-12">
              <a href="/about" class="inline-flex items-center justify-center px-10 py-4 font-medium tracking-wider uppercase text-sm relative group overflow-hidden">
                <span class="relative z-10 flex items-center text-secondary group-hover:text-white transition-colors duration-500">
                  Learn More About Us
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transition-transform duration-500 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </span>
                <!-- Premium button styling with borders -->
                <span class="absolute top-0 left-0 w-full h-[2px] bg-secondary transform origin-left transition-transform duration-500 scale-x-100"></span>
                <span class="absolute bottom-0 right-0 w-full h-[2px] bg-secondary transform origin-right transition-transform duration-500 scale-x-100"></span>
                <span class="absolute top-0 left-0 h-full w-[2px] bg-secondary transform origin-top transition-transform duration-500 scale-y-100"></span>
                <span class="absolute top-0 right-0 h-full w-[2px] bg-secondary transform origin-bottom transition-transform duration-500 scale-y-100"></span>
                <!-- Hover fill effect -->
                <span class="absolute inset-0 bg-secondary transform scale-x-0 origin-left transition-transform duration-500 group-hover:scale-x-100"></span>
              </a>
            </div>
          </div>

          <div class="relative lg:col-span-2">
            <!-- About Page 3D Game Preview with premium frame -->
            <div class="relative about-3d-preview">
              <div class="absolute -inset-1 bg-gradient-to-r from-secondary/20 via-secondary/10 to-primary/20 rounded-lg blur-sm"></div>
              <div class="relative bg-white p-1 rounded-lg">
                <AboutGamePreview />
              </div>
            </div>
          </div>
        </div>

        <!-- Premium metrics bar with cards -->
        <div class="mt-24" ref="statsContainerRef">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <div v-for="(stat, index) in stats" :key="index"
                class="transform transition-all duration-500 hover:-translate-y-2 text-center group relative border-0 metric-card-wrapper">
              <!-- Glowing border behind the card -->
              <div class="absolute inset-0 metric-card-border"></div>

              <!-- Card content with white background and sharp edges -->
              <div class="relative z-20 bg-white p-8 h-full flex flex-col justify-center items-center w-full border border-gray-100">
                <!-- Premium icon styling -->
                <div class="mb-6">
                  <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-50 text-secondary group-hover:bg-secondary/5 transition-all duration-500">
                    <svg v-if="index === 0" xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <svg v-else-if="index === 1" xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <svg v-else-if="index === 2" xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                </div>

                <!-- Premium stat display -->
                <div class="text-4xl font-bold text-gray-800 mb-2">{{ stat.displayValue }}</div>
                <div class="text-sm text-gray-500 uppercase tracking-wider">{{ stat.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </BusinessInfoProvider>

  <!-- Testimonials Section - Ferrari-inspired Premium Design -->
  <section class="py-28 relative overflow-hidden">
    <!-- Luxury background with subtle pattern and gradient -->
    <div class="absolute inset-0 bg-gradient-to-b from-primary via-primary to-black"></div>
    <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.15\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>



    <div class="container-custom relative z-10 text-white">
      <!-- Luxury Section Header -->
      <div class="text-center mb-16">
        <div class="flex justify-center items-center mb-3">
          <div class="h-px w-12 bg-secondary opacity-50"></div>
          <span class="mx-4 text-sm uppercase tracking-[0.25em] font-medium text-secondary">Client Experiences</span>
          <div class="h-px w-12 bg-secondary opacity-50"></div>
        </div>
        <h2 class="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-6 text-white relative inline-block tracking-tight">
          What Our Clients Say
        </h2>
        <div class="h-px w-24 bg-secondary mx-auto mb-8"></div>
        <p class="text-lg md:text-xl text-white/80 max-w-3xl mx-auto font-light leading-relaxed">
          Don't just take our word for it. Hear from our distinguished clients about their exceptional experience with GT Motor Sports.
        </p>
      </div>

      <!-- Premium testimonials scroll container -->
      <div class="testimonials-scroll-container w-full mt-16">
        <div class="testimonials-scroll">
          <!-- Original Testimonials with premium styling -->
          <div v-for="(testimonial, index) in testimonials" :key="index"
               class="testimonial-card backdrop-blur-sm p-8 mx-4 transition-all duration-500 hover:scale-105 w-[380px] md:w-[480px] relative group">
            <!-- Premium card styling with border gradient -->
            <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 border border-white/10 group-hover:border-secondary/30 transition-all duration-500"></div>

            <!-- Premium quote icon -->
            <div class="absolute -top-4 -left-2 text-secondary/20 text-6xl font-serif">"</div>

            <!-- Content with premium spacing -->
            <div class="relative z-10">
              <div class="flex items-center mb-6">
                <div class="relative">
                  <div class="absolute -inset-1 bg-gradient-to-r from-secondary/30 to-secondary/10 rounded-full blur-sm opacity-70"></div>
                  <img :src="testimonial.image" :alt="testimonial.name" class="w-16 h-16 rounded-full object-cover relative z-10 border-2 border-white/20"/>
                </div>
                <div class="ml-5">
                  <h3 class="font-heading font-bold text-lg tracking-tight">{{ testimonial.name }}</h3>
                  <p class="text-sm text-white/70 uppercase tracking-wider mt-1">{{ testimonial.role }}</p>
                </div>
              </div>

              <!-- Premium star rating -->
              <div class="flex mb-4 space-x-1">
                <template v-for="star in 5" :key="star">
                  <svg xmlns="http://www.w3.org/2000/svg"
                       :class="star <= testimonial.rating ? 'text-secondary' : 'text-white/20'"
                       class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </template>
              </div>

              <!-- Premium testimonial content -->
              <p class="italic text-white/90 line-clamp-4 font-light leading-relaxed">{{ testimonial.content }}</p>

              <!-- Premium accent line -->
              <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-secondary transition-all duration-700 group-hover:w-full"></div>
            </div>
          </div>

          <!-- Duplicate Testimonials for seamless scroll with premium styling -->
          <div v-for="(testimonial, index) in testimonials" :key="`duplicate-${index}`"
               class="testimonial-card backdrop-blur-sm p-8 mx-4 transition-all duration-500 hover:scale-105 w-[380px] md:w-[480px] relative group">
            <!-- Premium card styling with border gradient -->
            <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 border border-white/10 group-hover:border-secondary/30 transition-all duration-500"></div>

            <!-- Premium quote icon -->
            <div class="absolute -top-4 -left-2 text-secondary/20 text-6xl font-serif">"</div>

            <!-- Content with premium spacing -->
            <div class="relative z-10">
              <div class="flex items-center mb-6">
                <div class="relative">
                  <div class="absolute -inset-1 bg-gradient-to-r from-secondary/30 to-secondary/10 rounded-full blur-sm opacity-70"></div>
                  <img :src="testimonial.image" :alt="testimonial.name" class="w-16 h-16 rounded-full object-cover relative z-10 border-2 border-white/20"/>
                </div>
                <div class="ml-5">
                  <h3 class="font-heading font-bold text-lg tracking-tight">{{ testimonial.name }}</h3>
                  <p class="text-sm text-white/70 uppercase tracking-wider mt-1">{{ testimonial.role }}</p>
                </div>
              </div>

              <!-- Premium star rating -->
              <div class="flex mb-4 space-x-1">
                <template v-for="star in 5" :key="star">
                  <svg xmlns="http://www.w3.org/2000/svg"
                       :class="star <= testimonial.rating ? 'text-secondary' : 'text-white/20'"
                       class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </template>
              </div>

              <!-- Premium testimonial content -->
              <p class="italic text-white/90 line-clamp-4 font-light leading-relaxed">{{ testimonial.content }}</p>

              <!-- Premium accent line -->
              <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-secondary transition-all duration-700 group-hover:w-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

    <!-- PREMIUM SERVICES SECTION -->
    <section class="section relative overflow-hidden py-24" ref="servicesRef">
      <!-- Luxury background with subtle pattern -->
      <div class="absolute inset-0 bg-gradient-to-b from-gray-50 to-white"></div>
      <div class="absolute inset-0 opacity-5" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23000000\' fill-opacity=\'0.15\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>

      <div class="container-custom relative z-10">
        <!-- Luxury Section Header -->
        <div class="text-center mb-20 relative">
          <div class="flex justify-center items-center mb-3">
            <div class="h-px w-12 bg-secondary opacity-50"></div>
            <span class="mx-4 text-sm uppercase tracking-[0.25em] font-medium text-secondary">Exceptional Experience</span>
            <div class="h-px w-12 bg-secondary opacity-50"></div>
          </div>
          <h2 class="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-6 text-gray-900 relative inline-block tracking-tight">
            Our Premium Services
          </h2>
          <div class="h-px w-24 bg-secondary mx-auto mb-8"></div>
          <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto font-light leading-relaxed">
            Quality service at competitive prices. We make buying, maintaining, and financing your dream car simple and affordable.
          </p>
        </div>

        <!-- Premium Service Cards Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 lg:gap-12 service-grid">
          <div
            v-for="(service, index) in services"
            :key="index"
            class="service-card group relative"
            @mouseenter="handleServiceHover(index)"
            @mouseleave="handleServiceLeave"
          >
            <!-- Luxury Card Container -->
            <div class="relative rounded-none bg-white overflow-hidden transform transition-all duration-700 group-hover:translate-y-[-8px] h-[600px] border-2 border-gray-200 group-hover:border-secondary/40 group-hover:shadow-[0_20px_50px_rgba(0,0,0,0.08)]">
              <!-- Premium Card Header -->
              <div class="relative h-56 overflow-hidden">
                <img
                  :src="service.image || 'https://via.placeholder.com/400x200?text=GT+Motor+Sports'"
                  :alt="service.title"
                  class="w-full h-full object-cover transition-all duration-1000 ease-in-out group-hover:scale-110 group-hover:filter group-hover:brightness-105"
                  @error="$event.target.src = 'https://via.placeholder.com/400x200?text=GT+Motor+Sports'"
                />
                <!-- Elegant gradient overlay -->
                <div class="absolute inset-0" :style="`background: linear-gradient(to bottom, ${service.color}00, ${service.color}E6); opacity: 0.9;`"></div>

                <!-- Luxury service title with decorative elements -->
                <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <div class="flex items-center mb-2">
                    <div class="w-8 h-px bg-white opacity-70"></div>
                    <div class="ml-3 text-xs uppercase tracking-widest font-medium opacity-90">Premium Service</div>
                  </div>
                  <h3 class="text-2xl md:text-3xl font-heading font-bold tracking-tight">{{ service.title }}</h3>
                </div>
              </div>

              <!-- Refined Card Content -->
              <div class="p-8 flex flex-col h-[calc(100%-14rem)]">
                <p class="text-gray-700 mb-6 leading-relaxed font-light text-sm line-clamp-4">{{ service.description }}</p>

                <!-- Elegant feature list -->
                <ul class="space-y-3 mb-6 flex-grow">
                  <li v-for="(point, i) in service.points" :key="i" class="flex items-start text-gray-700 transition-all duration-300 group-hover:translate-x-1">
                    <div class="flex-shrink-0 w-6 h-6 mr-3 text-white rounded-sm p-1 flex items-center justify-center transform transition-transform duration-500 group-hover:rotate-3" :style="`background-color: ${service.color};`" v-html="getServiceIcon(point.icon)"></div>
                    <span class="text-sm font-medium break-words flex-1 min-w-0 pt-0.5">{{ point.text }}</span>
                  </li>
                </ul>

                <!-- Luxury CTA Button -->
                <div class="mt-auto pb-2">
                  <button
                    class="w-full py-3 font-medium tracking-wide transition-all duration-300 flex items-center justify-center group-hover:shadow-md relative overflow-hidden"
                    :style="`color: ${service.color}; border: 2px solid ${service.color};`"
                    @click="goTo(service.ctaLink)"
                  >
                    <span class="relative z-10 flex items-center transition-colors duration-300 group-hover:text-white">
                      {{ service.ctaText }}
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </span>
                    <!-- Button hover effect -->
                    <div class="absolute inset-0 w-full h-full transition-all duration-300 transform scale-x-0 origin-left group-hover:scale-x-100" :style="`background-color: ${service.color};`"></div>
                  </button>
                </div>
              </div>
            </div>

            <!-- Luxury accent line -->
            <div class="absolute bottom-0 left-0 w-0 h-1 transition-all duration-500 group-hover:w-full" :style="`background-color: ${service.color};`"></div>
          </div>
        </div>
      </div>


    </section>
</template>