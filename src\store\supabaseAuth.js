import { ref, computed } from 'vue'
import { supabase } from '../utils/supabase'

// User state
const user = ref(null)
const session = ref(null)
const isLoading = ref(false)
const error = ref(null)
const userPermissions = ref([])

// Computed properties
const isAuthenticated = computed(() => !!session.value)
const isAdmin = computed(() => user.value?.role_id === 1)

/**
 * Initialize authentication state
 * This should be called when the app starts
 */
const initAuth = async () => {
  // Get the current session
  const { data: { session: currentSession } } = await supabase.auth.getSession()
  
  if (currentSession) {
    session.value = currentSession
    await fetchUserProfile()
    await fetchUserPermissions()
  }
  
  // Listen for auth changes
  supabase.auth.onAuthStateChange(async (_event, newSession) => {
    session.value = newSession
    
    if (newSession) {
      await fetchUserProfile()
      await fetchUserPermissions()
    } else {
      user.value = null
      userPermissions.value = []
    }
  })
}

/**
 * Fetch the user's profile from the custom users table
 */
const fetchUserProfile = async () => {
  if (!session.value) return
  
  const { data, error: fetchError } = await supabase
    .from('users')
    .select('*, user_roles(name, description)')
    .eq('id', session.value.user.id)
    .single()
  
  if (fetchError) {
    console.error('Error fetching user profile:', fetchError)
    return
  }
  
  if (data) {
    user.value = data
  }
}

/**
 * Fetch the user's permissions
 */
const fetchUserPermissions = async () => {
  if (!user.value || !user.value.role_id) return
  
  const { data, error: fetchError } = await supabase
    .from('role_permissions')
    .select('resource, action')
    .eq('role_id', user.value.role_id)
  
  if (fetchError) {
    console.error('Error fetching user permissions:', fetchError)
    return
  }
  
  if (data) {
    userPermissions.value = data
  }
}

/**
 * Check if the user has a specific permission
 * @param {string} resource - The resource to check
 * @param {string} action - The action to check
 * @returns {boolean} - Whether the user has the permission
 */
const hasPermission = (resource, action) => {
  if (!userPermissions.value.length) return false
  
  return userPermissions.value.some(
    permission => permission.resource === resource && permission.action === action
  )
}

/**
 * Sign in with email and password
 * @param {string} email - The user's email
 * @param {string} password - The user's password
 * @returns {Promise<boolean>} - Whether the login was successful
 */
const login = async (email, password) => {
  isLoading.value = true
  error.value = null
  
  try {
    const { data, error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (signInError) {
      error.value = signInError.message
      return false
    }
    
    if (data.session) {
      session.value = data.session
      await fetchUserProfile()
      await fetchUserPermissions()
      return true
    }
    
    return false
  } catch (err) {
    console.error('Login error:', err)
    error.value = 'An unexpected error occurred'
    return false
  } finally {
    isLoading.value = false
  }
}

/**
 * Sign out the current user
 */
const logout = async () => {
  isLoading.value = true
  
  try {
    await supabase.auth.signOut()
    user.value = null
    session.value = null
    userPermissions.value = []
  } catch (err) {
    console.error('Logout error:', err)
    error.value = 'An error occurred during logout'
  } finally {
    isLoading.value = false
  }
}

/**
 * Register a new user
 * @param {string} email - The user's email
 * @param {string} password - The user's password
 * @param {string} fullName - The user's full name
 * @returns {Promise<boolean>} - Whether the registration was successful
 */
const register = async (email, password, fullName) => {
  isLoading.value = true
  error.value = null
  
  try {
    const { data, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName
        }
      }
    })
    
    if (signUpError) {
      error.value = signUpError.message
      return false
    }
    
    return !!data.user
  } catch (err) {
    console.error('Registration error:', err)
    error.value = 'An unexpected error occurred'
    return false
  } finally {
    isLoading.value = false
  }
}

/**
 * Update a user's role
 * @param {string} userId - The user's ID
 * @param {number} roleId - The new role ID
 * @returns {Promise<boolean>} - Whether the update was successful
 */
const updateUserRole = async (userId, roleId) => {
  if (!hasPermission('users', 'update')) {
    error.value = 'You do not have permission to update users'
    return false
  }
  
  isLoading.value = true
  error.value = null
  
  try {
    const { error: updateError } = await supabase
      .from('users')
      .update({ role_id: roleId })
      .eq('id', userId)
    
    if (updateError) {
      error.value = updateError.message
      return false
    }
    
    return true
  } catch (err) {
    console.error('Update user role error:', err)
    error.value = 'An unexpected error occurred'
    return false
  } finally {
    isLoading.value = false
  }
}

export default {
  user,
  session,
  isLoading,
  error,
  userPermissions,
  isAuthenticated,
  isAdmin,
  initAuth,
  login,
  logout,
  register,
  hasPermission,
  updateUserRole
}