<template>
  <div class="styling-tab">
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-gray-900">Site Styling</h2>
      <p class="mt-1 text-sm text-gray-600">
        Customize your website's appearance with colors, typography, and media.
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Left Column: Styling Controls -->
      <div>
        <form @submit.prevent="saveStyling">
          <!-- Colors Section -->
          <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
            <div
              class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
              @click="toggleSection('colors')"
            >
              <div>
                <h3 class="text-lg leading-6 font-semibold text-gray-900">Colors</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                  Customize the color scheme of your website.
                </p>
              </div>
              <div class="text-gray-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 transition-transform duration-200"
                  :class="{'rotate-180': !expandedSections.colors}"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
            <div v-if="expandedSections.colors" class="border-t border-gray-200 px-4 py-5 sm:p-6">
              <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                <ColorPicker
                  v-model="styling.colors.primary"
                  label="Primary Color"
                  id="primary-color"
                  description="Main color for headers, footers, and accents."
                />

                <ColorPicker
                  v-model="styling.colors.secondary"
                  label="Secondary Color"
                  id="secondary-color"
                  description="Accent color for buttons and highlights."
                />

                <ColorPicker
                  v-model="styling.colors.accent"
                  label="Accent Color"
                  id="accent-color"
                  description="Additional accent color for variety."
                />

                <ColorPicker
                  v-model="styling.colors.light"
                  label="Light Color"
                  id="light-color"
                  description="Light background and text color."
                />

                <ColorPicker
                  v-model="styling.colors.dark"
                  label="Dark Color"
                  id="dark-color"
                  description="Dark background and text color."
                />
              </div>
            </div>
          </div>

          <!-- Typography Section -->
          <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
            <div
              class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
              @click="toggleSection('typography')"
            >
              <div>
                <h3 class="text-lg leading-6 font-semibold text-gray-900">Typography</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                  Choose fonts for your website.
                </p>
              </div>
              <div class="text-gray-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 transition-transform duration-200"
                  :class="{'rotate-180': !expandedSections.typography}"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
            <div v-if="expandedSections.typography" class="border-t border-gray-200 px-4 py-5 sm:p-6">
              <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                <div>
                  <label for="heading-font" class="block text-sm font-medium text-gray-800">Heading Font</label>
                  <select
                    id="heading-font"
                    v-model="styling.typography.headingFont"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-400 shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                  >
                    <option v-for="font in headingFonts" :key="font" :value="font">{{ font }}</option>
                  </select>
                  <p class="mt-1 text-sm text-gray-500">Font used for headings and titles.</p>
                </div>

                <div>
                  <label for="body-font" class="block text-sm font-medium text-gray-800">Body Font</label>
                  <select
                    id="body-font"
                    v-model="styling.typography.bodyFont"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-400 shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                  >
                    <option v-for="font in bodyFonts" :key="font" :value="font">{{ font }}</option>
                  </select>
                  <p class="mt-1 text-sm text-gray-500">Font used for body text and paragraphs.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Layout Section -->
          <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
            <div
              class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
              @click="toggleSection('layout')"
            >
              <div>
                <h3 class="text-lg leading-6 font-semibold text-gray-900">Layout</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                  Adjust layout elements like card styles and buttons.
                </p>
              </div>
              <div class="text-gray-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 transition-transform duration-200"
                  :class="{'rotate-180': !expandedSections.layout}"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
            <div v-if="expandedSections.layout" class="border-t border-gray-200 px-4 py-5 sm:p-6">
              <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                <div>
                  <label for="card-border-radius" class="block text-sm font-medium text-gray-800">Card Border Radius</label>
                  <select
                    id="card-border-radius"
                    v-model="styling.layout.cardBorderRadius"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-400 shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                  >
                    <option value="0">None (0)</option>
                    <option value="0.25rem">Small (0.25rem)</option>
                    <option value="0.5rem">Medium (0.5rem)</option>
                    <option value="0.75rem">Large (0.75rem)</option>
                    <option value="1rem">Extra Large (1rem)</option>
                  </select>
                  <p class="mt-1 text-sm text-gray-500">Roundness of card corners.</p>
                </div>

                <div>
                  <label for="card-shadow" class="block text-sm font-medium text-gray-800">Card Shadow</label>
                  <select
                    id="card-shadow"
                    v-model="styling.layout.cardShadow"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-400 shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                  >
                    <option value="none">None</option>
                    <option value="sm">Small</option>
                    <option value="md">Medium</option>
                    <option value="lg">Large</option>
                  </select>
                  <p class="mt-1 text-sm text-gray-500">Shadow depth for cards.</p>
                </div>

                <div>
                  <label for="button-border-radius" class="block text-sm font-medium text-gray-800">Button Border Radius</label>
                  <select
                    id="button-border-radius"
                    v-model="styling.layout.buttonBorderRadius"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-400 shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                  >
                    <option value="0">None (0)</option>
                    <option value="0.125rem">Extra Small (0.125rem)</option>
                    <option value="0.25rem">Small (0.25rem)</option>
                    <option value="0.375rem">Medium (0.375rem)</option>
                    <option value="0.5rem">Large (0.5rem)</option>
                    <option value="9999px">Full (Pill)</option>
                  </select>
                  <p class="mt-1 text-sm text-gray-500">Roundness of button corners.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Media Section -->
          <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
            <div
              class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
              @click="toggleSection('media')"
            >
              <div>
                <h3 class="text-lg leading-6 font-semibold text-gray-900">Media</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                  Upload custom images and videos for your website.
                </p>
              </div>
              <div class="text-gray-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 transition-transform duration-200"
                  :class="{'rotate-180': !expandedSections.media}"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
            <div v-if="expandedSections.media" class="border-t border-gray-200 px-4 py-5 sm:p-6">
              <div class="grid grid-cols-1 gap-y-6">
                <MediaUploader
                  v-model="styling.media.logo"
                  label="Website Logo"
                  id="site-logo"
                  section="logo"
                  accept="image/*"
                  description="Your website logo that will appear in the header. Recommended size: 200x80px, transparent background."
                />

                <MediaUploader
                  v-model="styling.media.heroImage"
                  label="Hero Background Image"
                  id="hero-image"
                  section="hero"
                  accept="image/*"
                  description="Background image for the home page hero section. Recommended size: 1920x1080px."
                />

                <MediaUploader
                  v-model="styling.media.heroVideo"
                  label="Hero Background Video"
                  id="hero-video"
                  section="hero-video"
                  accept="video/*"
                  description="Background video for the home page hero section. Recommended format: MP4, max 5MB."
                />

                <MediaUploader
                  v-model="styling.media.aboutImage"
                  label="About Page Image"
                  id="about-image"
                  section="about"
                  accept="image/*"
                  description="Featured image for the About page. Recommended size: 1200x800px."
                />

                <MediaUploader
                  v-model="styling.media.footerImage"
                  label="Footer Background Image"
                  id="footer-image"
                  section="footer"
                  accept="image/*"
                  description="Background image for the site footer. Recommended size: 1920x400px."
                />
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-between items-center">
            <button
              type="button"
              @click="resetStyling"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Reset to Defaults
            </button>

            <div class="flex items-center space-x-4">
              <!-- Status messages - only show one at a time -->
              <span v-if="saveSuccess && !isSaving" class="text-sm text-green-600">
                Settings saved successfully!
              </span>
              <span v-else-if="saveError && !isSaving" class="text-sm text-red-600">
                {{ errorMessage }}
              </span>

              <button
                type="submit"
                :disabled="isSaving"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <svg v-if="isSaving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ isSaving ? 'Saving...' : 'Save Changes' }}
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Right Column: Preview -->
      <div>
        <StylePreview />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import ColorPicker from '../../components/admin/ColorPicker.vue';
import MediaUploader from '../../components/admin/MediaUploader.vue';
import StylePreview from '../../components/admin/StylePreview.vue';
import stylingStore from '../../store/styling';

// Create local states to override the store's states
const localIsSaving = ref(false);
const localSaveSuccess = ref(false);

// Track expanded sections
const expandedSections = reactive({
  colors: true,
  typography: false,
  layout: false,
  media: false
});

// Toggle section expansion
const toggleSection = (section) => {
  expandedSections[section] = !expandedSections[section];
};

// Font options
const headingFonts = [
  'Montserrat',
  'Roboto',
  'Open Sans',
  'Playfair Display',
  'Oswald',
  'Raleway',
  'Lato',
  'Merriweather',
  'Poppins'
];

const bodyFonts = [
  'Inter',
  'Roboto',
  'Open Sans',
  'Lato',
  'Nunito',
  'Source Sans Pro',
  'Poppins',
  'Noto Sans',
  'Work Sans'
];

// Get styling from store
const styling = computed(() => stylingStore.styling);
const isLoading = computed(() => stylingStore.isLoading);
// Use local states instead of the store's states
const isSaving = computed(() => localIsSaving.value);
const saveSuccess = computed(() => localSaveSuccess.value);
const saveError = computed(() => stylingStore.saveError);
const errorMessage = computed(() => stylingStore.errorMessage);

// Initialize store on component mount
onMounted(async () => {
  console.log('StylingTab mounted');

  // Ensure our local states start as false
  localIsSaving.value = false;
  localSaveSuccess.value = false;

  // Force reset all store state flags to ensure a clean state
  stylingStore.isSaving.value = false;
  stylingStore.saveSuccess.value = false;
  stylingStore.saveError.value = false;
  stylingStore.errorMessage.value = '';

  // Add a small delay to ensure state is reset before proceeding
  await new Promise(resolve => setTimeout(resolve, 50));

  try {
    // Initialize the store
    await stylingStore.initStore();

    // Force all states to false after initialization
    localIsSaving.value = false;
    localSaveSuccess.value = false;
    stylingStore.isSaving.value = false;
    stylingStore.saveSuccess.value = false;

    console.log('Styling store initialized successfully');

    // Add a final check after a short delay to catch any race conditions
    setTimeout(() => {
      if (stylingStore.isSaving.value || stylingStore.saveSuccess.value) {
        console.warn('StylingTab: Store states are not reset after initialization, forcing reset');
        stylingStore.isSaving.value = false;
        stylingStore.saveSuccess.value = false;
      }
      localIsSaving.value = false;
      localSaveSuccess.value = false;
    }, 100);
  } catch (error) {
    console.error('Error initializing styling store:', error);
    stylingStore.errorMessage.value = 'Failed to load styling settings. Please refresh the page.';
    stylingStore.saveError.value = true;

    // Ensure all states are false even if initialization fails
    localIsSaving.value = false;
    localSaveSuccess.value = false;
    stylingStore.isSaving.value = false;
    stylingStore.saveSuccess.value = false;
  }
});

// Clean up when component is unmounted
onUnmounted(() => {
  console.log('StylingTab unmounted, cleaning up');
  // Ensure we reset all states when navigating away
  localIsSaving.value = false;
  localSaveSuccess.value = false;
  stylingStore.isSaving.value = false;
  stylingStore.saveSuccess.value = false;
});

// Save styling with additional error handling
const saveStyling = async () => {
  console.log('StylingTab: Starting save operation');

  // If already saving, prevent multiple save attempts
  if (localIsSaving.value) {
    console.warn('Already saving, ignoring duplicate save request');
    return;
  }

  try {
    // Set our local saving state to true
    localIsSaving.value = true;

    // Force the store's saving state to false to prevent conflicts
    stylingStore.isSaving.value = false;

    // Create a safety timeout for our local state
    const localSafetyTimeout = setTimeout(() => {
      if (localIsSaving.value) {
        console.warn('Local safety timeout triggered: resetting localIsSaving state');
        localIsSaving.value = false;
        stylingStore.saveError.value = true;
        stylingStore.errorMessage.value = 'Save operation timed out. Please try again.';
      }
    }, 8000);

    try {
      const success = await stylingStore.saveStyling();

      if (success) {
        // Show success message briefly
        localSaveSuccess.value = true;

        // Clear success message after 3 seconds
        setTimeout(() => {
          localSaveSuccess.value = false;
        }, 3000);
      }
    } finally {
      // Clear the local safety timeout
      clearTimeout(localSafetyTimeout);

      // Always set our local saving state to false
      localIsSaving.value = false;

      // Force the store's states to false as well
      stylingStore.isSaving.value = false;
      stylingStore.saveSuccess.value = false;
    }
  } catch (error) {
    console.error('Unexpected error in saveStyling:', error);
    // Ensure all states are reset even if an unexpected error occurs
    localIsSaving.value = false;
    localSaveSuccess.value = false;
    stylingStore.isSaving.value = false;
    stylingStore.saveSuccess.value = false;
    stylingStore.saveError.value = true;
    stylingStore.errorMessage.value = 'An unexpected error occurred. Please try again.';
  }
};

// Reset styling to defaults
const resetStyling = () => {
  if (confirm('Are you sure you want to reset all styling to default values? This cannot be undone.')) {
    stylingStore.resetStyling();
  }
};
</script>
