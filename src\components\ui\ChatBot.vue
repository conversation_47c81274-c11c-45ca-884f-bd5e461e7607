<script setup>
import { ref, onMounted, computed } from 'vue';
import { GoogleGenAI } from "@google/genai";
import vehicleStore from '../../store/vehicles';

// Get API key from environment variables
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const ai = new GoogleGenAI({ apiKey: API_KEY });
// Get vehicles from the store
const vehicles = computed(() => vehicleStore.vehicles.value);
const isLoadingVehicles = computed(() => vehicleStore.isLoading.value);

// Initialize vehicle store when component is mounted
onMounted(() => {
  console.log('[ChatBot] Component mounted, initializing vehicle store');
  // The store now always uses Supabase, so we don't need to set useSupabase flag
  console.log('[ChatBot] Using Supabase for vehicle data');
  
  // Initialize vehicle store to ensure we have the latest data
  vehicleStore.initStore().then(() => {
    console.log('[ChatBot] Vehicle store initialized');
    console.log('[ChatBot] Vehicles loaded:', vehicles.value ? vehicles.value.length : 0);
    
    if (vehicles.value && vehicles.value.length > 0) {
      // Log the years of vehicles to debug
      const years = vehicles.value.map(v => v.year).filter(Boolean).sort((a, b) => b - a);
      console.log('[ChatBot] Vehicle years available:', years);
      
      if (years.length > 0) {
        console.log('[ChatBot] Newest vehicle year:', years[0]);
        console.log('[ChatBot] Sample newest vehicle:',
          vehicles.value.find(v => v.year === years[0])?.title);
      }
    }
  });
  
  // Set up a periodic refresh of vehicle data (every 5 minutes)
  const refreshInterval = setInterval(() => {
    vehicleStore.initStore().then(() => {
      console.log('[ChatBot] Refreshed vehicle inventory data');
    });
  }, 5 * 60 * 1000);
  
  // Clean up interval when component is unmounted
  return () => clearInterval(refreshInterval);
});

const isOpen = ref(false);
const messages = ref([
  { role: 'assistant', content: 'Welcome to GT Motor Sports! How can I help you today? Ask me about our vehicle inventory, financing options, or detailing services.' }
]);
const userInput = ref('');

// System instruction for the chatbot
const getSystemInstruction = () => {
  let instruction = "You are a helpful assistant for GT Motor Sports, a car dealership. Your name is GT Assistant. You provide concise, friendly, and conversational responses about our dealership services including vehicle inventory, financing options, and detailing services. Keep responses under 150 words and maintain a professional but approachable tone. Your responses should sound natural, as if coming from a helpful salesperson, not a database.";
  
  instruction += "\n\nIMPORTANT RESPONSE GUIDELINES:";
  instruction += "\n1. Be conversational and natural - avoid sounding like you're reading from a database";
  instruction += "\n2. Omit technical details like stock numbers and VINs unless specifically requested";
  instruction += "\n3. Focus on the most relevant information that would interest a customer";
  instruction += "\n4. When describing vehicles, highlight key selling points like color, features, and condition";
  instruction += "\n5. Use natural language like 'We have a red Mazda CX-5' rather than listing specifications";
  instruction += "\n6. FORMAT YOUR RESPONSES FOR READABILITY:";
  instruction += "\n   - Use short paragraphs with 1-2 sentences each";
  instruction += "\n   - When listing multiple vehicles, use a separate paragraph for each vehicle";
  instruction += "\n   - Start each vehicle listing with a bullet point (•) or dash (-)";
  instruction += "\n   - Add blank lines between different sections of your response";
  instruction += "\n   - Highlight important information like prices with spacing";
  
  // Add vehicle inventory information if available
  if (vehicles.value && vehicles.value.length > 0) {
    instruction += "\n\nHere is our current vehicle inventory (for your reference only - don't list all these details in your responses):";
    
    // Add detailed information for up to 10 vehicles
    const vehiclesToShow = vehicles.value.slice(0, 10);
    vehiclesToShow.forEach(vehicle => {
      // Basic info line
      instruction += `\n- ${vehicle.title}: $${vehicle.price.toLocaleString()}`;
      
      // Add detailed specs in a structured format but note they're for reference
      instruction += `\n  • Mileage: ${vehicle.mileage.toLocaleString()} miles`;
      instruction += `\n  • Exterior: ${vehicle.exteriorColor || 'N/A'}`;
      instruction += `\n  • Interior: ${vehicle.interiorColor || 'N/A'}`;
      
      if (vehicle.engine || vehicle.engineSize) {
        instruction += `\n  • Engine: ${vehicle.engine || ''} ${vehicle.engineSize || ''}`.trim();
      }
      
      if (vehicle.transmission) {
        instruction += `\n  • Transmission: ${vehicle.transmission}`;
      }
      
      if (vehicle.drivetrain) {
        instruction += `\n  • Drivetrain: ${vehicle.drivetrain}`;
      }
      
      // Store stock number and VIN for reference but note they shouldn't be included in responses
      instruction += `\n  • Internal Reference - Stock #: ${vehicle.stockNumber || 'N/A'}`;
      if (vehicle.vin) {
        instruction += `\n  • Internal Reference - VIN: ${vehicle.vin}`;
      }
      
      // Add a brief description if available
      if (vehicle.description && vehicle.description.length > 0) {
        // Truncate description if too long
        const shortDesc = vehicle.description.length > 100
          ? vehicle.description.substring(0, 100) + '...'
          : vehicle.description;
        instruction += `\n  • Description: ${shortDesc}`;
      }
    });
    
    if (vehicles.value.length > 10) {
      instruction += `\n\nPlus ${vehicles.value.length - 10} more vehicles in inventory. You can tell customers we have ${vehicles.value.length} total vehicles available.`;
    }
    
    // Get the newest model year from inventory
    const years = vehicles.value
      .map(v => v.year)
      .filter(year => year && !isNaN(year))
      .sort((a, b) => b - a);
    
    const newestYear = years.length > 0 ? years[0] : null;
    const newestVehicles = newestYear ?
      vehicles.value.filter(v => v.year === newestYear) : [];
    
    // Add guidance for how to respond to inventory queries
    instruction += "\n\nWhen customers ask about our inventory:";
    instruction += "\n1. If they ask about specific years, makes, or models that match our inventory, mention the matching vehicles with key details like color, price, and mileage.";
    instruction += "\n2. If they ask about years, makes, or models we don't have, clearly state we don't currently have those specific vehicles, but mention what we do have that's similar or closest to their request.";
    instruction += "\n3. For general inventory questions, highlight our diverse selection with examples from different price ranges.";
    instruction += "\n4. Always be specific about what we have in stock based on the inventory list above.";
    instruction += "\n5. IMPORTANT: Respond in a natural, conversational way with good formatting. For example:";
    instruction += "\n   - Good: 'Yes, we have a few white trucks in our inventory:\n\n• 2021 Ford F-150 Lariat 4WD SuperCrew - $47,988\n\n• Several 2023-2024 Ford Transit Cargo Vans - $53,988 to $67,988\n\n• 2023 Ram Promaster Cargo 3500 High Roof - $58,988\n\nWould you like more details about any of these?'";
    instruction += "\n   - Bad: 'We have a few white trucks and vans in stock: - 2021 Ford F-150 Lariat 4WD SuperCrew 5.5' Box 502A ($47988) - Several white 2023 and 2024 Ford Transit Cargo Vans, ranging from $53988 to $67988 - 2023 Ram Promaster Cargo 3500 High Roof Ext 159\" WB ($58988)'";
    
    if (newestYear) {
      instruction += `\n\nIMPORTANT: The newest vehicles in our inventory are from ${newestYear}. When asked about new vehicles or recent model years, mention these ${newestYear} models:`;
      
      // List the newest vehicles
      newestVehicles.forEach(vehicle => {
        instruction += `\n- ${vehicle.title}: $${vehicle.price.toLocaleString()}`;
      });
    }
  } else {
    instruction += "\n\nOur inventory data is currently being loaded. Please tell customers we have a wide selection of quality vehicles and invite them to visit our dealership or check our website for the most up-to-date inventory.";
  }
  
  // Add information about financing options
  instruction += "\n\nFinancing Options:";
  instruction += "\n- Standard Financing: Traditional auto loans with competitive rates";
  instruction += "\n- Special Programs: Options for first-time buyers and those with limited credit history";
  instruction += "\n- Lease Options: Flexible terms with lower monthly payments";
  instruction += "\n- Credit Rebuilding: Programs designed to help improve credit scores";
  instruction += "\n- $0 Down Payment: Available for qualified buyers";
  instruction += "\n- Everyone Approved: We work with all credit situations";
  
  // Add information about detailing services
  instruction += "\n\nDetailing Services:";
  instruction += "\n- Interior Detailing: Deep cleaning of all interior surfaces";
  instruction += "\n- Exterior Detailing: Paint correction, polishing, and protective coatings";
  instruction += "\n- Complete Packages: Comprehensive interior and exterior detailing";
  
  // Add examples of good conversational responses
  instruction += "\n\nEXAMPLES OF GOOD CONVERSATIONAL RESPONSES:";
  
  instruction += "\n\nQuestion: 'Do you have any red cars in stock?'";
  instruction += "\nGood response: 'Yes! We have a beautiful red 2024 Mazda CX-5 with black interior.\n\nIt has only 34,354 miles and is priced at $35,988.\n\nThis vehicle also qualifies for our $0 down financing program!'";
  
  instruction += "\n\nQuestion: 'What financing options do you have?'";
  instruction += "\nGood response: 'We offer several financing options to fit your needs!\n\n• Standard auto loans with competitive rates\n• Special programs for first-time buyers\n• Flexible lease terms with lower monthly payments\n• Credit rebuilding programs\n• $0 down payment options for qualified buyers\n\nWhat type of financing are you most interested in?'";
  
  instruction += "\n\nQuestion: 'Which white trucks do you have?'";
  instruction += "\nGood response: 'We have a few white trucks in our inventory:\n\n• 2021 Ford F-150 Lariat 4WD SuperCrew - $47,988\n\n• Several 2023-2024 Ford Transit Cargo Vans - $53,988 to $67,988\n\n• 2023 Ram Promaster Cargo 3500 High Roof - $58,988\n\nWould you like more details about any of these?'";
  
  return instruction;
};
const isLoading = ref(false);

const toggleChat = () => {
  isOpen.value = !isOpen.value;
  
  // Initialize vehicle store if not already initialized
  if (isOpen.value && !vehicleStore.isInitialized) {
    vehicleStore.initStore();
  }
};

const sendMessage = async () => {
  if (!userInput.value.trim()) return;
  
  console.log('[ChatBot] Sending message:', userInput.value);
  
  // The store now always uses Supabase
  console.log('[ChatBot] Using Supabase for vehicle data');
  
  // Ensure we have the latest vehicle data before responding
  if (!isLoadingVehicles.value) {
    await vehicleStore.initStore();
  }
  
  // Log vehicle data for debugging
  if (vehicles.value && vehicles.value.length > 0) {
    const years = vehicles.value.map(v => v.year).filter(Boolean).sort((a, b) => b - a);
    console.log('[ChatBot] Available vehicle years:', years);
    console.log('[ChatBot] Total vehicles:', vehicles.value.length);
  }
  
  // Add user message to chat
  messages.value.push({ role: 'user', content: userInput.value });
  
  // Clear input and show loading
  const userQuestion = userInput.value;
  userInput.value = '';
  isLoading.value = true;
  
  try {
    // Call Gemini API
    // Get current system instruction with vehicle data
    const currentSystemInstruction = getSystemInstruction();
    console.log('[ChatBot] System instruction length:', currentSystemInstruction.length);
    
    // Format the conversation history for the API
    let contents = [];
    
    // If this is not the first message, include conversation history
    if (messages.value.length > 1) {
      console.log('[ChatBot] Including conversation history with', messages.value.length - 1, 'previous messages');
      
      // Add previous messages to provide context
      // Skip the first message (greeting) to save tokens
      for (let i = 1; i < messages.value.length; i++) {
        const msg = messages.value[i];
        contents.push({
          role: msg.role === 'user' ? 'user' : 'model',
          parts: [{ text: msg.content }]
        });
      }
    } else {
      // For the first user message, just include that message
      contents.push({
        role: 'user',
        parts: [{ text: userQuestion }]
      });
    }
    
    console.log('[ChatBot] Sending request with', contents.length, 'messages');
    
    // Call the Gemini API with the full conversation history
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: contents,
      config: {
        systemInstruction: currentSystemInstruction,
      },
    });
    
    const responseText = response.text;
    console.log('[ChatBot] Got response:', responseText.substring(0, 50) + '...');
    
    // Add AI response to chat
    messages.value.push({ role: 'assistant', content: responseText });
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    
    // Log detailed error information for debugging
    if (error.response) {
      console.error('API Error Response:', error.response);
    }
    
    // Check if it's a context length error
    const errorMessage = error.message || '';
    if (errorMessage.includes('context length') || errorMessage.includes('token limit')) {
      console.log('[ChatBot] Context length error detected, resetting conversation');
      // Keep only the initial greeting and the latest user message
      const userMessage = messages.value[messages.value.length - 1];
      messages.value = [
        messages.value[0], // Keep the greeting
        userMessage // Keep the latest user message
      ];
      
      // Try again with just the current question
      try {
        console.log('[ChatBot] Retrying with simplified conversation');
        const response = await ai.models.generateContent({
          model: "gemini-2.0-flash",
          contents: [{
            role: 'user',
            parts: [{ text: userQuestion }]
          }],
          config: {
            systemInstruction: getSystemInstruction(),
          },
        });
        messages.value.push({ role: 'assistant', content: response.text });
        return; // Exit the function if successful
      } catch (retryError) {
        console.error('Error on retry:', retryError);
      }
    }
    
    // If we get here, show a friendly error message
    messages.value.push({
      role: 'assistant',
      content: 'Sorry, I encountered an error. Please try again or contact our team directly.'
    });
  } finally {
    isLoading.value = false;
  }
};

// Auto-scroll to bottom of chat when new messages arrive
const chatContainer = ref(null);
const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
};

// Watch for changes to messages and scroll to bottom
const watchMessages = () => {
  if (messages.value.length > 0) {
    setTimeout(scrollToBottom, 100);
  }
};

import { watch } from 'vue';

// Set up proper watcher for messages
watch(messages, () => {
  setTimeout(scrollToBottom, 100);
}, { deep: true });

// Watch for chat open state to scroll when opened
watch(isOpen, (newValue) => {
  if (newValue) {
    setTimeout(scrollToBottom, 100);
  }
});

// Initial setup
onMounted(() => {
  // Initial scroll
  scrollToBottom();
});
</script>

<template>
  <!-- Chat button -->
  <div class="fixed bottom-6 right-6 z-50">
    <button
      id="chat-button"
      @click="toggleChat"
      class="bg-secondary hover:bg-dark text-white rounded-full p-4 shadow-lg transition-all duration-300 flex items-center justify-center w-14 h-14"
      aria-label="Chat with GT Assistant"
    >
      <!-- Chat icon when closed -->
      <div v-if="!isOpen" class="relative">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
        </svg>
        <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
          1
        </span>
      </div>
      <!-- Close icon when open -->
      <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </div>

  <!-- Chat window -->
  <div 
    v-if="isOpen" 
    class="fixed bottom-24 right-6 w-80 sm:w-96 bg-white rounded-lg shadow-xl z-50 flex flex-col transition-all duration-300 max-h-[70vh] border border-gray-200"
  >
    <!-- Chat header -->
    <div class="bg-primary text-white p-4 rounded-t-lg flex justify-between items-center">
      <div class="flex items-center">
        <img src="/GTWHEEL.png" alt="GT Motor Sports" class="h-6 w-6 mr-2" />
        <h3 class="font-bold">GT Motor Sports Assistant</h3>
      </div>
      <button @click="toggleChat" class="text-white hover:text-gray-200">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
    
    <!-- Chat messages -->
    <div
      ref="chatContainer"
      class="flex-1 overflow-y-auto p-4 space-y-4"
      style="max-height: 350px; scroll-behavior: smooth;"
    >
      <div
        v-for="(message, index) in messages"
        :key="index"
        :class="[
          'p-3 rounded-lg max-w-[85%]',
          message.role === 'user'
            ? 'bg-blue-100 ml-auto'
            : 'bg-gray-100'
        ]"
      >
        <div class="whitespace-pre-line">{{ message.content }}</div>
      </div>
      
      <!-- Loading indicator -->
      <div v-if="isLoading" class="bg-gray-100 p-3 rounded-lg max-w-[85%] flex items-center space-x-2">
        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
      </div>
    </div>
    
    <!-- Chat input -->
    <div class="border-t border-gray-200 p-4">
      <form @submit.prevent="sendMessage" class="flex space-x-2">
        <input 
          v-model="userInput" 
          type="text" 
          placeholder="Type your message..." 
          class="flex-1 border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
        />
        <button
          type="submit"
          class="bg-primary hover:bg-dark text-white rounded-full p-2 transition-colors duration-300"
          :disabled="isLoading || !userInput.trim()"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        </button>
      </form>
    </div>
  </div>
</template>

<style scoped>
/* Add any additional styling here */
#chat-button {
  box-shadow: 0 0 15px rgba(225, 29, 72, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(225, 29, 72, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(225, 29, 72, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(225, 29, 72, 0);
  }
}
</style>