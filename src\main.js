import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import './style.css';
import './assets/main.css';

// 1. Import vue3-particles AND the tsparticles engine loader
import Particles from "vue3-particles";
import { loadFull } from "tsparticles"; // Or loadSlim, loadBasic, or specific presets/shapes

// Create the Vue application
const app = createApp(App);

app.use(router);

// 2. Register Particles plugin WITH the init function
app.use(Particles, {
  init: async engine => {
    // --- Load the tsparticles package ---
    // "loadFull" loads all features, good for starting, optimize later if needed.
    await loadFull(engine);

    // --- Or load specific features ---
    // await loadSlim(engine); // Loads basic features
    // await loadBasic(engine); // Loads the absolute minimum
    // You can also load specific shapes, modes, presets here if not using loadFull
    // e.g., await loadStarsPreset(engine);
  },
});

// Ensure router is ready before mounting
router.isReady().then(() => {
  app.mount('#app');
});