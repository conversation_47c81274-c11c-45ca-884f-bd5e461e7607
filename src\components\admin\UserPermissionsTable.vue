<script setup>
import { ref, onMounted, computed } from 'vue';
import { supabase } from '../../utils/supabase';
import authStore from '../../store/supabaseAuth';

const users = ref([]);
const roles = ref([]);
const isLoading = ref(true);
const error = ref(null);
const selectedUser = ref(null);
const showRoleModal = ref(false);
const selectedRole = ref(null);

// Computed property to check if current user is admin
const isAdmin = computed(() => authStore.isAdmin.value);

// Fetch all users and their roles
const fetchUsers = async () => {
  isLoading.value = true;
  error.value = null;
  
  try {
    const { data, error: fetchError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        full_name,
        is_active,
        role_id,
        created_at,
        updated_at,
        user_roles (
          id,
          name,
          description
        )
      `)
      .order('created_at', { ascending: false });
    
    if (fetchError) {
      throw fetchError;
    }
    
    users.value = data;
  } catch (err) {
    console.error('Error fetching users:', err);
    error.value = 'Failed to load users. Please try again.';
  } finally {
    isLoading.value = false;
  }
};

// Fetch all available roles
const fetchRoles = async () => {
  try {
    const { data, error: fetchError } = await supabase
      .from('user_roles')
      .select('*')
      .order('id');
    
    if (fetchError) {
      throw fetchError;
    }
    
    roles.value = data;
  } catch (err) {
    console.error('Error fetching roles:', err);
    error.value = 'Failed to load roles. Please try again.';
  }
};

// Open role change modal
const openRoleModal = (user) => {
  if (!isAdmin.value) return;
  
  selectedUser.value = user;
  selectedRole.value = user.role_id;
  showRoleModal.value = true;
};

// Update user role
const updateUserRole = async () => {
  if (!selectedUser.value || !selectedRole.value) return;
  
  isLoading.value = true;
  error.value = null;
  
  try {
    const success = await authStore.updateUserRole(
      selectedUser.value.id,
      selectedRole.value
    );
    
    if (success) {
      // Update local data
      const userIndex = users.value.findIndex(u => u.id === selectedUser.value.id);
      if (userIndex !== -1) {
        const selectedRoleData = roles.value.find(r => r.id === selectedRole.value);
        users.value[userIndex].role_id = selectedRole.value;
        users.value[userIndex].user_roles = selectedRoleData;
      }
      
      showRoleModal.value = false;
      selectedUser.value = null;
    } else {
      error.value = authStore.error.value || 'Failed to update user role';
    }
  } catch (err) {
    console.error('Error updating user role:', err);
    error.value = 'An unexpected error occurred';
  } finally {
    isLoading.value = false;
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

// Load data on component mount
onMounted(async () => {
  await Promise.all([fetchUsers(), fetchRoles()]);
});
</script>

<template>
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-xl font-semibold mb-4">User Permissions</h2>
    
    <div v-if="error" class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-700">{{ error }}</p>
        </div>
      </div>
    </div>
    
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <svg class="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
    
    <div v-else-if="users.length === 0" class="text-center py-8 text-gray-500">
      No users found.
    </div>
    
    <div v-else class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              User
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Role
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="user in users" :key="user.id">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ user.full_name || 'N/A' }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ user.email }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                {{ user.user_roles?.name || 'No Role' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                :class="user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                {{ user.is_active ? 'Active' : 'Inactive' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatDate(user.created_at) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <button 
                v-if="isAdmin" 
                @click="openRoleModal(user)" 
                class="text-primary hover:text-primary-dark"
              >
                Change Role
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- Role Change Modal -->
    <div v-if="showRoleModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Change User Role</h3>
        
        <div v-if="selectedUser" class="mb-4">
          <p class="text-sm text-gray-600">
            Changing role for: <span class="font-medium">{{ selectedUser.email }}</span>
          </p>
        </div>
        
        <div class="mb-4">
          <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Select Role</label>
          <select 
            id="role" 
            v-model="selectedRole" 
            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
          >
            <option v-for="role in roles" :key="role.id" :value="role.id">
              {{ role.name }} - {{ role.description }}
            </option>
          </select>
        </div>
        
        <div class="flex justify-end space-x-3">
          <button 
            @click="showRoleModal = false" 
            class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Cancel
          </button>
          <button 
            @click="updateUserRole" 
            :disabled="isLoading"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Save Changes
          </button>
        </div>
      </div>
    </div>
  </div>
</template>