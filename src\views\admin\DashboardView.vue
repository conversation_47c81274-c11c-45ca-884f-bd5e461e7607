<script setup>
import { ref, computed, onMounted, watch, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { debounce } from 'lodash-es';
import AdminNavbar from '../../components/admin/AdminNavbar.vue';
import VehiclePreviewModal from '../../components/admin/VehiclePreviewModal.vue';
import BusinessInfoTab from './BusinessInfoTab.vue'; // Ensure this component exists at this path
import StylingTab from './StylingTab.vue'; // Import the new Styling tab component
import CarPagesExport from '../../components/admin/CarPagesExport.vue'; // Import the CarPages export component
import authStore from '../../store/supabaseAuth';
import vehicleStore from '../../store/vehicles';
import stylingStore from '../../store/styling'; // Import the styling store
import UserPermissionsTable from '../../components/admin/UserPermissionsTable.vue';
// Import settings services - MAKE SURE THIS PATH IS CORRECT
import {
  getInterestRates, updateInterestRates,
  getDetailingPrices, updateDetailingPrices,
  getBusinessInfo, updateBusinessInfo,
  getFinancingFaqs, updateFinancingFaqs,
  getFinancingOptions, updateFinancingOptions,
  getFinancingProcess, updateFinancingProcess
} from '../../database/settingsService';
import { getAllPreQualifications } from '../../database/financingService';
import * as supabaseService from '../../database/supabaseService';

const router = useRouter();

// Note: Direct image URLs are now used instead of transformation

// --- Global State ---
const isLoading = ref(true); // Combined initial loading state for the entire dashboard
const activeTab = ref('inventory'); // 'inventory', 'financing', 'detailing', 'business', 'users', 'styling'
const showCarPagesExport = ref(true); // Toggle for CarPages export component - default to visible

// --- Inventory State ---
const isTableLoading = ref(false); // Specific to inventory table operations (sorting, filtering)
const searchQuery = ref('');
const debouncedSearchQuery = ref('');
const sortKey = ref(''); // Field to sort by
const sortDir = ref('asc'); // Sort direction
const currentPage = ref(1); // Pagination
const itemsPerPage = ref(20); // Pagination
const showDeleteModal = ref(false); // Delete confirmation modal
const vehicleToDelete = ref(null); // Vehicle selected for deletion
const isDeleting = ref(false); // Loading state for delete operation
const selectedVehicle = ref(null); // Vehicle selected for preview
const showPreviewModal = ref(false); // Preview modal visibility
const openingAiStudio = ref(false); // State for navigating to AI Studio

// --- Financing State ---
const isLoadingFinancing = ref(true); // Separate loading state for fetching financing settings
const isSavingFinancing = ref(false); // State for when saving financing settings
const saveSuccessFinancing = ref(false); // Success message state
const saveErrorFinancing = ref(false); // Error message state
const errorMessageFinancing = ref(''); // Error message text
const creditScoreTiers = [ // Static definition of tiers
  { id: 'excellent', label: 'Excellent (720+)' },
  { id: 'good', label: 'Good (680-719)' },
  { id: 'fair', label: 'Fair (620-679)' },
  { id: 'needs-help', label: 'Needs Help (below 620)' }
];
const termLengths = [36, 48, 60, 72, 84]; // Static definition of terms
// Reactive object to hold interest rates fetched from DB and bound to inputs
const interestRates = reactive({
  excellent: { 36: 0, 48: 0, 60: 0, 72: 0, 84: 0 },
  good: { 36: 0, 48: 0, 60: 0, 72: 0, 84: 0 },
  fair: { 36: 0, 48: 0, 60: 0, 72: 0, 84: 0 },
  'needs-help': { 36: 0, 48: 0, 60: 0, 72: 0, 84: 0 }
});

// --- Financing FAQ State ---
const isLoadingFaqs = ref(true); // Loading state for fetching FAQs
const isSavingFaqs = ref(false); // State for when saving FAQs
const saveSuccessFaqs = ref(false); // Success message state
const saveErrorFaqs = ref(false); // Error message state
const errorMessageFaqs = ref(''); // Error message text
// Reactive array to hold FAQs fetched from DB and bound to inputs
const financingFaqs = ref([
  { question: '', answer: '' } // Start with one empty FAQ
]);

// --- Financing Options State ---
const isLoadingFinancingOptions = ref(true); // Loading state for fetching financing options
const isSavingFinancingOptions = ref(false); // State for when saving financing options
const saveSuccessFinancingOptions = ref(false); // Success message state
const saveErrorFinancingOptions = ref(false); // Error message state
const errorMessageFinancingOptions = ref(''); // Error message text

// Reactive object to hold financing options content
const financingOptionsContent = reactive({
  title: 'Financing Options',
  description: 'We offer a variety of financing options to meet your needs and budget.',
  options: [
    {
      title: 'Standard Financing',
      description: 'Traditional auto financing with competitive rates based on your credit score.',
      image: '/STANDARDFINANCING.png',
      features: [
        'Competitive interest rates',
        'Flexible terms from 36 to 84 months',
        'Quick approval process',
        'No prepayment penalties'
      ]
    },
    {
      title: 'Special Programs',
      description: 'Specialized financing programs for unique situations.',
      image: '/SPECIALPROGRAMS.png',
      features: [
        'First-time buyer programs',
        'College graduate programs',
        'Military discounts',
        'Healthcare worker programs'
      ]
    },
    {
      title: 'Lease Options',
      description: 'Flexible lease options with lower monthly payments.',
      image: '/LEASEOPTIONS.png',
      features: [
        'Lower monthly payments',
        'Drive a new car every few years',
        'Multiple mileage options',
        'Lease-end purchase options'
      ]
    },
    {
      title: 'Credit Rebuilding',
      description: 'Programs designed to help rebuild your credit while getting the vehicle you need.',
      image: '/CREDITREBUILDING.png',
      features: [
        'Options for all credit situations',
        'Credit score improvement opportunities',
        'Refinancing options after credit improvement',
        'Financial education resources'
      ]
    }
  ]
});

// --- Financing Process State ---
const isLoadingFinancingProcess = ref(true); // Loading state for fetching financing process
const isSavingFinancingProcess = ref(false); // State for when saving financing process
const saveSuccessFinancingProcess = ref(false); // Success message state
const saveErrorFinancingProcess = ref(false); // Error message state
const errorMessageFinancingProcess = ref(''); // Error message text

// Reactive object to hold financing process content
const financingProcessContent = reactive({
  title: 'Our Financing Process',
  description: 'We make financing your vehicle simple and straightforward with our easy step-by-step process.',
  steps: [
    {
      title: 'Pre-Qualification',
      description: 'Complete our simple pre-qualification form to get started without affecting your credit score.',
      icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
      expandedDetails: [
        'No impact on your credit score',
        'Quick response time',
        'Multiple financing options presented',
        'Personalized rate estimates'
      ]
    },
    {
      title: 'Application',
      description: 'Complete a full application with one of our financing specialists who will guide you through the process.',
      icon: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z',
      expandedDetails: [
        'Dedicated financing specialist',
        'Help gathering necessary documentation',
        'Multiple lender options',
        'Transparent process with no hidden fees'
      ]
    },
    {
      title: 'Approval',
      description: 'Receive approval from our network of lenders with competitive rates and terms tailored to your situation.',
      icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
      expandedDetails: [
        'Fast approval process',
        'Multiple offers to choose from',
        'Clear explanation of terms',
        'Flexible down payment options'
      ]
    },
    {
      title: 'Finalization',
      description: 'Review and sign your financing documents, and drive away in your new vehicle.',
      icon: 'M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4',
      expandedDetails: [
        'Digital document signing available',
        'Clear explanation of all terms',
        'No surprise fees or charges',
        'Same-day vehicle delivery possible'
      ]
    }
  ]
});

// Track expanded sections for financing tab
const financingExpandedSections = reactive({
  rates: false, // Interest Rates section collapsed by default
  faq: false, // FAQ section collapsed by default
  preQualifications: false, // Pre-Qualification Submissions section collapsed by default
  financingOptions: false, // Financing Options section collapsed by default
  financingProcess: false // Financing Process section collapsed by default
});

// Toggle section expansion for financing tab
const toggleFinancingSection = (section) => {
  financingExpandedSections[section] = !financingExpandedSections[section];
};

// --- Pre-Qualification State ---
const preQualifications = ref([]); // Store pre-qualification submissions
const isLoadingPreQualifications = ref(true); // Loading state for pre-qualification data
const preQualificationError = ref(null); // Error message if fetching fails
const preQualificationSortKey = ref('created_at'); // Default sort by creation date
const preQualificationSortDir = ref('desc'); // Default sort direction (newest first)

// --- Business Information State ---
const isLoadingBusiness = ref(true); // Separate loading state for fetching business information
const isSavingBusiness = ref(false); // State for when saving business information
const saveSuccessBusiness = ref(false); // Success message state
const saveErrorBusiness = ref(false); // Error message state
const errorMessageBusiness = ref(''); // Error message text
// Reactive object to hold business information fetched from DB and bound to inputs
const businessInfo = reactive({
  about: {
    title: '',
    description: '',
    mission: '',
    vision: '',
    history: ''
  },
  reviews: [
    { id: 1, author: '', vehicle: '', rating: 5, text: '' },
    { id: 2, author: '', vehicle: '', rating: 5, text: '' },
    { id: 3, author: '', vehicle: '', rating: 5, text: '' }
  ],
  services: [
    { id: 1, title: '', description: '', icon: '' },
    { id: 2, title: '', description: '', icon: '' },
    { id: 3, title: '', description: '', icon: '' },
    { id: 4, title: '', description: '', icon: '' }
  ],
  contact: {
    address: '',
    phone: '',
    email: '',
    hours: [
      { day: 'Monday', open: '', close: '' },
      { day: 'Tuesday', open: '', close: '' },
      { day: 'Wednesday', open: '', close: '' },
      { day: 'Thursday', open: '', close: '' },
      { day: 'Friday', open: '', close: '' },
      { day: 'Saturday', open: '', close: '' },
      { day: 'Sunday', open: '', close: '' }
    ]
  }
});

// --- Detailing State ---
const isLoadingDetailing = ref(true); // Separate loading state for fetching detailing settings
const isSavingDetailing = ref(false); // State for when saving detailing settings
const saveSuccessDetailing = ref(false); // Success message state
const saveErrorDetailing = ref(false); // Error message state
const errorMessageDetailing = ref(''); // Error message text
const vehicleCategories = [ // Static definition of categories
  { id: 'Sedans', label: 'Sedans' },
  { id: 'SUVs/Small Vans', label: 'SUVs/Small Vans' },
  { id: 'Trucks/Large Vans', label: 'Trucks/Large Vans' },
  { id: 'Limo, Semis, Trailers', label: 'Limo, Semis, Trailers', specialPricing: true }
];
const serviceTiers = [ // Static definition of regular service tiers
  { id: 'Express Detailing', label: 'Express Detailing' },
  { id: 'Tier 1', label: 'Tier 1' },
  { id: 'Tier 2', label: 'Tier 2' },
  { id: 'Tier 3', label: 'Tier 3' },
  { id: 'Tier 4', label: 'Tier 4' }
];
const specialServices = [ { id: 'Base Price', label: 'Base Price' } ]; // Static definition for special category

// Track expanded sections for detailing tab
const detailingExpandedSections = reactive({
  pricing: false, // Pricing section collapsed by default
  whyChooseUs: false, // Why Choose Us section collapsed by default
  detailingProcess: false // Detailing Process section collapsed by default
});

// Reactive object to hold detailing prices fetched from DB and bound to inputs
// Initialize with default values as placeholders until data is fetched
const detailingPrices = reactive({
  'Sedans': { 'Express Detailing': 0, 'Tier 1': 0, 'Tier 2': 0, 'Tier 3': 0, 'Tier 4': 0 },
  'SUVs/Small Vans': { 'Express Detailing': 0, 'Tier 1': 0, 'Tier 2': 0, 'Tier 3': 0, 'Tier 4': 0 },
  'Trucks/Large Vans': { 'Express Detailing': 0, 'Tier 1': 0, 'Tier 2': 0, 'Tier 3': 0, 'Tier 4': 0 },
  'Limo, Semis, Trailers': { 'Base Price': 0 }
});

// Reactive object to hold Why Choose Us section content
const whyChooseUsContent = reactive({
  title: 'Why Choose Our Detailing Service',
  description: 'We provide exceptional car detailing services with attention to every detail, ensuring your vehicle looks its absolute best.',
  features: [
    { title: 'Premium Products', description: 'We use only the highest quality detailing products to ensure the best results for your vehicle.', icon: 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z' },
    { title: 'Experienced Staff', description: 'Our detailing technicians are highly trained professionals with years of experience.', icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z' },
    { title: 'Attention to Detail', description: 'We meticulously clean every nook and cranny of your vehicle for a truly comprehensive detailing.', icon: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z' },
    { title: 'Convenient Service', description: 'Drop off your vehicle or wait in our comfortable lounge while we transform your car.', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' }
  ]
});

// Reactive object to hold Detailing Process section content
const detailingProcessContent = reactive({
  title: 'Our Detailing Process',
  description: 'We follow a meticulous process to ensure your vehicle receives the best possible care.',
  steps: [
    {
      title: 'Initial Assessment',
      description: 'We thoroughly inspect your vehicle to identify areas that need special attention.',
      image: '/ASSESSMENT.jpg',
      expandedDetails: [
        'Comprehensive 27-point inspection of your vehicle',
        'Documentation of existing conditions and problem areas',
        'Personalized consultation to discuss findings',
        'Custom detailing package recommendations',
        'Transparent pricing with no hidden fees'
      ]
    },
    {
      title: 'Exterior Cleaning',
      description: 'Using premium products, we wash, clay, and polish the exterior to remove contaminants and restore shine.',
      image: '/EXTERIORDETAIL.jpg',
      expandedDetails: [
        'Foam pre-soak to loosen dirt and grime',
        'Two-bucket hand wash with pH-balanced soap',
        'Clay bar treatment to remove embedded contaminants',
        'Machine polishing to remove swirl marks and scratches',
        'Premium wax or ceramic coating application',
        'Wheel, tire, and trim detailing'
      ]
    },
    {
      title: 'Interior Detailing',
      description: 'We meticulously clean all interior surfaces, including hard-to-reach areas, to remove dirt and stains.',
      image: '/INTERIORDETAIL.jpg',
      expandedDetails: [
        'Deep vacuum of all surfaces including seats and carpets',
        'Material-specific cleaning for leather, vinyl, and fabric',
        'Stubborn stain treatment with specialized products',
        'Steam cleaning for carpets and upholstery',
        'Streak-free glass cleaning inside and out',
        'UV protectant application for dashboard and trim'
      ]
    },
    {
      title: 'Final Touches',
      description: 'We apply protective coatings and perform a final inspection to ensure everything meets our high standards.',
      image: '/FINALTOUCH.jpg',
      expandedDetails: [
        'Tire dressing and trim restoration',
        'Final wipe-down with specialized microfiber towels',
        'Quality control inspection using detailed checklist',
        'Personalized walkthrough of completed services',
        'Maintenance tips to preserve the detailed finish',
        'Follow-up scheduling for ongoing care'
      ]
    }
  ]
});

// --- Authentication & Initial Data Fetch ---
onMounted(async () => {
  // Initialize authentication state
  authStore.initAuth();
  if (!authStore.isAuthenticated.value) {
    router.push('/admin/login'); // Redirect if not authenticated
    return;
  }

  isLoading.value = true; // Start global loading indicator

  // Set specific loading states
  isTableLoading.value = true; // <-- Set inventory table loading to true initially
  isLoadingFinancing.value = true;
  isLoadingFinancingOptions.value = true;
  isLoadingFinancingProcess.value = true;
  isLoadingDetailing.value = true;
  isLoadingBusiness.value = true; // Set loading for business info

  try {
    // Fetch all necessary data concurrently
    await Promise.all([
      fetchInventoryData(),
      fetchFinancingData(),
      fetchDetailingData(),
      fetchBusinessData(), // Fetch business data
      fetchPreQualificationData(), // Fetch pre-qualification data
      stylingStore.initStore() // Initialize styling store
    ]);
    console.log("All initial data fetched.");
  } catch (error) {
    console.error("Failed to load initial dashboard data:", error);
    // Optionally set a global error state to display a message
  } finally {
    // Stop all loading indicators
    isLoading.value = false;
    isTableLoading.value = false; // <-- Set inventory table loading to false after fetch
    isLoadingFinancing.value = false;
    isLoadingFinancingOptions.value = false;
    isLoadingFinancingProcess.value = false;
    isLoadingDetailing.value = false;
    isLoadingBusiness.value = false; // Stop loading for business info
  }
});

// --- Data Fetching Functions ---
async function fetchInventoryData() {
  console.log("Fetching inventory data...");
  // Keep isTableLoading true until data is fetched or error occurs
  try {
    // Set the page size in the store to match our dashboard setting
    vehicleStore.pageSize.value = itemsPerPage.value;

    // Reset pagination to ensure we start fresh
    vehicleStore.currentPage.value = 0; // Set to 0 because loadNextPage will increment to 1
    currentPage.value = 1;

    // Reset pagination state
    vehicleStore.hasMoreVehicles.value = true;
    vehicleStore.lastPageLoaded.value = false;

    // Clear existing vehicles to ensure we get fresh data
    vehicleStore.vehicles.value = [];

    // Load the first page of vehicles
    console.log("Loading first page of vehicles with page size:", itemsPerPage.value);
    await vehicleStore.loadNextPage();

    // If we still don't have vehicles, try a forced refresh
    if (vehicleStore.vehicles.value.length === 0) {
      console.log("Vehicle store still empty, forcing refresh...");
      await vehicleStore.refreshStore();
    }

    // Get the total count for pagination
    if (vehicleStore.totalVehicles.value === 0 && vehicleStore.vehicles.value.length > 0) {
      // If we have vehicles but no total count, estimate based on what we have
      console.log("No total count available, estimating based on current data");
      vehicleStore.totalVehicles.value = Math.max(vehicleStore.vehicles.value.length, itemsPerPage.value);
    }

    console.log(`Inventory data fetched successfully. ${vehicleStore.vehicles.value.length} vehicles loaded.`);
    console.log(`Total vehicles in store: ${vehicleStore.totalVehicles.value}`);
    console.log(`Store pagination state: currentPage=${vehicleStore.currentPage.value}, hasMoreVehicles=${vehicleStore.hasMoreVehicles.value}, lastPageLoaded=${vehicleStore.lastPageLoaded.value}`);

    // Log vehicle data for debugging
    logVehicleData();
  } catch (error) {
    console.error("Failed to fetch vehicles:", error);
    // Consider setting an inventory-specific error flag if needed
  }
  // Note: isTableLoading is set to false in the onMounted finally block
}

async function fetchFinancingData() {
    console.log("Fetching financing data...");
    isLoadingFinancing.value = true;
    isLoadingFaqs.value = true;
    isLoadingFinancingOptions.value = true;
    isLoadingFinancingProcess.value = true;
    saveErrorFinancing.value = false; // Reset error state on new fetch attempt
    saveErrorFaqs.value = false; // Reset error state for FAQs
    saveErrorFinancingOptions.value = false; // Reset error state for financing options
    saveErrorFinancingProcess.value = false; // Reset error state for financing process
    errorMessageFinancing.value = '';
    errorMessageFaqs.value = '';
    errorMessageFinancingOptions.value = '';
    errorMessageFinancingProcess.value = '';

  try {
    // Call the service functions to get all financing data from Supabase
    const [ratesFromDB, faqsFromDB, optionsFromDB, processFromDB] = await Promise.all([
      getInterestRates(),
      getFinancingFaqs(),
      getFinancingOptions(),
      getFinancingProcess()
    ]);

    console.log("Raw financing rates from DB:", ratesFromDB);
    console.log("Raw financing FAQs from DB:", faqsFromDB);
    console.log("Raw financing options from DB:", optionsFromDB);
    console.log("Raw financing process from DB:", processFromDB);

    // Process interest rates
    if (ratesFromDB && typeof ratesFromDB === 'object') {
      // Update the local reactive object with fetched data
      for (const tier of Object.keys(interestRates)) {
        if (ratesFromDB[tier]) {
          for (const term of termLengths) {
            // Check if the specific rate exists and is a number in the fetched data
            if (ratesFromDB[tier][term] !== undefined && ratesFromDB[tier][term] !== null && !isNaN(parseFloat(ratesFromDB[tier][term]))) {
              interestRates[tier][term] = parseFloat(ratesFromDB[tier][term]);
            } else {
              console.warn(`Missing or invalid rate for financing tier '${tier}', term '${term}'. Keeping default/previous value.`);
              // Keep the default 0 or previously loaded value if the fetched one is invalid/missing
            }
          }
        } else {
            console.warn(`Financing tier '${tier}' not found in fetched data. Keeping default/previous values.`);
        }
      }
      console.log("Financing rates processed:", JSON.parse(JSON.stringify(interestRates))); // Log processed data
    } else {
        console.warn("No financing rates data found in database or invalid format returned.");
        // Keep default values if nothing is fetched
        errorMessageFinancing.value = "No financing rates found in settings. Using defaults.";
        saveErrorFinancing.value = true; // Indicate a non-critical error (data missing)
    }

    // Process FAQs
    if (faqsFromDB && Array.isArray(faqsFromDB) && faqsFromDB.length > 0) {
      financingFaqs.value = faqsFromDB;
      console.log("Financing FAQs processed:", JSON.parse(JSON.stringify(financingFaqs.value)));
    } else {
      console.warn("No financing FAQs found in database or invalid format returned.");
      // Keep default values (one empty FAQ) if nothing is fetched
      financingFaqs.value = [{ question: '', answer: '' }];
      errorMessageFaqs.value = "No FAQs found in settings. Using defaults.";
      saveErrorFaqs.value = true; // Indicate a non-critical error (data missing)
    }

    // Process Financing Options
    if (optionsFromDB && typeof optionsFromDB === 'object') {
      // Update title and description if they exist
      if (optionsFromDB.title) {
        financingOptionsContent.title = optionsFromDB.title;
      }
      if (optionsFromDB.description) {
        financingOptionsContent.description = optionsFromDB.description;
      }
      // Update options if they exist and are in the correct format
      if (Array.isArray(optionsFromDB.options) && optionsFromDB.options.length > 0) {
        financingOptionsContent.options = optionsFromDB.options;
      }
      console.log("Financing options processed:", JSON.parse(JSON.stringify(financingOptionsContent)));
    } else {
      console.warn("No financing options found in database or invalid format returned.");
      // Keep default values if nothing is fetched
      errorMessageFinancingOptions.value = "No financing options found in settings. Using defaults.";
      saveErrorFinancingOptions.value = true; // Indicate a non-critical error (data missing)

      // Try to load from localStorage as fallback
      try {
        const savedFinancingOptions = localStorage.getItem('financingOptionsContent');
        if (savedFinancingOptions) {
          const parsedOptions = JSON.parse(savedFinancingOptions);
          if (parsedOptions && typeof parsedOptions === 'object') {
            // Update title and description if they exist
            if (parsedOptions.title) {
              financingOptionsContent.title = parsedOptions.title;
            }
            if (parsedOptions.description) {
              financingOptionsContent.description = parsedOptions.description;
            }
            // Update options if they exist and are in the correct format
            if (Array.isArray(parsedOptions.options) && parsedOptions.options.length > 0) {
              financingOptionsContent.options = parsedOptions.options;
            }
            console.log("Financing options loaded from localStorage as fallback:", JSON.parse(JSON.stringify(financingOptionsContent)));
          }
        }
      } catch (error) {
        console.warn("Failed to load financing options from localStorage:", error);
      }
    }

    // Process Financing Process
    if (processFromDB && typeof processFromDB === 'object') {
      // Update title and description if they exist
      if (processFromDB.title) {
        financingProcessContent.title = processFromDB.title;
      }
      if (processFromDB.description) {
        financingProcessContent.description = processFromDB.description;
      }
      // Update steps if they exist and are in the correct format
      if (Array.isArray(processFromDB.steps) && processFromDB.steps.length > 0) {
        financingProcessContent.steps = processFromDB.steps;
      }
      console.log("Financing process processed:", JSON.parse(JSON.stringify(financingProcessContent)));
    } else {
      console.warn("No financing process found in database or invalid format returned.");
      // Keep default values if nothing is fetched
      errorMessageFinancingProcess.value = "No financing process found in settings. Using defaults.";
      saveErrorFinancingProcess.value = true; // Indicate a non-critical error (data missing)

      // Try to load from localStorage as fallback
      try {
        const savedFinancingProcess = localStorage.getItem('financingProcessContent');
        if (savedFinancingProcess) {
          const parsedProcess = JSON.parse(savedFinancingProcess);
          if (parsedProcess && typeof parsedProcess === 'object') {
            // Update title and description if they exist
            if (parsedProcess.title) {
              financingProcessContent.title = parsedProcess.title;
            }
            if (parsedProcess.description) {
              financingProcessContent.description = parsedProcess.description;
            }
            // Update steps if they exist and are in the correct format
            if (Array.isArray(parsedProcess.steps) && parsedProcess.steps.length > 0) {
              financingProcessContent.steps = parsedProcess.steps;
            }
            console.log("Financing process loaded from localStorage as fallback:", JSON.parse(JSON.stringify(financingProcessContent)));
          }
        }
      } catch (error) {
        console.warn("Failed to load financing process from localStorage:", error);
      }
    }
  } catch (error) {
    console.error("Failed to fetch financing data:", error);
    errorMessageFinancing.value = "Failed to load interest rate data from the database.";
    saveErrorFinancing.value = true; // Indicate a critical error
    errorMessageFaqs.value = "Failed to load FAQ data from the database.";
    saveErrorFaqs.value = true; // Indicate a critical error
    errorMessageFinancingOptions.value = "Failed to load financing options from the database.";
    saveErrorFinancingOptions.value = true; // Indicate a critical error
    errorMessageFinancingProcess.value = "Failed to load financing process from the database.";
    saveErrorFinancingProcess.value = true; // Indicate a critical error
  } finally {
    isLoadingFinancing.value = false;
    isLoadingFaqs.value = false;
    isLoadingFinancingOptions.value = false;
    isLoadingFinancingProcess.value = false;
    console.log("Financing data fetch complete.");
  }
}

// --- Fetch Pre-Qualification Data ---
async function fetchPreQualificationData() {
  console.log("Fetching pre-qualification data...");
  isLoadingPreQualifications.value = true;
  preQualificationError.value = null;

  try {
    // Call the service function to get pre-qualification submissions from Supabase
    const result = await getAllPreQualifications();

    if (result.success && result.data) {
      preQualifications.value = result.data;
      console.log("Pre-qualification data fetched successfully:", preQualifications.value);
    } else {
      console.warn("Failed to fetch pre-qualification data:", result.error);
      preQualificationError.value = result.error || "Failed to load pre-qualification data.";
    }
  } catch (error) {
    console.error("Error fetching pre-qualification data:", error);
    preQualificationError.value = "An unexpected error occurred while loading pre-qualification data.";
  } finally {
    isLoadingPreQualifications.value = false;
    console.log("Pre-qualification data fetch complete.");
  }
}

// --- Fetch Business Information ---
async function fetchBusinessData() {
  console.log("Fetching business information...");
  isLoadingBusiness.value = true; // Ensure loading state is true at the start
  saveErrorBusiness.value = false; // Reset error state
  errorMessageBusiness.value = '';

  try {
    // Call the service function to get business info from Supabase
    const businessInfoFromDB = await getBusinessInfo();
    console.log("Raw business info from DB:", businessInfoFromDB);

    if (businessInfoFromDB && typeof businessInfoFromDB === 'object') {
      // Update the local reactive object with fetched data

      // Handle about section (Deep merge or replace)
      if (businessInfoFromDB.about) {
        Object.keys(businessInfo.about).forEach(key => {
          if (businessInfoFromDB.about[key] !== undefined && businessInfoFromDB.about[key] !== null) {
            businessInfo.about[key] = businessInfoFromDB.about[key];
          } else {
            // Optionally reset to default if DB value is null/undefined
            // businessInfo.about[key] = ''; // Or keep existing value
          }
        });
      }

      // Handle reviews (Replace existing array if data is valid)
      if (Array.isArray(businessInfoFromDB.reviews)) {
         // Ensure reviews have a unique ID, map if necessary or assume DB provides it
         businessInfo.reviews = businessInfoFromDB.reviews.map((review, index) => ({
            id: review.id || Date.now() + index, // Generate fallback ID if missing
            ...review
         }));
      }

      // Handle services (Replace existing array if data is valid)
      if (Array.isArray(businessInfoFromDB.services)) {
         businessInfo.services = businessInfoFromDB.services.map((service, index) => ({
            id: service.id || Date.now() + index + 1000, // Generate fallback ID
            ...service
         }));
      }

      // Handle contact information (Deep merge or replace)
      if (businessInfoFromDB.contact) {
        // Update basic contact info
        ['address', 'phone', 'email'].forEach(key => {
          if (businessInfoFromDB.contact[key] !== undefined && businessInfoFromDB.contact[key] !== null) {
            businessInfo.contact[key] = businessInfoFromDB.contact[key];
          } else {
            // businessInfo.contact[key] = ''; // Or keep existing
          }
        });

        // Update business hours (Carefully merge or replace)
        if (Array.isArray(businessInfoFromDB.contact.hours) && businessInfoFromDB.contact.hours.length === businessInfo.contact.hours.length) {
           // Assuming the order and days match
           businessInfoFromDB.contact.hours.forEach((hourInfo, index) => {
             businessInfo.contact.hours[index] = {
               ...businessInfo.contact.hours[index], // Keep the original day name
               open: hourInfo.open ?? '', // Use nullish coalescing for defaults
               close: hourInfo.close ?? ''
             };
           });
        } else if (businessInfoFromDB.contact.hours) {
            console.warn("Fetched business hours structure doesn't match default structure. Check data format.");
            // Decide on fallback strategy: keep defaults, try partial merge, etc.
        }
      }

      console.log("Business information processed:", JSON.parse(JSON.stringify(businessInfo)));
    } else {
      console.warn("No business information found in database or invalid format returned.");
      errorMessageBusiness.value = "No business information found in settings. Using defaults.";
      saveErrorBusiness.value = true; // Indicate a non-critical error (data missing)
    }
  } catch (error) {
    console.error("Failed to fetch business information:", error);
    errorMessageBusiness.value = "Failed to load business information from the database.";
    saveErrorBusiness.value = true; // Indicate a critical error
  } finally {
    isLoadingBusiness.value = false; // Ensure loading state is false at the end
    console.log("Business information fetch complete.");
  }
}


async function fetchDetailingData() {
    console.log("Fetching detailing data...");
    isLoadingDetailing.value = true;
    saveErrorDetailing.value = false; // Reset error state
    errorMessageDetailing.value = '';
  try {
    // Call the service function to get detailing settings from Supabase
    const detailingSettings = await getDetailingPrices();
    console.log("Raw detailing settings from DB:", detailingSettings);
    console.log("Detailing settings structure:", JSON.stringify(detailingSettings, null, 2));

    if (detailingSettings && typeof detailingSettings === 'object') {
      // Check if we have the new structure with pricing property or the old direct structure
      const pricingData = detailingSettings.pricing || detailingSettings;

      // Process pricing data
      if (pricingData) {
        console.log("Processing pricing data:", pricingData);
        // Update the local reactive object with fetched pricing data
        for (const categoryKey of Object.keys(detailingPrices)) {
          // Find the matching category definition
          const categoryDef = vehicleCategories.find(cat => cat.id === categoryKey);
          if (!categoryDef) continue; // Skip if category definition doesn't exist

          if (pricingData[categoryKey]) {
            for (const serviceKey of Object.keys(detailingPrices[categoryKey])) {
              // Check if the specific price exists and is a number in the fetched data
              if (pricingData[categoryKey][serviceKey] !== undefined &&
                  pricingData[categoryKey][serviceKey] !== null &&
                  !isNaN(parseFloat(pricingData[categoryKey][serviceKey]))) {
                detailingPrices[categoryKey][serviceKey] = parseFloat(pricingData[categoryKey][serviceKey]);
              } else {
                console.warn(`Missing or invalid price for detailing category '${categoryKey}', service '${serviceKey}'. Keeping default/previous value.`);
                // Keep default 0 or previously loaded value
              }
            }
          } else {
            console.warn(`Detailing category '${categoryKey}' not found in fetched pricing data. Keeping default/previous values.`);
          }
        }
        console.log("Detailing prices processed:", JSON.parse(JSON.stringify(detailingPrices))); // Log processed data
      } else {
        console.warn("No pricing data found in detailing settings. Using defaults.");
      }

      // Process Why Choose Us section data
      if (detailingSettings.whyChooseUs) {
        // Update title and description if they exist
        if (detailingSettings.whyChooseUs.title) {
          whyChooseUsContent.title = detailingSettings.whyChooseUs.title;
        }
        if (detailingSettings.whyChooseUs.description) {
          whyChooseUsContent.description = detailingSettings.whyChooseUs.description;
        }

        // Update features if they exist and are in the correct format
        if (Array.isArray(detailingSettings.whyChooseUs.features) && detailingSettings.whyChooseUs.features.length > 0) {
          // Replace the entire features array
          whyChooseUsContent.features = detailingSettings.whyChooseUs.features;
        }

        console.log("Why Choose Us content processed:", JSON.parse(JSON.stringify(whyChooseUsContent)));
      } else {
        console.warn("No Why Choose Us data found in detailing settings. Using defaults.");
      }

      // Process Detailing Process section data
      if (detailingSettings.detailingProcess) {
        // Update title and description if they exist
        if (detailingSettings.detailingProcess.title) {
          detailingProcessContent.title = detailingSettings.detailingProcess.title;
        }
        if (detailingSettings.detailingProcess.description) {
          detailingProcessContent.description = detailingSettings.detailingProcess.description;
        }

        // Update steps if they exist and are in the correct format
        if (Array.isArray(detailingSettings.detailingProcess.steps) && detailingSettings.detailingProcess.steps.length > 0) {
          // Replace the entire steps array
          detailingProcessContent.steps = detailingSettings.detailingProcess.steps;
        }

        console.log("Detailing Process content processed:", JSON.parse(JSON.stringify(detailingProcessContent)));
      } else {
        console.warn("No Detailing Process data found in detailing settings. Using defaults.");
      }
    } else {
      console.warn("No detailing settings data found in database or invalid format returned.");
      // Keep default values if nothing is fetched
      errorMessageDetailing.value = "No detailing settings found. Using defaults.";
      saveErrorDetailing.value = true; // Indicate non-critical error
    }
  } catch (error) {
    console.error("Failed to fetch detailing settings:", error);
    errorMessageDetailing.value = "Failed to load detailing settings from the database.";
    saveErrorDetailing.value = true; // Indicate critical error
  } finally {
      isLoadingDetailing.value = false;
      console.log("Detailing data fetch complete.");
  }
}

// --- Inventory Logic (Computed, Watchers, Methods) ---
watch(searchQuery, debounce((newValue) => {
  debouncedSearchQuery.value = newValue;
  currentPage.value = 1; // Reset pagination on search
}, 300)); // Debounce search input

// Watch for tab changes to reset relevant state
watch(activeTab, (newTab, oldTab) => {
  console.log(`Tab changed from ${oldTab} to ${newTab}`);

  // Reset styling store state when switching to styling tab
  if (newTab === 'styling') {
    console.log('Switching to styling tab, resetting styling store state');

    // Force reset all styling store state flags BEFORE initializing
    stylingStore.isSaving.value = false;
    stylingStore.saveSuccess.value = false;
    stylingStore.saveError.value = false;
    stylingStore.errorMessage.value = '';

    // Add a small delay before initializing to ensure state is reset
    setTimeout(() => {
      // Double-check states are still false before initializing
      if (stylingStore.isSaving.value || stylingStore.saveSuccess.value) {
        console.warn('Store states were not reset before init, forcing reset again');
        stylingStore.isSaving.value = false;
        stylingStore.saveSuccess.value = false;
      }

      // Re-initialize the styling store to ensure fresh data
      stylingStore.initStore().catch(error => {
        console.error('Error initializing styling store when switching to tab:', error);
        stylingStore.errorMessage.value = 'Failed to load styling settings. Please refresh the page.';
        stylingStore.saveError.value = true;
      });

      // Double-check states again after a short delay
      setTimeout(() => {
        if (stylingStore.isSaving.value || stylingStore.saveSuccess.value) {
          console.warn('Store states still not reset after init, forcing reset one more time');
          stylingStore.isSaving.value = false;
          stylingStore.saveSuccess.value = false;
        }
      }, 100);
    }, 50);
  }

  // Force reset styling store state when switching away from styling tab
  if (oldTab === 'styling') {
    console.log('Switching away from styling tab, forcing state reset');
    // Force reset all state flags to prevent stuck states
    stylingStore.isSaving.value = false;
    stylingStore.saveSuccess.value = false;
    stylingStore.saveError.value = false;

    // Clear any timeout that might be running
    setTimeout(() => {
      // Double-check after a small delay to ensure state is reset
      if (stylingStore.isSaving.value || stylingStore.saveSuccess.value) {
        console.warn('Styling store states still not reset after tab change, forcing reset');
        stylingStore.isSaving.value = false;
        stylingStore.saveSuccess.value = false;
      }
    }, 500);
  }
});

// Debug function to log vehicle data
const logVehicleData = () => {
  if (vehicleStore.vehicles.value && vehicleStore.vehicles.value.length > 0) {
    const firstVehicle = vehicleStore.vehicles.value[0];
    console.log('[Admin Dashboard] First vehicle data:', {
      id: firstVehicle.id,
      title: firstVehicle.title,
      image: firstVehicle.image,
      hasImage: !!firstVehicle.image,
      imageType: typeof firstVehicle.image,
      galleryLength: firstVehicle.gallery ? firstVehicle.gallery.length : 0
    });

    // Check if the image URL is a valid Supabase URL
    if (firstVehicle.image && typeof firstVehicle.image === 'string') {
      const isSupabaseUrl = firstVehicle.image.includes('supabase.co/storage/v1/object/public/car-images/');
      const isHttpUrl = firstVehicle.image.startsWith('http');
      console.log('[Admin Dashboard] Image URL check:', {
        isSupabaseUrl,
        isHttpUrl,
        imageUrl: firstVehicle.image,
        transformedUrl: getVehicleImageUrl(firstVehicle)
      });
    }

    // Log all vehicle images for debugging
    console.log('[Admin Dashboard] All vehicle images:');
    vehicleStore.vehicles.value.slice(0, 5).forEach((vehicle, index) => {
      console.log(`Vehicle ${index + 1}:`, {
        id: vehicle.id,
        imageUrl: vehicle.image,
        transformedUrl: getVehicleImageUrl(vehicle)
      });
    });
  } else {
    console.log('[Admin Dashboard] No vehicles available to log');
  }
};

// Filter vehicles based on search query
const filteredVehicles = computed(() => {
  const query = debouncedSearchQuery.value.toLowerCase().trim();
  const allVehicles = vehicleStore.vehicles.value || [];
  if (!query) return allVehicles;
  return allVehicles.filter(vehicle =>
    (vehicle.title && vehicle.title.toLowerCase().includes(query)) ||
    (vehicle.make && vehicle.make.toLowerCase().includes(query)) ||
    (vehicle.model && vehicle.model.toLowerCase().includes(query)) ||
    (vehicle.stockNumber && String(vehicle.stockNumber).toLowerCase().includes(query)) || // Ensure stockNumber is treated as string
    (vehicle.vin && vehicle.vin.toLowerCase().includes(query))
  );
});

// Sort the filtered vehicles
const sortedVehicles = computed(() => {
  const vehicles = [...filteredVehicles.value];
  if (sortKey.value) {
    vehicles.sort((a, b) => {
      let valA = a[sortKey.value];
      let valB = b[sortKey.value];
      const isNumeric = ['price', 'specialPrice', 'year', 'mileage'].includes(sortKey.value);
      const isString = typeof valA === 'string';

      if (isNumeric) {
        valA = Number(valA) || 0; // Handle potential null/undefined/non-numeric
        valB = Number(valB) || 0;
      } else if (isString) {
        valA = valA?.toLowerCase() || ''; // Handle potential null/undefined
        valB = valB?.toLowerCase() || '';
      } else {
          // Handle other types or fallback to 0/empty string if necessary
          valA = valA ?? '';
          valB = valB ?? '';
      }

      let comparison = 0;
      if (valA < valB) comparison = -1;
      else if (valA > valB) comparison = 1;

      return sortDir.value === 'asc' ? comparison : comparison * -1;
    });
  }
  return vehicles;
});

// Calculate total pages for pagination
const totalPages = computed(() => {
    // Use the total count from the vehicle store if available
    if (vehicleStore.totalVehicles.value > 0) {
        return Math.ceil(vehicleStore.totalVehicles.value / itemsPerPage.value) || 1;
    }

    // Fallback to local calculation if store doesn't have total count
    if (!sortedVehicles.value) return 1;
    return Math.ceil(sortedVehicles.value.length / itemsPerPage.value) || 1; // Ensure at least 1 page
});

// Get the vehicles for the current page
const paginatedVehicles = computed(() => {
  // Adjust currentPage if it becomes invalid after filtering/sorting
  if (currentPage.value > totalPages.value) {
      currentPage.value = totalPages.value;
  }
  if (currentPage.value < 1) {
      currentPage.value = 1;
  }

  // Return the vehicles from the store directly
  // The store should already have the correct page loaded
  return vehicleStore.vehicles.value;
});

// Display pagination info text
const paginationInfo = computed(() => {
    // Use total count from store if available, otherwise use local count
    const totalItems = vehicleStore.totalVehicles.value > 0
        ? vehicleStore.totalVehicles.value
        : sortedVehicles.value.length;

    if (totalItems === 0) return "Showing 0 results";

    const startItem = (currentPage.value - 1) * itemsPerPage.value + 1;
    const endItem = Math.min(currentPage.value * itemsPerPage.value, totalItems);

    return `Showing ${startItem} to ${endItem} of ${totalItems} results`;
});

// Format price for inventory display
const formatInvPrice = (price) => {
  if (price === null || price === undefined) return 'N/A';
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'CAD', maximumFractionDigits: 0 }).format(price);
};

// Format mileage for inventory display
const formatMileage = (mileage) => {
    if (mileage === null || mileage === undefined) return 'N/A';
    return `${Number(mileage).toLocaleString()} km`; // Ensure mileage is treated as number
};

// Helper function to ensure vehicle images have proper Supabase URLs
const getVehicleImageUrl = (vehicle) => {
  if (!vehicle) {
    console.log('[getVehicleImageUrl] No vehicle, returning placeholder');
    return '/REDGTWHEEL.png';
  }
  
  // Log vehicle image data for debugging
  console.log('[getVehicleImageUrl] Vehicle image data:', {
    id: vehicle.id,
    title: vehicle.title,
    hasImage: !!vehicle.image,
    hasGallery: Array.isArray(vehicle.gallery) && vehicle.gallery.length > 0,
    galleryLength: Array.isArray(vehicle.gallery) ? vehicle.gallery.length : 0
  });
  
  // First try to get the first image from the gallery if it exists
  if (Array.isArray(vehicle.gallery) && vehicle.gallery.length > 0) {
    const galleryImage = vehicle.gallery[0];
    
    // If the gallery image is already a full URL, return it as is
    if (galleryImage.startsWith('http')) {
      console.log('[getVehicleImageUrl] Gallery image is already a full URL, returning as is');
      return galleryImage;
    }
    
    // Otherwise, construct the full URL
    const projectId = 'wjqlfcxgrdfyqpjsbnyp'; // Your Supabase project ID
    const fullGalleryUrl = `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${galleryImage}`;
    console.log('[getVehicleImageUrl] Converted gallery image to full URL:', fullGalleryUrl);
    return fullGalleryUrl;
  }
  
  // Fallback to the image property if gallery doesn't exist or is empty
  if (vehicle.image) {
    const url = vehicle.image;
    
    // If the URL is already a full URL, return it as is
    if (url.startsWith('http')) {
      console.log('[getVehicleImageUrl] Image is already a full URL, returning as is');
      return url;
    }
    
    // If it's a relative URL or just a path, construct the full URL
    const projectId = 'wjqlfcxgrdfyqpjsbnyp'; // Your Supabase project ID
    const fullUrl = `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${url}`;
    console.log('[getVehicleImageUrl] Converted relative path to full URL:', fullUrl);
    return fullUrl;
  }
  
  // If no image is available, return the placeholder
  console.log('[getVehicleImageUrl] No image available, returning placeholder');
  return '/REDGTWHEEL.png';
};

// Handle table header clicks for sorting
const sortBy = (key) => {
  isTableLoading.value = true; // Show loading indicator during sort
  if (sortKey.value === key) {
    sortDir.value = sortDir.value === 'asc' ? 'desc' : 'asc'; // Toggle direction
  } else {
    sortKey.value = key; // Set new sort key
    sortDir.value = 'asc'; // Default to ascending
  }
  currentPage.value = 1; // Reset to first page on sort change
  // Short delay for visual feedback, remove if sorting is slow
  setTimeout(() => { isTableLoading.value = false; }, 50);
};

// Pagination actions
const nextPage = async () => {
  if (currentPage.value < totalPages.value) {
    await goToPage(currentPage.value + 1);
  }
};

const prevPage = async () => {
  if (currentPage.value > 1) {
    await goToPage(currentPage.value - 1);
  }
};

// Go to specific page
const goToPage = async (page) => {
  if (page === currentPage.value || page < 1 || page > totalPages.value) {
    return; // Don't do anything if trying to go to current page or invalid page
  }

  isTableLoading.value = true;
  window.scrollTo({ top: 0, behavior: 'smooth' });

  try {
    console.log(`Navigating from page ${currentPage.value} to page ${page}`);

    // Update UI page
    currentPage.value = page;

    // We need to directly fetch the specific page from the database
    // instead of relying on the store's pagination mechanism
    const result = await supabaseService.getVehiclesFromSupabase({
      page: page,
      pageSize: itemsPerPage.value,
      filters: vehicleStore.activeFilters.value
    });

    // Extract vehicles and totalCount from the result
    const { vehicles, totalCount } = result;

    if (vehicles && vehicles.length > 0) {
      // Replace the vehicles in the store with the new page data
      vehicleStore.vehicles.value = vehicles;

      // Update the store's pagination state
      vehicleStore.currentPage.value = page;
      vehicleStore.totalVehicles.value = totalCount || vehicleStore.totalVehicles.value;
      vehicleStore.hasMoreVehicles.value = page * itemsPerPage.value < vehicleStore.totalVehicles.value;

      console.log(`Successfully loaded page ${page} from database with ${vehicles.length} vehicles`);
    } else {
      console.error(`No vehicles returned for page ${page}`);
      // If no vehicles returned, try to load the first page as a fallback
      if (page !== 1) {
        console.log("Falling back to page 1");
        await goToPage(1);
      }
    }
  } catch (error) {
    console.error(`Failed to load page ${page}:`, error);

    // Try to recover by loading the first page
    if (page !== 1) {
      console.log("Error occurred, falling back to page 1");
      await goToPage(1);
    }
  } finally {
    isTableLoading.value = false;
  }
};

// Calculate which page numbers to display
const displayedPageNumbers = computed(() => {
  const totalPageCount = totalPages.value;
  const current = currentPage.value;
  const delta = 2; // Number of pages to show on each side of current page

  if (totalPageCount <= 7) {
    // If we have 7 or fewer pages, show all pages
    return Array.from({ length: totalPageCount }, (_, i) => i + 1);
  }

  // Always include first and last page
  const pages = [1, totalPageCount];

  // Calculate range around current page
  const rangeStart = Math.max(2, current - delta);
  const rangeEnd = Math.min(totalPageCount - 1, current + delta);

  // Add ellipsis indicators and range
  if (rangeStart > 2) pages.push('...');
  for (let i = rangeStart; i <= rangeEnd; i++) {
    pages.push(i);
  }
  if (rangeEnd < totalPageCount - 1) pages.push('...');

  // Sort and return the page numbers
  return pages.sort((a, b) => {
    // Sort numerically, but keep ellipsis in their positions
    if (a === '...') {
      // Find the number before this ellipsis
      const idx = pages.indexOf(a);
      const prev = pages[idx - 1];
      return prev + 0.5;
    }
    if (b === '...') {
      // Find the number before this ellipsis
      const idx = pages.indexOf(b);
      const prev = pages[idx - 1];
      return prev + 0.5;
    }
    return a - b;
  });
});

// Delete vehicle actions
const confirmDelete = (vehicle) => { vehicleToDelete.value = vehicle; showDeleteModal.value = true; };
const cancelDelete = () => { showDeleteModal.value = false; vehicleToDelete.value = null; };
const deleteVehicle = async () => {
  if (!vehicleToDelete.value || isDeleting.value) return;

  isDeleting.value = true;
  console.log(`Attempting to delete vehicle ID: ${vehicleToDelete.value.id}`);

  try {
    await vehicleStore.deleteVehicle(vehicleToDelete.value.id); // Call store action
    console.log(`Vehicle "${vehicleToDelete.value.title}" deleted successfully.`);
    // No need to manually filter, store should update reactively.
    // If not reactive, uncomment the line below or call fetchInventoryData() again.
    // vehicles.value = vehicles.value.filter(v => v.id !== vehicleToDelete.value.id);
  } catch (error) {
    console.error("Failed to delete vehicle:", error);
    alert(`Failed to delete vehicle: ${error.message || 'Unknown error'}`);
  } finally {
    isDeleting.value = false;
    cancelDelete(); // Close modal regardless of success/failure
  }
};

// Preview modal actions
const showVehiclePreview = (vehicle) => { selectedVehicle.value = vehicle; showPreviewModal.value = true; };
const closePreviewModal = () => { showPreviewModal.value = false; setTimeout(() => { selectedVehicle.value = null; }, 300); }; // Delay clearing data for fade out

// Navigation actions
const editVehicle = (id) => { showPreviewModal.value = false; router.push(`/admin/vehicles/edit/${id}`); };
const openAiStudio = (id) => { openingAiStudio.value = true; showPreviewModal.value = false; router.push(`/admin/vehicles/ai-studio/${id}`); };
const addVehicle = () => { router.push('/admin/vehicles/add'); };
const importInventory = () => { router.push('/admin/inventory/import'); };

// Get CSS class based on vehicle status
const getStatusClass = (status) => {
    const lowerStatus = status?.toLowerCase() || 'unknown';
    switch (lowerStatus) {
        case 'available': return 'bg-green-100 text-green-800';
        case 'sold': return 'bg-red-100 text-red-800';
        case 'pending': return 'bg-yellow-100 text-yellow-800';
        case 'draft': return 'bg-blue-100 text-blue-800';
        default: return 'bg-gray-100 text-gray-800';
    }
};
// Get display text for vehicle status
const getStatusText = (status) => status || 'Unknown';

// --- Financing Logic ---
// Save interest rates to Supabase via settingsService
const saveInterestRates = async () => {
  isSavingFinancing.value = true;
  saveSuccessFinancing.value = false;
  saveErrorFinancing.value = false;
  errorMessageFinancing.value = '';
  console.log("Attempting to save interest rates:", JSON.parse(JSON.stringify(interestRates)));

  try {
    // Basic Validation before sending to service
    for (const tier of Object.keys(interestRates)) {
      for (const term of termLengths) {
        const rate = parseFloat(interestRates[tier][term]);
        if (isNaN(rate) || rate < 0) {
          // Convert potentially non-numeric input back to a valid number (e.g., 0) or throw error
           interestRates[tier][term] = 0; // Or handle as error
           console.warn(`Invalid rate input for ${tier} tier, ${term} month term. Resetting to 0.`);
           // throw new Error(`Invalid rate for ${tier} tier, ${term} month term. Please enter a valid number.`);
        } else {
             interestRates[tier][term] = rate; // Ensure it's stored as a number
        }
      }
    }

    // Call the service function to update the database
    const success = await updateInterestRates(interestRates);

    if (success) {
      console.log("Interest rates saved successfully.");
      saveSuccessFinancing.value = true;
      setTimeout(() => { saveSuccessFinancing.value = false; }, 3000); // Hide success message after 3s
    } else {
      // The service function should ideally throw an error on failure,
      // but handle the boolean return case as well.
      throw new Error("Failed to update interest rates in the database (service returned false).");
    }
  } catch (error) {
    console.error("Error saving interest rates:", error);
    errorMessageFinancing.value = error.message || "An error occurred while saving interest rates.";
    saveErrorFinancing.value = true;
    // Optionally auto-hide error message
    // setTimeout(() => { saveErrorFinancing.value = false; }, 5000);
  } finally {
    isSavingFinancing.value = false;
  }
};

// Format rate for display in placeholder (or if needed elsewhere)
const formatRate = (rate) => {
    const num = parseFloat(rate);
    return isNaN(num) ? '0.00' : num.toFixed(2);
};

// Save financing FAQs to Supabase via settingsService
const saveFinancingFaqs = async () => {
  isSavingFaqs.value = true;
  saveSuccessFaqs.value = false;
  saveErrorFaqs.value = false;
  errorMessageFaqs.value = '';
  console.log("Attempting to save financing FAQs:", JSON.parse(JSON.stringify(financingFaqs.value)));

  try {
    // Basic validation before sending to service
    // Remove empty FAQs
    const filteredFaqs = financingFaqs.value.filter(faq =>
      faq.question.trim() !== '' && faq.answer.trim() !== ''
    );

    if (filteredFaqs.length === 0) {
      throw new Error("At least one FAQ with both question and answer is required.");
    }

    // Call the service function to update the database
    const success = await updateFinancingFaqs(filteredFaqs);

    if (success) {
      console.log("Financing FAQs saved successfully.");
      saveSuccessFaqs.value = true;

      // Update the local state with the filtered FAQs
      financingFaqs.value = filteredFaqs;

      setTimeout(() => { saveSuccessFaqs.value = false; }, 3000); // Hide success message after 3s
    } else {
      throw new Error("Failed to update financing FAQs in the database (service returned false).");
    }
  } catch (error) {
    console.error("Error saving financing FAQs:", error);
    errorMessageFaqs.value = error.message || "An error occurred while saving financing FAQs.";
    saveErrorFaqs.value = true;
  } finally {
    isSavingFaqs.value = false;
  }
};

// Save financing options to Supabase via settingsService
const saveFinancingOptions = async () => {
  isSavingFinancingOptions.value = true;
  saveSuccessFinancingOptions.value = false;
  saveErrorFinancingOptions.value = false;
  errorMessageFinancingOptions.value = '';
  console.log("Attempting to save financing options:", JSON.parse(JSON.stringify(financingOptionsContent)));

  try {
    // Basic validation
    if (!financingOptionsContent.title.trim()) {
      throw new Error("Title is required for financing options section.");
    }

    if (!financingOptionsContent.description.trim()) {
      throw new Error("Description is required for financing options section.");
    }

    if (!financingOptionsContent.options.length) {
      throw new Error("At least one financing option is required.");
    }

    // Validate each option
    for (const option of financingOptionsContent.options) {
      if (!option.title.trim()) {
        throw new Error("All financing options must have a title.");
      }
      if (!option.description.trim()) {
        throw new Error("All financing options must have a description.");
      }
    }

    // Call the service function to update the database
    const success = await updateFinancingOptions(financingOptionsContent);

    // Also save to localStorage as a backup
    localStorage.setItem('financingOptionsContent', JSON.stringify(financingOptionsContent));

    if (success) {
      console.log("Financing options saved successfully to Supabase.");
      saveSuccessFinancingOptions.value = true;
      setTimeout(() => { saveSuccessFinancingOptions.value = false; }, 3000); // Hide success message after 3s
    } else {
      throw new Error("Failed to update financing options in the database (service returned false).");
    }
  } catch (error) {
    console.error("Error saving financing options:", error);
    errorMessageFinancingOptions.value = error.message || "An error occurred while saving financing options.";
    saveErrorFinancingOptions.value = true;
  } finally {
    isSavingFinancingOptions.value = false;
  }
};

// Save financing process to Supabase via settingsService
const saveFinancingProcess = async () => {
  isSavingFinancingProcess.value = true;
  saveSuccessFinancingProcess.value = false;
  saveErrorFinancingProcess.value = false;
  errorMessageFinancingProcess.value = '';
  console.log("Attempting to save financing process:", JSON.parse(JSON.stringify(financingProcessContent)));

  try {
    // Basic validation
    if (!financingProcessContent.title.trim()) {
      throw new Error("Title is required for financing process section.");
    }

    if (!financingProcessContent.description.trim()) {
      throw new Error("Description is required for financing process section.");
    }

    if (!financingProcessContent.steps.length) {
      throw new Error("At least one financing process step is required.");
    }

    // Validate each step
    for (const step of financingProcessContent.steps) {
      if (!step.title.trim()) {
        throw new Error("All financing process steps must have a title.");
      }
      if (!step.description.trim()) {
        throw new Error("All financing process steps must have a description.");
      }
    }

    // Call the service function to update the database
    const success = await updateFinancingProcess(financingProcessContent);

    // Also save to localStorage as a backup
    localStorage.setItem('financingProcessContent', JSON.stringify(financingProcessContent));

    if (success) {
      console.log("Financing process saved successfully to Supabase.");
      saveSuccessFinancingProcess.value = true;
      setTimeout(() => { saveSuccessFinancingProcess.value = false; }, 3000); // Hide success message after 3s
    } else {
      throw new Error("Failed to update financing process in the database (service returned false).");
    }
  } catch (error) {
    console.error("Error saving financing process:", error);
    errorMessageFinancingProcess.value = error.message || "An error occurred while saving financing process.";
    saveErrorFinancingProcess.value = true;
  } finally {
    isSavingFinancingProcess.value = false;
  }
};

// --- Pre-Qualification Logic ---
// Format date for display
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Sort pre-qualification data
const sortPreQualifications = (key) => {
  if (preQualificationSortKey.value === key) {
    // Toggle direction if clicking the same column
    preQualificationSortDir.value = preQualificationSortDir.value === 'asc' ? 'desc' : 'asc';
  } else {
    // Set new sort key and default to descending for dates, ascending for text
    preQualificationSortKey.value = key;
    preQualificationSortDir.value = key === 'created_at' ? 'desc' : 'asc';
  }
};

// Computed property for sorted pre-qualification data
const sortedPreQualifications = computed(() => {
  if (!preQualifications.value || preQualifications.value.length === 0) {
    return [];
  }

  const sorted = [...preQualifications.value];

  sorted.sort((a, b) => {
    let valA = a[preQualificationSortKey.value];
    let valB = b[preQualificationSortKey.value];

    // Handle dates
    if (preQualificationSortKey.value === 'created_at') {
      valA = new Date(valA || 0).getTime();
      valB = new Date(valB || 0).getTime();
    }
    // Handle strings
    else if (typeof valA === 'string' && typeof valB === 'string') {
      valA = valA.toLowerCase();
      valB = valB.toLowerCase();
    }

    // Default values if undefined
    valA = valA ?? '';
    valB = valB ?? '';

    // Compare values
    let comparison = 0;
    if (valA < valB) comparison = -1;
    else if (valA > valB) comparison = 1;

    // Apply sort direction
    return preQualificationSortDir.value === 'asc' ? comparison : comparison * -1;
  });

  return sorted;
});


// --- Detailing Logic ---
// Save detailing settings to Supabase via settingsService
const saveDetailingPrices = async () => {
  isSavingDetailing.value = true;
  saveSuccessDetailing.value = false;
  saveErrorDetailing.value = false;
  errorMessageDetailing.value = '';

  // Create pricing data object (direct structure for backward compatibility)
  const pricingData = {};

  console.log("Preparing to save detailing settings");

  try {
    // Process and validate pricing data
    for (const category of Object.keys(detailingPrices)) {
      pricingData[category] = {};
      for (const service of Object.keys(detailingPrices[category])) {
        const price = parseFloat(detailingPrices[category][service]);
        if (isNaN(price) || price < 0) {
          pricingData[category][service] = 0; // Reset invalid input to 0
          console.warn(`Invalid price input for ${category}, ${service}. Resetting to 0.`);
        } else {
          pricingData[category][service] = price; // Ensure stored as number
        }
      }
    }

    // Create a complete detailing settings object with both pricing structures
    // This ensures compatibility with both old and new code
    const detailingSettings = {
      ...pricingData, // Include direct pricing data for backward compatibility
      pricing: pricingData, // Include nested pricing data for new structure
      whyChooseUs: { ...whyChooseUsContent },
      detailingProcess: { ...detailingProcessContent }
    };

    console.log("Attempting to save detailing settings:", JSON.parse(JSON.stringify(detailingSettings)));

    // Call the service function to update the database
    const success = await updateDetailingPrices(detailingSettings);

    if (success) {
      console.log("Detailing settings saved successfully.");
      saveSuccessDetailing.value = true;
      setTimeout(() => { saveSuccessDetailing.value = false; }, 3000); // Hide success message
    } else {
      throw new Error("Failed to update detailing settings in the database (service returned false).");
    }
  } catch (error) {
    console.error("Error saving detailing settings:", error);
    errorMessageDetailing.value = error.message || "An error occurred while saving detailing settings.";
    saveErrorDetailing.value = true;
    // Optionally auto-hide error
    // setTimeout(() => { saveErrorDetailing.value = false; }, 5000);
  } finally {
    isSavingDetailing.value = false;
  }
};

// Format price for display in placeholder
const formatDetailPrice = (price) => {
    const num = parseFloat(price);
    return isNaN(num) ? '0.00' : num.toFixed(2);
};

// --- Business Information Logic ---
// Save business information to Supabase via settingsService
const saveBusinessInfo = async () => {
  isSavingBusiness.value = true;
  saveSuccessBusiness.value = false;
  saveErrorBusiness.value = false;
  errorMessageBusiness.value = '';
  console.log("Attempting to save business information:", JSON.parse(JSON.stringify(businessInfo)));

  try {
    // Basic validation before sending to service
    // Validate required fields (example)
    if (!businessInfo.about?.title?.trim()) {
      throw new Error("Business title in 'About' section is required.");
    }
    if (!businessInfo.contact?.address?.trim()) {
      throw new Error("Business address in 'Contact' section is required.");
    }
    if (!businessInfo.contact?.phone?.trim()) {
      throw new Error("Business phone number in 'Contact' section is required.");
    }
    if (!businessInfo.contact?.email?.trim()) {
      throw new Error("Business email in 'Contact' section is required.");
    }
    // Add more specific validation as needed (e.g., email format, phone format, hours format)

    // Call the service function to update the database
    const success = await updateBusinessInfo(businessInfo);

    if (success) {
      console.log("Business information saved successfully.");
      saveSuccessBusiness.value = true;
      setTimeout(() => { saveSuccessBusiness.value = false; }, 3000); // Hide success message after 3s
    } else {
      throw new Error("Failed to update business information in the database (service returned false).");
    }
  } catch (error) {
    console.error("Error saving business information:", error);
    errorMessageBusiness.value = error.message || "An error occurred while saving business information.";
    saveErrorBusiness.value = true;
  } finally {
    isSavingBusiness.value = false;
  }
};

// Add a new review
const addReview = () => {
  const newId = businessInfo.reviews.length > 0
    ? Math.max(0, ...businessInfo.reviews.map(r => r.id || 0)) + 1 // Ensure IDs are numbers and handle empty array
    : 1;

  businessInfo.reviews.push({
    id: newId,
    author: '',
    vehicle: '',
    rating: 5, // Default rating
    text: ''
  });
};


// Remove a review
const removeReview = (id) => {
  const index = businessInfo.reviews.findIndex(r => r.id === id);
  if (index !== -1) {
    businessInfo.reviews.splice(index, 1);
  } else {
      console.warn(`Attempted to remove review with non-existent ID: ${id}`);
  }
};

// Add a new service
const addService = () => {
  const newId = businessInfo.services.length > 0
    ? Math.max(0, ...businessInfo.services.map(s => s.id || 0)) + 1 // Ensure IDs are numbers and handle empty array
    : 1;

  businessInfo.services.push({
    id: newId,
    title: '',
    description: '',
    icon: '' // Default icon or leave empty
  });
};

// Remove a service
const removeService = (id) => {
  const index = businessInfo.services.findIndex(s => s.id === id);
  if (index !== -1) {
    businessInfo.services.splice(index, 1);
  } else {
       console.warn(`Attempted to remove service with non-existent ID: ${id}`);
  }
};

</script>

<template>
  <div class="min-h-screen bg-gray-100">
    <AdminNavbar />

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-20">
      <!-- Initial Loading State for the whole page -->
      <div v-if="isLoading" class="text-center py-10">
        <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-24 w-24 mx-auto mb-3" />
        <p class="text-gray-500">Loading Dashboard...</p>
      </div>

      <div v-else>
        <!-- Tab Navigation -->
        <div class="mb-6 border-b border-gray-200">
          <nav class="-mb-px flex space-x-8 overflow-x-auto" aria-label="Tabs">
            <button
              @click="activeTab = 'inventory'"
              :class="[
                activeTab === 'inventory'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm transition-colors duration-150 ease-in-out',
                activeTab !== 'inventory' ? 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 rounded-t' : ''
              ]"
            >
              Inventory Management
            </button>
            <button
              @click="activeTab = 'financing'"
              :class="[
                activeTab === 'financing'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm transition-colors duration-150 ease-in-out',
                activeTab !== 'financing' ? 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 rounded-t' : ''
              ]"
            >
              Financing Settings
            </button>
            <button
              @click="activeTab = 'detailing'"
              :class="[
                activeTab === 'detailing'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm transition-colors duration-150 ease-in-out',
                activeTab !== 'detailing' ? 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 rounded-t' : ''
              ]"
            >
              Detailing Settings
            </button>
            <!-- NEW: Business Information Tab Button -->
            <button
              @click="activeTab = 'business'"
              :class="[
                activeTab === 'business'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm transition-colors duration-150 ease-in-out',
                activeTab !== 'business' ? 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 rounded-t' : ''
              ]"
            >
              Business Information
            </button>
            <!-- NEW: Users Tab Button -->
            <button
              @click="activeTab = 'users'"
              :class="[
                activeTab === 'users'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm transition-colors duration-150 ease-in-out',
                activeTab !== 'users' ? 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 rounded-t' : ''
              ]"
            >
              Users & Permissions
            </button>

            <!-- NEW: Styling Tab Button -->
            <button
              @click="activeTab = 'styling'"
              :class="[
                activeTab === 'styling'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm transition-colors duration-150 ease-in-out',
                activeTab !== 'styling' ? 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 rounded-t' : ''
              ]"
            >
              Site Styling
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div>
          <!-- Inventory Tab Content -->
          <div v-show="activeTab === 'inventory'">
            <!-- Dashboard Header -->
            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
              <h2 class="text-2xl font-bold text-gray-900">
                Vehicle Inventory
                <span v-if="!isTableLoading && (vehicleStore.totalVehicles.value > 0 || sortedVehicles.length > 0)" class="text-lg font-normal text-gray-600">
                  ({{ vehicleStore.totalVehicles.value > 0 ? vehicleStore.totalVehicles.value : sortedVehicles.length }})
                </span>
              </h2>
              <div class="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
                <!-- Search Input -->
                 <div class="relative">
                    <input type="text" v-model="searchQuery" placeholder="Search..." aria-label="Search vehicles" class="w-full sm:w-64 px-4 py-2 border border-gray-400 rounded-md focus:outline-none focus:ring-primary focus:border-primary"/>
                 </div>
                <!-- Import Button -->
                 <button @click="importInventory" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" title="Import inventory">
                   <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"> <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" /> </svg> Import
                 </button>
                 <!-- Export to CarPages Button -->
                 <button @click="showCarPagesExport = !showCarPagesExport" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" title="Export to CarPages">
                   <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"> <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /> </svg> Export
                </button>
                <!-- Add Vehicle Button -->
                <button @click="addVehicle" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /> </svg> Add Vehicle
                </button>
              </div>
            </div>

            <!-- Vehicles Table -->
            <div class="shadow overflow-hidden sm:rounded-lg mb-6">
              <div class="overflow-x-auto relative">
                 <!-- Removed the absolute overlay loading indicator -->
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="sortBy('stockNumber')">Stock #<span v-if="sortKey === 'stockNumber'" class="ml-1">{{ sortDir === 'asc' ? '▲' : '▼' }}</span></th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="sortBy('status')">Status<span v-if="sortKey === 'status'" class="ml-1">{{ sortDir === 'asc' ? '▲' : '▼' }}</span></th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="sortBy('price')">Price<span v-if="sortKey === 'price'" class="ml-1">{{ sortDir === 'asc' ? '▲' : '▼' }}</span></th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="sortBy('year')">Year<span v-if="sortKey === 'year'" class="ml-1">{{ sortDir === 'asc' ? '▲' : '▼' }}</span></th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" @click="sortBy('mileage')">Mileage<span v-if="sortKey === 'mileage'" class="ml-1">{{ sortDir === 'asc' ? '▲' : '▼' }}</span></th>
                      <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200">
                     <!-- MODIFIED: Loading Row -->
                    <tr v-if="isTableLoading">
                        <td colspan="7" class="px-6 py-16 text-center text-gray-500">
                            <div class="flex flex-col items-center justify-center">
                                <img src="/REDGTWHEEL.png" alt="Loading Inventory..." class="animate-spin h-16 w-16 mb-3" />
                                <p>Loading inventory...</p>
                            </div>
                        </td>
                    </tr>

                    <!-- MODIFIED: Existing Vehicle Rows (only show when NOT loading and vehicles exist) -->
                    <template v-if="!isTableLoading && paginatedVehicles.length > 0">
                      <!-- Debug: Log the first vehicle data -->
                      <div v-if="paginatedVehicles.length > 0" class="hidden">
                        {{ console.log('[Vehicle Data Debug]', paginatedVehicles[0]) }}
                      </div>
                      <tr v-for="vehicle in paginatedVehicles" :key="vehicle.id" class="hover:bg-gray-50 cursor-pointer transition duration-150 ease-in-out" @click="showVehiclePreview(vehicle)">
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10" style="position: relative; z-index: 10;">
                              <!-- Display actual vehicle image -->
                              <img
                                :src="getVehicleImageUrl(vehicle)"
                                :alt="vehicle.title"
                                class="h-10 w-10 rounded-md object-cover"
                                onerror="this.onerror=null; this.src='/REDGTWHEEL.png';"
                              />
                            </div>
                            <div class="ml-4">
                              <div class="text-sm font-medium text-gray-900 group relative"> <div class="max-w-[200px] truncate" :title="vehicle.title"> {{ vehicle.title }} </div> </div>
                              <div class="text-sm text-gray-500 truncate max-w-[200px]"> {{ vehicle.make }} {{ vehicle.model }} </div>
                            </div>
                          </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 relative"> <div class="max-w-[100px] truncate" :title="vehicle.stockNumber || 'N/A'"> {{ vehicle.stockNumber || 'N/A' }} </div> </td>
                        <td class="px-6 py-4 whitespace-nowrap"> <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="getStatusClass(vehicle.status)"> {{ getStatusText(vehicle.status) }} </span> </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-gray-900">{{ formatInvPrice(vehicle.price) }}</div>
                          <div v-if="vehicle.specialPrice && vehicle.specialPrice < vehicle.price" class="text-sm text-green-600 font-medium"> {{ formatInvPrice(vehicle.specialPrice) }} (Special) </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"> {{ vehicle.year || 'N/A' }} </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"> {{ formatMileage(vehicle.mileage) }} </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" @click.stop>
                          <button @click="editVehicle(vehicle.id)" class="text-primary hover:text-primary-dark mr-3 transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary rounded" title="Edit"> Edit </button>
                          <button @click="confirmDelete(vehicle)" class="text-red-600 hover:text-red-800 transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-red-500 rounded" title="Delete"> Delete </button>
                        </td>
                      </tr>
                    </template>

                    <!-- MODIFIED: Existing Empty/No Results Row (only show when NOT loading and NO vehicles exist) -->
                    <tr v-if="!isTableLoading && paginatedVehicles.length === 0">
                      <td colspan="7" class="px-6 py-10 text-center text-gray-500">
                         <div v-if="debouncedSearchQuery">
                            <p class="text-lg font-medium">No vehicles match your search</p>
                            <p class="mt-1 text-sm">Try adjusting your search terms.</p>
                         </div>
                         <div v-else>
                            <p class="text-lg font-medium">Inventory is empty</p>
                            <p class="mt-1 text-sm">Click "Add Vehicle" to get started.</p>
                         </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Enhanced Pagination Controls -->
            <nav v-if="!isTableLoading && sortedVehicles.length > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-b-lg shadow" aria-label="Pagination">
              <!-- Pagination Info -->
              <div class="hidden sm:block">
                <p class="text-sm text-gray-700">{{ paginationInfo }}</p>
              </div>

              <!-- Pagination Controls -->
              <div class="flex-1 flex justify-between sm:justify-end items-center space-x-2">
                <!-- First Page Button -->
                <button
                  @click="goToPage(1)"
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition ease-in-out duration-150 focus:outline-none focus:ring-2 focus:ring-primary"
                  title="First Page"
                >
                  <span class="sr-only">First Page</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                  </svg>
                </button>

                <!-- Previous Button -->
                <button
                  @click="prevPage"
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition ease-in-out duration-150 focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <span class="sr-only">Previous</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  <span class="hidden sm:inline ml-1">Previous</span>
                </button>

                <!-- Page Numbers -->
                <div class="hidden md:flex space-x-1">
                  <template v-for="(page, index) in displayedPageNumbers" :key="index">
                    <!-- Ellipsis -->
                    <span v-if="page === '...'" class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700">
                      ...
                    </span>

                    <!-- Page Number Button -->
                    <button
                      v-else
                      @click="goToPage(page)"
                      :class="[
                        'relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-primary',
                        currentPage === page
                          ? 'z-10 bg-primary border-primary text-white'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      ]"
                    >
                      {{ page }}
                    </button>
                  </template>
                </div>

                <!-- Current Page / Total Pages (Mobile) -->
                <span class="md:hidden text-sm text-gray-700">
                  Page {{ currentPage }} of {{ totalPages }}
                </span>

                <!-- Next Button -->
                <button
                  @click="nextPage"
                  :disabled="currentPage === totalPages || totalPages <= 1"
                  class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition ease-in-out duration-150 focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <span class="hidden sm:inline mr-1">Next</span>
                  <span class="sr-only">Next</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>

                <!-- Last Page Button -->
                <button
                  @click="goToPage(totalPages)"
                  :disabled="currentPage === totalPages || totalPages <= 1"
                  class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition ease-in-out duration-150 focus:outline-none focus:ring-2 focus:ring-primary"
                  title="Last Page"
                >
                  <span class="sr-only">Last Page</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
              </div>
            </nav>
            
            <!-- CarPages Export Component -->
            <div v-if="showCarPagesExport" class="mt-8">
              <CarPagesExport />
            </div>
          </div>

          <!-- Financing Tab Content -->
          <div v-show="activeTab === 'financing'">
            <div v-if="isLoadingFinancing" class="text-center py-10">
                <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-16 w-16 mx-auto mb-3" />
                <p class="text-gray-500">Loading Financing Settings...</p>
            </div>
            <div v-else>
                 <!-- Page Header -->
                <div class="mb-6">
                  <h2 class="text-2xl font-bold text-gray-900">Financing Settings</h2>
                  <p class="mt-1 text-sm text-gray-600"> Manage interest rates for the financing calculator shown on the public financing page. </p>
                </div>

                <!-- Alert Messages -->
                <div v-if="saveSuccessFinancing" class="mb-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm" role="alert">
                    <p class="font-medium">Success!</p>
                    <p>Interest rates saved successfully.</p>
                </div>
                <div v-if="saveErrorFinancing" class="mb-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm" role="alert">
                     <p class="font-medium">Error</p>
                    <p>{{ errorMessageFinancing || 'An unknown error occurred.' }}</p>
                </div>

                <!-- Interest Rates Form -->
                <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
                    <!-- Section Header with Toggle -->
                    <div
                        class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
                        @click="toggleFinancingSection('rates')"
                    >
                        <div>
                            <h3 class="text-lg leading-6 font-semibold text-gray-900">Interest Rates by Credit Score Tier</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                Set the Annual Percentage Rate (APR).
                            </p>
                        </div>
                        <div class="text-gray-500">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-6 w-6 transition-transform duration-200"
                                :class="{'rotate-180': !financingExpandedSections.rates}"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>

                    <!-- Interest Rates Content -->
                    <div v-if="financingExpandedSections.rates" class="border-t border-gray-200">
                        <form @submit.prevent="saveInterestRates">
                            <div class="px-4 py-5 sm:p-0"> <!-- Remove padding for full width table -->
                                <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50 z-10"> Credit Score Tier </th>
                                        <th v-for="term in termLengths" :key="term" scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> {{ term }} Months </th>
                                    </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="tier in creditScoreTiers" :key="tier.id">
                                        <td class="px-6 py-4 whitespace-nowrap sticky left-0 bg-white z-10"> <div class="text-sm font-medium text-gray-900">{{ tier.label }}</div> </td>
                                        <td v-for="term in termLengths" :key="`${tier.id}-${term}`" class="px-6 py-4 whitespace-nowrap">
                                        <div class="relative rounded-md shadow-sm max-w-[120px]"> <!-- Limit width -->
                                            <input type="number" v-model.number="interestRates[tier.id][term]" step="0.01" min="0" max="100" required
                                                class="shadow-sm focus:ring-primary focus:border-primary block w-full pl-3 pr-8 sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200 appearance-none"
                                                :aria-label="`Interest rate for ${tier.label} at ${term} months`"
                                                :placeholder="formatRate(interestRates[tier.id][term])" />
                                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"> <span class="text-gray-500 sm:text-sm">%</span> </div>
                                        </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                </div>
                            </div>
                            <div class="px-4 py-3 bg-gray-50 text-right sm:px-6 border-t border-gray-200">
                                <button type="submit" :disabled="isSavingFinancing" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-opacity duration-200">
                                <svg v-if="isSavingFinancing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"> <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle> <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path> </svg>
                                {{ isSavingFinancing ? 'Saving...' : 'Save Interest Rates' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- FAQ Management Section -->
                <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8 mt-10">
                    <!-- Section Header with Toggle -->
                    <div
                        class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
                        @click="toggleFinancingSection('faq')"
                    >
                        <div>
                            <h3 class="text-lg leading-6 font-semibold text-gray-900">Frequently Asked Questions</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                Manage the FAQ questions and answers displayed on the financing page.
                            </p>
                        </div>
                        <div class="text-gray-500">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-6 w-6 transition-transform duration-200"
                                :class="{'rotate-180': !financingExpandedSections.faq}"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>

                    <!-- Alert Messages for FAQs -->
                    <div v-if="saveSuccessFaqs" class="mb-4 mt-4 mx-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm" role="alert">
                        <p class="font-medium">Success!</p>
                        <p>FAQ items saved successfully.</p>
                    </div>
                    <div v-if="saveErrorFaqs" class="mb-4 mt-4 mx-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm" role="alert">
                        <p class="font-medium">Error</p>
                        <p>{{ errorMessageFaqs || 'An unknown error occurred.' }}</p>
                    </div>

                    <!-- Loading State -->
                    <div v-if="isLoadingFaqs" class="border-t border-gray-200 px-4 py-5 sm:p-6 text-center">
                        <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-12 w-12 mx-auto mb-3" />
                        <p class="text-gray-500">Loading FAQ items...</p>
                    </div>

                    <!-- FAQ Content -->
                    <div v-else-if="financingExpandedSections.faq" class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <form @submit.prevent="saveFinancingFaqs">
                            <div class="space-y-6">
                                <!-- FAQ Items -->
                                <div v-for="(faq, index) in financingFaqs" :key="index" class="bg-gray-50 p-4 rounded-lg relative">
                                    <!-- Remove Button -->
                                    <button
                                        type="button"
                                        @click="financingFaqs.splice(index, 1)"
                                        class="absolute top-2 right-2 text-gray-400 hover:text-red-500 focus:outline-none"
                                        title="Remove FAQ"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>

                                    <!-- Question Field -->
                                    <div class="mb-4">
                                        <label :for="`faq-question-${index}`" class="block text-sm font-medium text-gray-700 mb-1">
                                            Question
                                        </label>
                                        <input
                                            :id="`faq-question-${index}`"
                                            v-model="faq.question"
                                            type="text"
                                            class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                                            placeholder="Enter question here"
                                        />
                                    </div>

                                    <!-- Answer Field -->
                                    <div>
                                        <label :for="`faq-answer-${index}`" class="block text-sm font-medium text-gray-700 mb-1">
                                            Answer
                                        </label>
                                        <textarea
                                            :id="`faq-answer-${index}`"
                                            v-model="faq.answer"
                                            rows="4"
                                            class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200"
                                            placeholder="Enter answer here"
                                        ></textarea>
                                    </div>
                                </div>

                                <!-- Add FAQ Button -->
                                <button
                                    type="button"
                                    @click="financingFaqs.push({ question: '', answer: '' })"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                    Add FAQ Item
                                </button>
                            </div>

                            <!-- Save Button -->
                            <div class="mt-6">
                                <button
                                    type="submit"
                                    :disabled="isSavingFaqs"
                                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-opacity duration-200"
                                >
                                    <svg v-if="isSavingFaqs" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    {{ isSavingFaqs ? 'Saving...' : 'Save FAQ Items' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Pre-Qualification Submissions Section -->
                <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8 mt-10">
                    <!-- Section Header with Toggle -->
                    <div
                        class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
                        @click="toggleFinancingSection('preQualifications')"
                    >
                        <div>
                            <h3 class="text-lg leading-6 font-semibold text-gray-900">Pre-Qualification Submissions</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                View and manage customer pre-qualification submissions from the financing page.
                            </p>
                        </div>
                        <div class="text-gray-500">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-6 w-6 transition-transform duration-200"
                                :class="{'rotate-180': !financingExpandedSections.preQualifications}"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>

                    <!-- Pre-Qualification Content -->
                    <div v-if="financingExpandedSections.preQualifications" class="border-t border-gray-200">
                        <!-- Loading State -->
                        <div v-if="isLoadingPreQualifications" class="p-6 text-center">
                            <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-12 w-12 mx-auto mb-3" />
                            <p class="text-gray-500">Loading pre-qualification submissions...</p>
                        </div>

                        <!-- Error State -->
                        <div v-else-if="preQualificationError" class="p-6">
                            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm" role="alert">
                                <p class="font-medium">Error</p>
                                <p>{{ preQualificationError }}</p>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div v-else-if="!sortedPreQualifications.length" class="p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <p class="text-gray-500 text-lg font-medium">No pre-qualification submissions yet</p>
                            <p class="text-gray-400 text-sm mt-1">Submissions from the financing page will appear here.</p>
                        </div>

                        <!-- Data Table -->
                        <div v-else>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col"
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                                @click="sortPreQualifications('created_at')">
                                                Date
                                                <span v-if="preQualificationSortKey === 'created_at'" class="ml-1">
                                                    {{ preQualificationSortDir === 'asc' ? '▲' : '▼' }}
                                                </span>
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                                @click="sortPreQualifications('first_name')">
                                                Name
                                                <span v-if="preQualificationSortKey === 'first_name'" class="ml-1">
                                                    {{ preQualificationSortDir === 'asc' ? '▲' : '▼' }}
                                                </span>
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                                @click="sortPreQualifications('email')">
                                                Email
                                                <span v-if="preQualificationSortKey === 'email'" class="ml-1">
                                                    {{ preQualificationSortDir === 'asc' ? '▲' : '▼' }}
                                                </span>
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                                @click="sortPreQualifications('phone_number')">
                                                Phone
                                                <span v-if="preQualificationSortKey === 'phone_number'" class="ml-1">
                                                    {{ preQualificationSortDir === 'asc' ? '▲' : '▼' }}
                                                </span>
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                                @click="sortPreQualifications('vehicle_of_interest')">
                                                Vehicle Interest
                                                <span v-if="preQualificationSortKey === 'vehicle_of_interest'" class="ml-1">
                                                    {{ preQualificationSortDir === 'asc' ? '▲' : '▼' }}
                                                </span>
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                                @click="sortPreQualifications('financing_type')">
                                                Type
                                                <span v-if="preQualificationSortKey === 'financing_type'" class="ml-1">
                                                    {{ preQualificationSortDir === 'asc' ? '▲' : '▼' }}
                                                </span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr v-for="submission in sortedPreQualifications" :key="submission.id" class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ formatDate(submission.created_at) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ submission.first_name }} {{ submission.last_name }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <a :href="`mailto:${submission.email}`" class="text-primary hover:text-primary-dark">
                                                    {{ submission.email }}
                                                </a>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <a :href="`tel:${submission.phone_number}`" class="text-gray-600 hover:text-gray-900">
                                                    {{ submission.phone_number }}
                                                </a>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ submission.vehicle_of_interest || 'Not specified' }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                                                      :class="submission.financing_type === 'Purchase' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'">
                                                    {{ submission.financing_type }}
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financing Options Section -->
                <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8 mt-10">
                    <!-- Section Header with Toggle -->
                    <div
                        class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
                        @click="toggleFinancingSection('financingOptions')"
                    >
                        <div>
                            <h3 class="text-lg leading-6 font-semibold text-gray-900">Financing Options</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                Customize the financing options displayed on the financing page.
                            </p>
                        </div>
                        <div class="text-gray-500">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-6 w-6 transition-transform duration-200"
                                :class="{'rotate-180': !financingExpandedSections.financingOptions}"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>

                    <!-- Alert Messages for Financing Options -->
                    <div v-if="saveSuccessFinancingOptions" class="mb-4 mt-4 mx-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm" role="alert">
                        <p class="font-medium">Success!</p>
                        <p>Financing options saved successfully.</p>
                    </div>
                    <div v-if="saveErrorFinancingOptions" class="mb-4 mt-4 mx-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm" role="alert">
                        <p class="font-medium">Error</p>
                        <p>{{ errorMessageFinancingOptions || 'An unknown error occurred.' }}</p>
                    </div>

                    <!-- Loading State -->
                    <div v-if="isLoadingFinancingOptions" class="border-t border-gray-200 px-4 py-5 sm:p-6 text-center">
                        <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-12 w-12 mx-auto mb-3" />
                        <p class="text-gray-500">Loading financing options...</p>
                    </div>

                    <!-- Financing Options Content -->
                    <div v-else-if="financingExpandedSections.financingOptions" class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <form @submit.prevent="saveFinancingOptions">
                            <!-- Section Title and Description -->
                            <div class="mb-6">
                                <label for="financingOptionsTitle" class="block text-sm font-medium text-gray-700 mb-1">Section Title</label>
                                <input
                                    id="financingOptionsTitle"
                                    type="text"
                                    v-model="financingOptionsContent.title"
                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Enter section title"
                                />
                            </div>
                            <div class="mb-6">
                                <label for="financingOptionsDescription" class="block text-sm font-medium text-gray-700 mb-1">Section Description</label>
                                <textarea
                                    id="financingOptionsDescription"
                                    v-model="financingOptionsContent.description"
                                    rows="3"
                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Enter section description"
                                ></textarea>
                            </div>

                            <!-- Financing Options -->
                            <div class="mb-6">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Financing Options</h4>
                                <div class="space-y-6">
                                    <div v-for="(option, index) in financingOptionsContent.options" :key="index" class="bg-white p-4 rounded-md shadow-sm border border-gray-200">
                                        <div class="flex justify-between items-start mb-4">
                                            <h5 class="text-sm font-medium text-gray-900">Option {{ index + 1 }}: {{ option.title || 'Untitled Option' }}</h5>
                                            <button
                                                @click="financingOptionsContent.options.splice(index, 1)"
                                                type="button"
                                                class="text-red-600 hover:text-red-800 focus:outline-none"
                                                :disabled="financingOptionsContent.options.length <= 1"
                                                :class="{'opacity-50 cursor-not-allowed': financingOptionsContent.options.length <= 1}"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="grid grid-cols-1 gap-4">
                                            <div>
                                                <label :for="`option-title-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                                                <input
                                                    :id="`option-title-${index}`"
                                                    type="text"
                                                    v-model="option.title"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="Option title"
                                                />
                                            </div>
                                            <div>
                                                <label :for="`option-description-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                                <textarea
                                                    :id="`option-description-${index}`"
                                                    v-model="option.description"
                                                    rows="2"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="Option description"
                                                ></textarea>
                                            </div>
                                            <div>
                                                <label :for="`option-image-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Image Path</label>
                                                <input
                                                    :id="`option-image-${index}`"
                                                    type="text"
                                                    v-model="option.image"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="/path/to/image.jpg"
                                                />
                                                <p class="mt-1 text-xs text-gray-500">Enter the path to the image file (e.g., /STANDARDFINANCING.png)</p>
                                            </div>
                                            <div>
                                                <label :for="`option-features-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Features</label>
                                                <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                                                    <div v-for="(feature, featureIndex) in option.features" :key="`feature-${index}-${featureIndex}`" class="flex items-center mb-2">
                                                        <input
                                                            :id="`option-feature-${index}-${featureIndex}`"
                                                            type="text"
                                                            v-model="option.features[featureIndex]"
                                                            class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                            placeholder="Feature point"
                                                        />
                                                        <button
                                                            @click="option.features.splice(featureIndex, 1)"
                                                            type="button"
                                                            class="ml-2 text-red-600 hover:text-red-800 focus:outline-none"
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                    <button
                                                        type="button"
                                                        @click="option.features.push('')"
                                                        class="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                                        </svg>
                                                        Add Feature
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button
                                    type="button"
                                    @click="financingOptionsContent.options.push({
                                        title: '',
                                        description: '',
                                        image: '',
                                        features: ['', '', '', '']
                                    })"
                                    class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                    Add Financing Option
                                </button>
                            </div>

                            <!-- Save Button -->
                            <div class="mt-6">
                                <button
                                    type="submit"
                                    :disabled="isSavingFinancingOptions"
                                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-opacity duration-200"
                                >
                                    <svg v-if="isSavingFinancingOptions" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    {{ isSavingFinancingOptions ? 'Saving...' : 'Save Financing Options' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Financing Process Section -->
                <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8 mt-10">
                    <!-- Section Header with Toggle -->
                    <div
                        class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
                        @click="toggleFinancingSection('financingProcess')"
                    >
                        <div>
                            <h3 class="text-lg leading-6 font-semibold text-gray-900">Financing Process</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                Customize the financing process steps displayed on the financing page.
                            </p>
                        </div>
                        <div class="text-gray-500">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-6 w-6 transition-transform duration-200"
                                :class="{'rotate-180': !financingExpandedSections.financingProcess}"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>

                    <!-- Alert Messages for Financing Process -->
                    <div v-if="saveSuccessFinancingProcess" class="mb-4 mt-4 mx-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm" role="alert">
                        <p class="font-medium">Success!</p>
                        <p>Financing process saved successfully.</p>
                    </div>
                    <div v-if="saveErrorFinancingProcess" class="mb-4 mt-4 mx-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm" role="alert">
                        <p class="font-medium">Error</p>
                        <p>{{ errorMessageFinancingProcess || 'An unknown error occurred.' }}</p>
                    </div>

                    <!-- Loading State -->
                    <div v-if="isLoadingFinancingProcess" class="border-t border-gray-200 px-4 py-5 sm:p-6 text-center">
                        <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-12 w-12 mx-auto mb-3" />
                        <p class="text-gray-500">Loading financing process...</p>
                    </div>

                    <!-- Financing Process Content -->
                    <div v-else-if="financingExpandedSections.financingProcess" class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <form @submit.prevent="saveFinancingProcess">
                            <!-- Section Title and Description -->
                            <div class="mb-6">
                                <label for="financingProcessTitle" class="block text-sm font-medium text-gray-700 mb-1">Section Title</label>
                                <input
                                    id="financingProcessTitle"
                                    type="text"
                                    v-model="financingProcessContent.title"
                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Enter section title"
                                />
                            </div>
                            <div class="mb-6">
                                <label for="financingProcessDescription" class="block text-sm font-medium text-gray-700 mb-1">Section Description</label>
                                <textarea
                                    id="financingProcessDescription"
                                    v-model="financingProcessContent.description"
                                    rows="3"
                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Enter section description"
                                ></textarea>
                            </div>

                            <!-- Process Steps -->
                            <div class="mb-6">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Process Steps</h4>
                                <div class="space-y-6">
                                    <div v-for="(step, index) in financingProcessContent.steps" :key="index" class="bg-white p-4 rounded-md shadow-sm border border-gray-200">
                                        <div class="flex justify-between items-start mb-4">
                                            <h5 class="text-sm font-medium text-gray-900">Step {{ index + 1 }}: {{ step.title || 'Untitled Step' }}</h5>
                                            <button
                                                @click="financingProcessContent.steps.splice(index, 1)"
                                                type="button"
                                                class="text-red-600 hover:text-red-800 focus:outline-none"
                                                :disabled="financingProcessContent.steps.length <= 1"
                                                :class="{'opacity-50 cursor-not-allowed': financingProcessContent.steps.length <= 1}"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="grid grid-cols-1 gap-4">
                                            <div>
                                                <label :for="`step-title-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                                                <input
                                                    :id="`step-title-${index}`"
                                                    type="text"
                                                    v-model="step.title"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="Step title"
                                                />
                                            </div>
                                            <div>
                                                <label :for="`step-description-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                                <textarea
                                                    :id="`step-description-${index}`"
                                                    v-model="step.description"
                                                    rows="2"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="Step description"
                                                ></textarea>
                                            </div>
                                            <div>
                                                <label :for="`step-icon-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Icon SVG Path</label>
                                                <input
                                                    :id="`step-icon-${index}`"
                                                    type="text"
                                                    v-model="step.icon"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="SVG path data"
                                                />
                                                <p class="mt-1 text-xs text-gray-500">Enter the SVG path data for the icon (d attribute)</p>
                                            </div>
                                            <div>
                                                <label :for="`step-details-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Expanded Details</label>
                                                <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                                                    <div v-for="(detail, detailIndex) in step.expandedDetails" :key="`detail-${index}-${detailIndex}`" class="flex items-center mb-2">
                                                        <input
                                                            :id="`step-detail-${index}-${detailIndex}`"
                                                            type="text"
                                                            v-model="step.expandedDetails[detailIndex]"
                                                            class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                            placeholder="Detail point"
                                                        />
                                                        <button
                                                            @click="step.expandedDetails.splice(detailIndex, 1)"
                                                            type="button"
                                                            class="ml-2 text-red-600 hover:text-red-800 focus:outline-none"
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                    <button
                                                        type="button"
                                                        @click="step.expandedDetails.push('')"
                                                        class="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                                        </svg>
                                                        Add Detail
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button
                                    type="button"
                                    @click="financingProcessContent.steps.push({
                                        title: '',
                                        description: '',
                                        icon: '',
                                        expandedDetails: ['', '', '', '']
                                    })"
                                    class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                    Add Process Step
                                </button>
                            </div>

                            <!-- Save Button -->
                            <div class="mt-6">
                                <button
                                    type="submit"
                                    :disabled="isSavingFinancingProcess"
                                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-opacity duration-200"
                                >
                                    <svg v-if="isSavingFinancingProcess" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    {{ isSavingFinancingProcess ? 'Saving...' : 'Save Financing Process' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
          </div>

          <!-- Detailing Tab Content -->
          <div v-show="activeTab === 'detailing'">
             <div v-if="isLoadingDetailing" class="text-center py-10">
                <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-16 w-16 mx-auto mb-3" />
                <p class="text-gray-500">Loading Detailing Settings...</p>
            </div>
            <div v-else>
                <!-- Page Header -->
                <div class="mb-6">
                  <h2 class="text-2xl font-bold text-gray-900">Detailing Settings</h2>
                  <p class="mt-1 text-sm text-gray-600"> Manage prices for detailing services shown on the public detailing page. </p>
                </div>

                <!-- Alert Messages -->
                 <div v-if="saveSuccessDetailing" class="mb-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm" role="alert">
                    <p class="font-medium">Success!</p>
                    <p>Detailing prices saved successfully.</p>
                </div>
                <div v-if="saveErrorDetailing" class="mb-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm" role="alert">
                    <p class="font-medium">Error</p>
                    <p>{{ errorMessageDetailing || 'An unknown error occurred.' }}</p>
                </div>

                <form @submit.prevent="saveDetailingPrices">
                    <!-- Detailing Pricing Section -->
                    <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
                        <!-- Section Header with Toggle -->
                        <div
                            class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
                            @click="detailingExpandedSections.pricing = !detailingExpandedSections.pricing"
                        >
                            <div>
                                <h3 class="text-lg leading-6 font-semibold text-gray-900">Detailing Pricing</h3>
                                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                    Set detailing prices for all vehicle types
                                </p>
                            </div>
                            <div class="text-gray-500">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-6 w-6 transition-transform duration-200"
                                    :class="{'rotate-180': !detailingExpandedSections.pricing}"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </div>
                        </div>

                        <!-- Pricing Content -->
                        <div v-if="detailingExpandedSections.pricing" class="border-t border-gray-200 px-0 py-6 bg-gray-50">
                            <!-- Standard Vehicles Header -->
                            <h4 class="text-base font-medium text-gray-900 mb-4 px-6">Standard Vehicles</h4>

                            <!-- Standard Vehicles Table -->
                            <div class="overflow-x-auto mb-8">
                                <table class="min-w-full divide-y divide-gray-200 bg-white">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50 z-10 border-b-2 border-gray-200">
                                                Vehicle Category
                                            </th>
                                            <th v-for="service in serviceTiers" :key="`header-${service.id}`" scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">
                                                {{ service.label }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <!-- Regular Vehicle Categories -->
                                        <tr v-for="(category, index) in vehicleCategories.filter(c => !c.specialPricing)" :key="category.id"
                                            :class="[
                                                'transition-colors duration-150',
                                                index % 2 === 0 ? 'bg-white hover:bg-gray-50' : 'bg-gray-50 hover:bg-gray-100'
                                            ]">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 sticky left-0 z-10"
                                                :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'">
                                                {{ category.label }}
                                            </td>
                                            <td v-for="service in serviceTiers" :key="`${category.id}-${service.id}`" class="px-6 py-4 whitespace-nowrap">
                                                <div class="relative rounded-md shadow-sm max-w-[150px] mx-auto">
                                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <span class="text-gray-500 sm:text-sm">$</span>
                                                    </div>
                                                    <input
                                                        :id="`${category.id}-${service.id}`"
                                                        type="number"
                                                        v-model.number="detailingPrices[category.id][service.id]"
                                                        step="0.01"
                                                        min="0"
                                                        required
                                                        class="focus:ring-primary focus:border-primary block w-full pl-8 pr-12 sm:text-sm border-gray-400 border-2 rounded-md appearance-none hover:border-gray-500 transition-colors duration-150"
                                                        :aria-label="`Price for ${service.label} on ${category.label}`"
                                                        :placeholder="formatDetailPrice(detailingPrices[category.id][service.id])"
                                                    />
                                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                        <span class="text-gray-500 sm:text-sm">CAD</span>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Special Vehicles Header -->
                            <h4 class="text-base font-medium text-gray-900 mb-2 px-6">Special Vehicles</h4>
                            <p class="text-sm text-gray-500 mb-4 px-6">Base prices displayed as price+</p>

                            <!-- Special Vehicles Table -->
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 bg-white">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50 z-10 border-b-2 border-gray-200">
                                                Vehicle Category
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">
                                                Base Price
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">
                                                Display Format
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr v-for="(category, index) in vehicleCategories.filter(c => c.specialPricing)" :key="category.id"
                                            :class="[
                                                'transition-colors duration-150',
                                                index % 2 === 0 ? 'bg-white hover:bg-gray-50' : 'bg-gray-50 hover:bg-gray-100'
                                            ]">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 sticky left-0 z-10"
                                                :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'">
                                                {{ category.label }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div v-for="service in specialServices" :key="`${category.id}-${service.id}`" class="relative rounded-md shadow-sm max-w-[150px] mx-auto">
                                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <span class="text-gray-500 sm:text-sm">$</span>
                                                    </div>
                                                    <input
                                                        :id="`${category.id}-${service.id}`"
                                                        type="number"
                                                        v-model.number="detailingPrices[category.id][service.id]"
                                                        step="0.01"
                                                        min="0"
                                                        required
                                                        class="focus:ring-primary focus:border-primary block w-full pl-8 pr-8 sm:text-sm border-gray-400 border-2 rounded-md appearance-none hover:border-gray-500 transition-colors duration-150"
                                                        :aria-label="`Price for ${service.label} on ${category.label}`"
                                                        :placeholder="formatDetailPrice(detailingPrices[category.id][service.id])"
                                                    />
                                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                                        <span class="text-gray-500 sm:text-sm">+</span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <div v-for="service in specialServices" :key="`display-${category.id}-${service.id}`">
                                                    <span class="font-medium">${{ formatDetailPrice(detailingPrices[category.id][service.id]) }}+</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Why Choose Us Section -->
                    <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
                        <!-- Section Header with Toggle -->
                        <div
                            class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
                            @click="detailingExpandedSections.whyChooseUs = !detailingExpandedSections.whyChooseUs"
                        >
                            <div>
                                <h3 class="text-lg leading-6 font-semibold text-gray-900">Why Choose Us</h3>
                                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                    Customize the "Why Choose Us" section displayed on the detailing page
                                </p>
                            </div>
                            <div class="text-gray-500">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-6 w-6 transition-transform duration-200"
                                    :class="{'rotate-180': !detailingExpandedSections.whyChooseUs}"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </div>
                        </div>

                        <!-- Why Choose Us Content -->
                        <div v-if="detailingExpandedSections.whyChooseUs" class="border-t border-gray-200 px-4 py-6 bg-gray-50">
                            <!-- Section Title and Description -->
                            <div class="mb-6">
                                <label for="whyChooseUsTitle" class="block text-sm font-medium text-gray-700 mb-1">Section Title</label>
                                <input
                                    id="whyChooseUsTitle"
                                    type="text"
                                    v-model="whyChooseUsContent.title"
                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Enter section title"
                                />
                            </div>
                            <div class="mb-6">
                                <label for="whyChooseUsDescription" class="block text-sm font-medium text-gray-700 mb-1">Section Description</label>
                                <textarea
                                    id="whyChooseUsDescription"
                                    v-model="whyChooseUsContent.description"
                                    rows="3"
                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Enter section description"
                                ></textarea>
                            </div>

                            <!-- Features -->
                            <div class="mb-6">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Features</h4>
                                <div class="space-y-6">
                                    <div v-for="(feature, index) in whyChooseUsContent.features" :key="index" class="bg-white p-4 rounded-md shadow-sm border border-gray-200">
                                        <div class="flex justify-between items-start mb-4">
                                            <h5 class="text-sm font-medium text-gray-900">Feature {{ index + 1 }}</h5>
                                            <button
                                                @click="whyChooseUsContent.features.splice(index, 1)"
                                                type="button"
                                                class="text-red-600 hover:text-red-800 focus:outline-none"
                                                :disabled="whyChooseUsContent.features.length <= 1"
                                                :class="{'opacity-50 cursor-not-allowed': whyChooseUsContent.features.length <= 1}"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="grid grid-cols-1 gap-4">
                                            <div>
                                                <label :for="`feature-title-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                                                <input
                                                    :id="`feature-title-${index}`"
                                                    type="text"
                                                    v-model="feature.title"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="Feature title"
                                                />
                                            </div>
                                            <div>
                                                <label :for="`feature-description-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                                <textarea
                                                    :id="`feature-description-${index}`"
                                                    v-model="feature.description"
                                                    rows="2"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="Feature description"
                                                ></textarea>
                                            </div>
                                            <div>
                                                <label :for="`feature-icon-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Icon SVG Path</label>
                                                <input
                                                    :id="`feature-icon-${index}`"
                                                    type="text"
                                                    v-model="feature.icon"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="SVG path data"
                                                />
                                                <p class="mt-1 text-xs text-gray-500">Enter the SVG path data for the icon (d attribute)</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button
                                    type="button"
                                    @click="whyChooseUsContent.features.push({ title: '', description: '', icon: '' })"
                                    class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                    Add Feature
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Detailing Process Section -->
                    <div class="bg-white shadow-md overflow-hidden sm:rounded-lg mb-8">
                        <!-- Section Header with Toggle -->
                        <div
                            class="px-4 py-5 sm:px-6 bg-gray-200 border-b border-gray-200 cursor-pointer flex justify-between items-center"
                            @click="detailingExpandedSections.detailingProcess = !detailingExpandedSections.detailingProcess"
                        >
                            <div>
                                <h3 class="text-lg leading-6 font-semibold text-gray-900">Detailing Process</h3>
                                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                    Customize the "Detailing Process" section displayed on the detailing page
                                </p>
                            </div>
                            <div class="text-gray-500">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-6 w-6 transition-transform duration-200"
                                    :class="{'rotate-180': !detailingExpandedSections.detailingProcess}"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </div>
                        </div>

                        <!-- Detailing Process Content -->
                        <div v-if="detailingExpandedSections.detailingProcess" class="border-t border-gray-200 px-4 py-6 bg-gray-50">
                            <!-- Section Title and Description -->
                            <div class="mb-6">
                                <label for="detailingProcessTitle" class="block text-sm font-medium text-gray-700 mb-1">Section Title</label>
                                <input
                                    id="detailingProcessTitle"
                                    type="text"
                                    v-model="detailingProcessContent.title"
                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Enter section title"
                                />
                            </div>
                            <div class="mb-6">
                                <label for="detailingProcessDescription" class="block text-sm font-medium text-gray-700 mb-1">Section Description</label>
                                <textarea
                                    id="detailingProcessDescription"
                                    v-model="detailingProcessContent.description"
                                    rows="3"
                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Enter section description"
                                ></textarea>
                            </div>

                            <!-- Process Steps -->
                            <div class="mb-6">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Process Steps</h4>
                                <div class="space-y-8">
                                    <div v-for="(step, index) in detailingProcessContent.steps" :key="index" class="bg-white p-4 rounded-md shadow-sm border border-gray-200">
                                        <div class="flex justify-between items-start mb-4">
                                            <h5 class="text-sm font-medium text-gray-900">Step {{ index + 1 }}: {{ step.title || 'Untitled Step' }}</h5>
                                            <button
                                                @click="detailingProcessContent.steps.splice(index, 1)"
                                                type="button"
                                                class="text-red-600 hover:text-red-800 focus:outline-none"
                                                :disabled="detailingProcessContent.steps.length <= 1"
                                                :class="{'opacity-50 cursor-not-allowed': detailingProcessContent.steps.length <= 1}"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="grid grid-cols-1 gap-4">
                                            <div>
                                                <label :for="`step-title-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                                                <input
                                                    :id="`step-title-${index}`"
                                                    type="text"
                                                    v-model="step.title"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="Step title"
                                                />
                                            </div>
                                            <div>
                                                <label :for="`step-description-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                                <textarea
                                                    :id="`step-description-${index}`"
                                                    v-model="step.description"
                                                    rows="2"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="Step description"
                                                ></textarea>
                                            </div>
                                            <div>
                                                <label :for="`step-image-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Image Path</label>
                                                <input
                                                    :id="`step-image-${index}`"
                                                    type="text"
                                                    v-model="step.image"
                                                    class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                    placeholder="/path/to/image.jpg"
                                                />
                                                <p class="mt-1 text-xs text-gray-500">Enter the path to the image file (e.g., /ASSESSMENT.jpg)</p>
                                            </div>
                                            <div>
                                                <label :for="`step-details-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Expanded Details</label>
                                                <div class="bg-gray-50 p-3 rounded-md border border-gray-200">
                                                    <div v-for="(detail, detailIndex) in step.expandedDetails" :key="`detail-${index}-${detailIndex}`" class="flex items-center mb-2">
                                                        <input
                                                            :id="`step-detail-${index}-${detailIndex}`"
                                                            type="text"
                                                            v-model="step.expandedDetails[detailIndex]"
                                                            class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                                                            placeholder="Detail point"
                                                        />
                                                        <button
                                                            @click="step.expandedDetails.splice(detailIndex, 1)"
                                                            type="button"
                                                            class="ml-2 text-red-600 hover:text-red-800 focus:outline-none"
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                    <button
                                                        type="button"
                                                        @click="step.expandedDetails.push('')"
                                                        class="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                                        </svg>
                                                        Add Detail
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button
                                    type="button"
                                    @click="detailingProcessContent.steps.push({
                                        title: '',
                                        description: '',
                                        image: '',
                                        expandedDetails: ['', '', '']
                                    })"
                                    class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-dark bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                    Add Process Step
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Save Button for Detailing -->
                    <div class="flex justify-end mt-6">
                        <button type="submit" :disabled="isSavingDetailing" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-opacity duration-200">
                            <svg v-if="isSavingDetailing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"> <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle> <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path> </svg>
                            {{ isSavingDetailing ? 'Saving...' : 'Save Detailing Prices' }}
                        </button>
                    </div>
                </form>
            </div>
          </div>

          <!-- NEW: Business Information Tab Content -->
          <div v-show="activeTab === 'business'">
            <BusinessInfoTab
              :business-info="businessInfo"
              :is-loading-business="isLoadingBusiness"
              :is-saving-business="isSavingBusiness"
              :save-success-business="saveSuccessBusiness"
              :save-error-business="saveErrorBusiness"
              :error-message-business="errorMessageBusiness"
              :save-business-info="saveBusinessInfo"
              :add-review="addReview"
              :remove-review="removeReview"
              :add-service="addService"
              :remove-service="removeService"
            />
          </div>

          <!-- NEW: Users Tab Content -->
          <div v-show="activeTab === 'users'">
            <div class="mb-6">
              <h2 class="text-2xl font-bold text-gray-900">User Management</h2>
              <p class="mt-1 text-sm text-gray-600">
                Manage users and their permissions. Only administrators can change user roles.
              </p>
            </div>

            <UserPermissionsTable />
          </div>

          <!-- NEW: Styling Tab Content -->
          <div v-show="activeTab === 'styling'">
            <StylingTab />
          </div>

        </div>
      </div>
    </main>

    <!-- Modals (Keep outside conditional tab content for proper overlay) -->

    <!-- Delete Confirmation Modal (Inventory) -->
    <div v-if="showDeleteModal" class="fixed z-20 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
       <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <transition enter-active-class="ease-out duration-300" enter-from-class="opacity-0" enter-to-class="opacity-100" leave-active-class="ease-in duration-200" leave-from-class="opacity-100" leave-to-class="opacity-0">
            <div v-show="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        </transition>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">​</span>
        <transition enter-active-class="ease-out duration-300" enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" enter-to-class="opacity-100 translate-y-0 sm:scale-100" leave-active-class="ease-in duration-200" leave-from-class="opacity-100 translate-y-0 sm:scale-100" leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <div v-show="showDeleteModal" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /> </svg>
                </div>
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title"> Delete Vehicle </h3>
                    <div class="mt-2"> <p class="text-sm text-gray-500"> Are you sure you want to delete <strong class="font-medium">{{ vehicleToDelete?.title || 'this vehicle' }}</strong>? This action cannot be undone. </p> </div>
                </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button @click="deleteVehicle" :disabled="isDeleting" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm transition ease-in-out duration-150 disabled:opacity-50 disabled:cursor-not-allowed">
                  <span v-if="isDeleting" class="mr-2">
                    <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </span>
                  {{ isDeleting ? 'Deleting...' : 'Delete' }}
                </button>
                <button @click="cancelDelete" :disabled="isDeleting" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:w-auto sm:text-sm transition ease-in-out duration-150 disabled:opacity-50 disabled:cursor-not-allowed"> Cancel </button>
            </div>
            </div>
        </transition>
      </div>
    </div>

    <!-- Vehicle Preview Modal (Inventory) -->
    <VehiclePreviewModal
      v-if="selectedVehicle"
      :vehicle="selectedVehicle"
      :is-open="showPreviewModal"
      @close="closePreviewModal"
      @edit="editVehicle(selectedVehicle.id)"
      @openAiStudio="openAiStudio(selectedVehicle.id)"
    />

  </div>
</template>

<style scoped>
/* Keep existing styles */
.truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
th[scope="col"][class*="cursor-pointer"] { user-select: none; }

/* Hide spinner buttons on number inputs */
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button { -webkit-appearance: none; margin: 0; }
input[type=number] { -moz-appearance: textfield; appearance: textfield; }

/* Style for sticky table header cell */
.sticky { position: sticky; }
.left-0 { left: 0; }
.z-10 { z-index: 10; }

/* Improve focus states */
button:focus-visible, input:focus-visible, select:focus-visible, textarea:focus-visible { /* Added textarea */
  outline: 2px solid var(--color-primary, #3b82f6); /* Adjust color as needed */
  outline-offset: 2px;
  box-shadow: 0 0 0 2px white, 0 0 0 4px var(--color-primary-ring, rgba(59, 130, 246, 0.5)); /* Example ring */
}
/* Specific focus for tab buttons - only apply focus styles to non-active tabs */
nav[aria-label="Tabs"] button:not(.border-primary):focus-visible {
   outline: none; /* Remove default outline */
   box-shadow: none; /* Remove general box-shadow if needed */
   /* Use ring utility or custom styles, but only for non-active tabs */
   --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
   --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--color-primary);
   box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
   --tw-ring-offset-width: 1px;
}

/* Remove focus ring during active/click state for all tab buttons */
nav[aria-label="Tabs"] button:active {
   outline: none !important;
   box-shadow: none !important;
   --tw-ring-offset-shadow: none !important;
   --tw-ring-shadow: none !important;
}

/* Ensure active tab is always underlined without focus ring */
nav[aria-label="Tabs"] button.border-primary {
   --tw-ring-offset-shadow: none !important;
   --tw-ring-shadow: none !important;
   border-bottom-width: 2px; /* Ensure underline is visible */
}

/* Ensure table layout allows sticky */
.overflow-x-auto table { border-collapse: separate; border-spacing: 0; }

/* Style for error images */
.error-image {
  object-fit: contain !important;
  padding: 0.25rem;
  background-color: #f3f4f6;
}

/* Detailing pricing styles */
.pricing-table-header {
  font-weight: 600;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.pricing-row:hover {
  background-color: #f9fafb;
}

/* Enhanced table styles for detailing pricing */
.overflow-x-auto table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.overflow-x-auto table th {
  position: relative;
  font-weight: 600;
}

.overflow-x-auto table th:after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 2px;
  background-color: #e5e7eb;
}

.overflow-x-auto table tbody tr:hover {
  background-color: #f9fafb;
}

/* Improve input field appearance */
.relative.rounded-md.shadow-sm input {
  transition: all 0.2s ease-in-out;
}

.relative.rounded-md.shadow-sm input:focus {
  border-color: var(--color-primary, #3b82f6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  outline: none;
}

/* Add subtle hover effect to inputs */
.relative.rounded-md.shadow-sm:hover input:not(:focus) {
  border-color: #64748b;
}

/* Enhanced input field styling */
.relative.rounded-md.shadow-sm {
  transition: transform 0.15s ease-in-out;
}

.relative.rounded-md.shadow-sm:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>