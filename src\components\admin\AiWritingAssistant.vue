<template>
  <div>
    <!-- AI Writing Assistant Button -->
    <button
      type="button"
      @click="togglePromptInput"
      class="absolute top-2 right-2 bg-primary text-white p-1 rounded-md hover:bg-primary-dark transition-colors z-10 flex items-center"
      title="AI Writing Assistant"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
      <span class="text-xs ml-1">AI</span>
    </button>

    <!-- Prompt Input Overlay (conditionally shown) -->
    <div v-if="showPromptInput" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full mx-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">AI Writing Assistant</h3>
          <button @click="showPromptInput = false" class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div class="mb-4">
          <label for="ai-prompt" class="block text-sm font-medium text-gray-700 mb-1">
            What would you like the AI to write?
          </label>
          <textarea
            id="ai-prompt"
            v-model="prompt"
            rows="3"
            class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-300 rounded-md px-3 py-2"
            placeholder="Example: Write a professional description for a car dealership that specializes in luxury vehicles"
          ></textarea>
        </div>
        
        <div class="flex justify-end space-x-3">
          <button
            type="button"
            @click="showPromptInput = false"
            class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Cancel
          </button>
          <button
            type="button"
            @click="generateContent"
            :disabled="isGenerating || !prompt.trim()"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="isGenerating" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isGenerating ? 'Generating...' : 'Generate' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { GoogleGenAI } from "@google/genai";

const props = defineProps({
  fieldType: {
    type: String,
    required: true,
    validator: (value) => ['description', 'mission', 'vision', 'history'].includes(value)
  },
  currentValue: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:content']);

const showPromptInput = ref(false);
const prompt = ref('');
const isGenerating = ref(false);

// Predefined prompts based on field type
const getDefaultPrompt = () => {
  switch (props.fieldType) {
    case 'description':
      return 'Write a professional and engaging business description for a car dealership';
    case 'mission':
      return 'Create a compelling mission statement for a car dealership';
    case 'vision':
      return 'Write an inspiring vision statement for a car dealership';
    case 'history':
      return 'Create a brief company history for a car dealership';
    default:
      return '';
  }
};

const togglePromptInput = () => {
  showPromptInput.value = !showPromptInput.value;
  if (showPromptInput.value && !prompt.value) {
    prompt.value = getDefaultPrompt();
  }
};

// Initialize the Gemini API
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const ai = new GoogleGenAI({ apiKey: API_KEY });

const generateContent = async () => {
  if (!prompt.value.trim()) return;
  
  isGenerating.value = true;
  
  try {
    // Prepare detailed system prompt based on field type
    let contextPrompt = 'You are a professional copywriter specializing in the automotive industry. Your task is to create high-quality, concise content for a car dealership website. Your writing should be professional, engaging, and specifically tailored to the car dealership industry.\n\n';
    contextPrompt += 'IMPORTANT GUIDELINES:\n';
    contextPrompt += '1. Keep all content relevant to the automotive/car dealership industry\n';
    contextPrompt += '2. Use industry-appropriate terminology and language\n';
    contextPrompt += '3. Focus on quality, professionalism, and customer service\n';
    contextPrompt += '4. Avoid generic content that could apply to any business\n';
    contextPrompt += '5. Be specific, concise, and impactful\n';
    contextPrompt += '6. Maintain a tone that builds trust and credibility\n\n';
    
    // Add field-specific instructions
    switch (props.fieldType) {
      case 'description':
        contextPrompt += 'BUSINESS DESCRIPTION GUIDELINES:\n';
        contextPrompt += '- Create a professional and engaging business description for the car dealership\n';
        contextPrompt += '- Length: 3-5 sentences (approximately 75-125 words)\n';
        contextPrompt += '- Include what makes this dealership unique (quality vehicles, customer service, etc.)\n';
        contextPrompt += '- Mention the types of vehicles or services offered\n';
        contextPrompt += '- Use language that builds trust and credibility\n';
        contextPrompt += '- Avoid clichés and generic statements\n';
        break;
      case 'mission':
        contextPrompt += 'MISSION STATEMENT GUIDELINES:\n';
        contextPrompt += '- Create a compelling mission statement for the car dealership\n';
        contextPrompt += '- Length: 1-3 sentences (approximately 25-75 words)\n';
        contextPrompt += '- Focus on customer service, quality, and values\n';
        contextPrompt += '- Express the dealership\'s purpose and commitment to customers\n';
        contextPrompt += '- Use clear, direct language that resonates with car buyers\n';
        contextPrompt += '- Avoid vague or generic mission statements\n';
        break;
      case 'vision':
        contextPrompt += 'VISION STATEMENT GUIDELINES:\n';
        contextPrompt += '- Create an inspiring vision statement for the car dealership\n';
        contextPrompt += '- Length: 1-3 sentences (approximately 25-75 words)\n';
        contextPrompt += '- Focus on future goals and aspirations\n';
        contextPrompt += '- Include the dealership\'s impact on customers and the community\n';
        contextPrompt += '- Express long-term ambitions in the automotive industry\n';
        contextPrompt += '- Use forward-looking, aspirational language\n';
        break;
      case 'history':
        contextPrompt += 'COMPANY HISTORY GUIDELINES:\n';
        contextPrompt += '- Create a compelling company history for the car dealership\n';
        contextPrompt += '- Length: 4-6 sentences (approximately 100-150 words)\n';
        contextPrompt += '- Create a narrative about founding, growth, and key milestones\n';
        contextPrompt += '- Include automotive industry-specific achievements\n';
        contextPrompt += '- Emphasize longevity, experience, and evolution in the car business\n';
        contextPrompt += '- Use a storytelling approach that builds credibility\n';
        break;
    }
    
    contextPrompt += '\nYour response should ONLY include the final text content, with no explanations, introductions, or additional notes.';
    
    // Include current content as context if it exists
    let userPrompt = '';
    if (props.currentValue && props.currentValue.trim()) {
      userPrompt = `The current content is: "${props.currentValue}"\n\nImprove upon this or rewrite it based on the following prompt: ${prompt.value}`;
    } else {
      userPrompt = `Generate content based on the following prompt: ${prompt.value}`;
    }
    
    // Call the Gemini API using the GoogleGenAI library
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: [{
        role: 'user',
        parts: [{ text: userPrompt }]
      }],
      config: {
        systemInstruction: contextPrompt,
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
      },
    });
    
    // Extract the generated text from the response
    const generatedText = response.text;
    
    // Emit the generated content to the parent component
    emit('update:content', generatedText);
    
    // Close the prompt input
    showPromptInput.value = false;
    prompt.value = '';
    
  } catch (error) {
    console.error('Error generating content with Gemini:', error);
    
    // Provide more detailed error information
    if (error.response) {
      console.error('API Error Response:', error.response);
    }
    
    // Provide more specific error messages based on the error type
    let errorMessage = 'Failed to generate content. Please try again later.';
    
    if (error.message) {
      if (error.message.includes('API key')) {
        errorMessage = 'API key error. Please contact the administrator.';
      } else if (error.message.includes('network') || error.message.includes('timeout')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.message.includes('content filtered') || error.message.includes('safety')) {
        errorMessage = 'Content was filtered due to safety settings. Please modify your prompt and try again.';
      } else if (error.message.includes('quota') || error.message.includes('rate limit')) {
        errorMessage = 'API usage limit reached. Please try again later.';
      }
    }
    
    alert(errorMessage);
  } finally {
    isGenerating.value = false;
  }
};
</script>

<style scoped>
/* Add any component-specific styles here */
</style>