<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { getInterestRates, getFinancingFaqs } from '../database/settingsService';
import { submitPreQualification } from '../database/financingService';

// --- State for fetched rates ---
const fetchedAprRates = ref(null); // Store fetched rates here
const isLoadingRates = ref(true);  // Loading state for fetching rates
const loadingErrorRates = ref(null); // Error message if fetching fails

// --- Calculator State ---
const vehiclePriceInput = ref('');
const downPaymentInput = ref('');
const termLengthInput = ref('60'); // Default matches select
const creditScoreTierInput = ref('good'); // Default matches select
const calculatedPayment = ref(null);
const calculationError = ref(null);
const isLoadingCalculation = ref(false);
const showPaymentOverlay = ref(false);
const isPaymentAnimating = ref(false);

// --- Computed property to safely access rates ---
// Use fetched rates if available, otherwise return empty object to prevent errors
const estimatedAprRates = computed(() => {
  // Ensure fetchedAprRates.value is not null and has keys before returning
  return fetchedAprRates.value && typeof fetchedAprRates.value === 'object' && Object.keys(fetchedAprRates.value).length > 0
    ? fetchedAprRates.value
    : {}; // Return empty object as fallback
});

// --- Function to Calculate Payment (Client-Side) ---
const calculateFinancePayment = () => {
  // Guard clause: Don't calculate if rates are loading or failed to load
  if (isLoadingRates.value || loadingErrorRates.value || !fetchedAprRates.value || Object.keys(estimatedAprRates.value).length === 0) {
    calculationError.value = 'Interest rates are currently unavailable. Please try again later.';
    isLoadingCalculation.value = false; // Ensure loading state is reset
    return;
  }

  isLoadingCalculation.value = true;
  calculatedPayment.value = null;
  calculationError.value = null;

  // --- Input Validation & Cleaning ---
  const errors = [];
  const priceString = String(vehiclePriceInput.value).replace(/[^0-9.]/g, '');
  const downString = String(downPaymentInput.value).replace(/[^0-9.]/g, '');

  if (priceString === '') errors.push('Vehicle price is required.');
  if (downString === '') errors.push('Down payment is required.');

  const price = parseFloat(priceString);
  const downPayment = parseFloat(downString);
  const term = parseInt(termLengthInput.value, 10);
  const creditTier = creditScoreTierInput.value;

  if (isNaN(price) || price <= 0) errors.push('Invalid vehicle price.');
  if (isNaN(downPayment) || downPayment < 0) errors.push('Invalid down payment.');

  // Use the computed property for rates
  if (!estimatedAprRates.value[creditTier]) {
     errors.push('Rate data unavailable for the selected credit tier.');
  }
  if (!isNaN(price) && !isNaN(downPayment) && downPayment > price) {
      errors.push('Down payment cannot exceed vehicle price.');
  }
  if (![36, 48, 60, 72, 84].includes(term)) errors.push('Invalid term length selected.');

  if (errors.length > 0) {
    calculationError.value = errors.join(' ');
    isLoadingCalculation.value = false;
    return;
  }

  // --- Rate Lookup ---
  let annualRatePercent;
  try {
    // Access rates via the computed property
    annualRatePercent = estimatedAprRates.value[creditTier]?.[term];

    // Check if the specific rate exists and is a valid number
    if (annualRatePercent === undefined || annualRatePercent === null || isNaN(parseFloat(annualRatePercent))) {
        console.warn(`Rate not found or invalid for tier '${creditTier}' and term ${term}.`);
        // Attempt to find a fallback rate within the same tier
        const tierRates = estimatedAprRates.value[creditTier] || {};
        const fallbackRate = Object.values(tierRates).find(r => r !== undefined && r !== null && !isNaN(parseFloat(r)));

        if (fallbackRate !== undefined) {
            annualRatePercent = fallbackRate;
            console.warn(`Using fallback rate for tier '${creditTier}': ${fallbackRate}%`);
            // Optionally inform the user about the fallback rate being used
            // calculationError.value = `Rate unavailable for ${term} months. Estimated using available rates.`;
        } else {
            // If no fallback rate is found in the tier, throw an error
            throw new Error(`No valid rates found for credit tier '${creditTier}'`);
        }
    }
     // Ensure rate is treated as a number for calculations
     annualRatePercent = parseFloat(annualRatePercent);

  } catch (e) {
    console.error(`Error looking up rate: ${e.message}`);
    calculationError.value = 'Could not find an estimated rate for the selected options.';
    isLoadingCalculation.value = false;
    return;
  }

  console.log(`Client-Side Calc: Price=${price}, Down=${downPayment}, Term=${term}, Tier='${creditTier}', Rate=${annualRatePercent}%`);

  // --- Calculation ---
  try {
    const principal = price - downPayment;
    if (principal <= 0) {
      calculatedPayment.value = '0.00'; // Handle zero or negative principal
      isLoadingCalculation.value = false;
      showPaymentOverlay.value = true;
      isPaymentAnimating.value = true;
      setTimeout(() => { isPaymentAnimating.value = false; }, 1500);
      return;
    }

    const annualRateDecimal = annualRatePercent / 100;
    const monthlyRate = annualRateDecimal / 12;
    const numberOfPayments = term;

    let monthlyPayment;
    if (monthlyRate === 0) { // Handle 0% interest case
      monthlyPayment = principal / numberOfPayments;
    } else {
      // Standard Amortization Formula
      monthlyPayment = principal *
                       (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) /
                       (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
    }

    // Ensure monthlyPayment is a valid number before formatting
    if (isNaN(monthlyPayment)) {
        throw new Error("Calculation resulted in NaN.");
    }

    calculatedPayment.value = monthlyPayment.toFixed(2);
    showPaymentOverlay.value = true;
    isPaymentAnimating.value = true;
    setTimeout(() => { isPaymentAnimating.value = false; }, 1500); // Match overlay transition

  } catch (calcError) {
    console.error('Error during payment calculation:', calcError);
    calculationError.value = 'Failed to calculate payment. Please check inputs.';
  } finally {
    isLoadingCalculation.value = false;
  }
};


// --- Fetch interest rates from database ---
const fetchInterestRates = async () => {
  isLoadingRates.value = true;
  loadingErrorRates.value = null;
  fetchedAprRates.value = null; // Reset before fetching
  console.log('Fetching interest rates...');
  try {
    const rates = await getInterestRates();
    console.log('Raw rates fetched:', rates);
    // Basic validation: Check if rates is an object and has keys
    if (rates && typeof rates === 'object' && Object.keys(rates).length > 0) {
      // Further validation could be added here to check nested structure if needed
      fetchedAprRates.value = rates; // Store fetched rates
      console.log('Interest rates loaded successfully:', fetchedAprRates.value);
    } else {
      // Handle case where no rates are configured or DB returns unexpected format
      loadingErrorRates.value = 'Financing rates are not configured yet. Please check back later.';
      console.warn('No interest rates found in database or database returned invalid data.');
      // Note: The calculator UI will show the error/loading state handled by v-if
    }
  } catch (error) {
    console.error('Error fetching interest rates:', error);
    loadingErrorRates.value = 'Failed to load financing rates. Please try again.';
  } finally {
    isLoadingRates.value = false;
    console.log('Finished fetching rates. Loading:', isLoadingRates.value, 'Error:', loadingErrorRates.value);
  }
};


// --- Original Setup Code (Animations, Data, Particles, etc.) ---
// Animation references
const pageTitle = ref(null);
const financingOptionsSection = ref(null);
const applicationSection = ref(null);
const benefitsCards = ref([]);

// Financing options data (Static - does not depend on fetched rates)
const financingOptions = ref([
  {
    title: 'Standard Financing',
    description: 'Traditional financing options with competitive rates for customers with established credit.',
    image: '/STANDARDFINANCING.png',
    features: [
      'Terms from 12-84 months',
      'Competitive interest rates',
      'Fixed monthly payments',
      'No early payment penalties',
      'Finance amounts from $5,000 to $100,000'
    ]
  },
  {
    title: 'Special Programs',
    description: 'Special financing programs designed for first-time buyers and those with limited credit history.',
    image: '/SPECIALPROGRAMS.png',
    features: [
      'First-time buyer programs',
      'College graduate incentives',
      'Military appreciation offers',
      'Flexible down payment options',
      'Custom term lengths available'
    ]
  },
  {
    title: 'Lease Options',
    description: 'Drive a new vehicle with lower monthly payments and the flexibility to upgrade more often.',
    image: '/LEASEOPTIONS.png',
    features: [
      'Lower monthly payments',
      'Drive new vehicles more often',
      'Multiple mileage options',
      'GAP coverage included',
      'End-of-lease purchase options'
    ]
  },
  {
    title: 'Credit Rebuilding',
    description: 'Specialized financing options for customers looking to rebuild their credit history.',
    image: '/CREDITREBUILDING.png',
    features: [
      'Options for challenged credit',
      'Credit rebuilding programs',
      'Reportable payment history',
      'Refinancing opportunities',
      'Financial education resources'
    ]
  }
]);
const hoveredOption = ref(null);

// Benefits of financing with us (Static)
const benefits = ref([
  { title: 'Fast Approvals', description: 'Get pre-approved quickly, often within the same day you apply.', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' },
  { title: 'Multiple Lenders', description: 'We work with numerous financial institutions to find you the best rates.', icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4' },
  { title: 'Flexible Terms', description: 'Customize your loan terms to fit your budget and financial goals.', icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z' },
  { title: 'Trade-In Assistance', description: "We'll help you get the maximum value for your current vehicle.", icon: 'M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z' }
]);

// FAQ items (Fetched from database with fallback to static data)
const faqItems = ref([
  { question: 'What credit score do I need to qualify for financing?', answer: "While a higher credit score typically leads to better rates, we work with lenders who can accommodate a range of credit profiles. We have financing options for credit scores from 550 to 850+, with specialized programs for those with limited credit history.", isOpen: false },
  { question: 'How much of a down payment is required?', answer: "Down payment requirements vary based on your credit profile, the vehicle you're purchasing, and the lender. While some programs offer zero down payment options, a down payment of 10-20% of the vehicle's value often results in better rates and terms.", isOpen: false },
  { question: 'Can I get pre-approved before finding a vehicle?', answer: "Yes! We recommend getting pre-approved before shopping. This gives you a clear budget and strengthens your negotiating position. Our pre-approval process is quick and doesn't impact your credit score with a hard inquiry until you're ready to finalize your purchase.", isOpen: false },
  { question: 'How long does the financing process take?', answer: "Most customers receive initial approval within 1-2 hours of submitting a complete application. The entire process from application to driving away in your new vehicle can be completed in as little as 24-48 hours, depending on your specific situation.", isOpen: false },
  { question: 'Can I refinance my current auto loan?', answer: 'Absolutely! If interest rates have dropped or your credit score has improved since your original financing, refinancing could save you money. We offer refinancing options that can lower your monthly payment or reduce your loan term.', isOpen: false },
  { question: 'Do you offer financing for aftermarket upgrades and service packages?', answer: 'Yes, we provide financing options that can include aftermarket upgrades, extended warranties, and service packages. These can be rolled into your auto loan, allowing you to enhance your vehicle while maintaining a single monthly payment.', isOpen: false }
]);
const isLoadingFaqs = ref(true);
const loadingErrorFaqs = ref(null);

// Function to fetch FAQs from database
const fetchFinancingFaqs = async () => {
  isLoadingFaqs.value = true;
  loadingErrorFaqs.value = null;

  try {
    const faqs = await getFinancingFaqs();
    console.log('Fetched FAQs:', faqs);

    // If FAQs exist in the database and are in the correct format, use them
    if (faqs && Array.isArray(faqs) && faqs.length > 0) {
      // Make sure each FAQ has the isOpen property
      faqItems.value = faqs.map(faq => ({
        ...faq,
        isOpen: false
      }));
      console.log('Using FAQs from database');
    } else {
      // Keep the default static FAQs if none found in database
      console.log('No FAQs found in database, using default static FAQs');
    }
  } catch (error) {
    console.error('Error fetching FAQs:', error);
    loadingErrorFaqs.value = 'Failed to load FAQ data';
  } finally {
    isLoadingFaqs.value = false;
  }
};

const toggleFaq = (index) => { faqItems.value[index].isOpen = !faqItems.value[index].isOpen; };

// Required documents for financing (Static)
const requiredDocuments = ref([
  "Valid driver's license", 'Proof of income (recent pay stubs or tax returns)', 'Proof of residence (utility bill or lease agreement)', 'Proof of insurance', 'Vehicle information (if trading in)'
]);

// Financing process steps (Static)
const financingProcess = ref([
  { title: 'Application', description: 'Complete our secure online application or visit us in person.', icon: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z' },
  { title: 'Approval', description: 'Our finance team works with lenders to secure your approval.', icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' },
  { title: 'Selection', description: 'Choose your perfect vehicle from our extensive inventory.', icon: 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z' },
  { title: 'Finalization', description: 'Complete paperwork and drive home in your new vehicle.', icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' }
]);

// Testimonials (Static)
const testimonials = ref([
  { name: 'Marcus Johnson', vehicle: 'Sports Sedan', quote: 'The financing team at GT Motorsports made the process incredibly smooth. They found me a great rate despite my complex credit history.', rating: 5 },
  { name: 'Lisa Wong', vehicle: 'Luxury Coupe', quote:"As a first-time buyer, I was nervous about financing, but they walked me through every step and got me approved when other dealers couldn't.", rating: 5 },
  { name: 'Robert Taylor', vehicle: 'Performance Car', quote: 'They found me financing terms that were much better than what my bank offered. The whole process was quick and transparent.', rating: 5 },
  { name: 'Sophia Martinez', vehicle: 'Sports Car', quote: 'I was able to get pre-approved online before I even visited the dealership, which made the buying process so much faster.', rating: 5 },
  { name: 'Tyler Wilson', vehicle: 'Performance Sedan', quote:"Their credit rebuilding program helped me secure financing when I thought it would be impossible. Now I'm rebuilding my credit while driving my dream car.", rating: 5 },
  { name: 'Jasmine Chen', vehicle: 'Luxury Coupe', quote:"The team found me creative financing solutions that worked with my business income. Couldn't be happier with the service.", rating: 5 },
  { name: 'Ethan Brown', vehicle: 'Muscle Car', quote: 'I was upside down on my trade-in, but they worked with me to find a solution that got me into a newer vehicle with a payment I could afford.', rating: 5 },
  { name: 'Olivia Davis', vehicle: 'Hot Hatchback', quote: 'The zero down payment option they found for me was exactly what I needed. The whole process took less than 48 hours from application to approval.', rating: 5 }
]);
const testimonialContainer = ref(null);
const isTestimonialAnimationPaused = ref(false);
const pauseTestimonialAnimation = () => { isTestimonialAnimationPaused.value = true; };
const resumeTestimonialAnimation = () => { isTestimonialAnimationPaused.value = false; };

// --- Financing Pre-Qualification Form ---
const financingForm = ref({
  first_name: '',
  last_name: '',
  email: '',
  phone_number: '',
  vehicle_of_interest: '',
  financing_type: 'Purchase' // Default to Purchase
});
const isSubmittingForm = ref(false);
const formSubmitSuccess = ref(false);
const formSubmitError = ref(null);

const submitFinancingForm = async () => {
  formSubmitError.value = null;
  isSubmittingForm.value = true;

  try {
    const result = await submitPreQualification(financingForm.value);

    if (result.success) {
      formSubmitSuccess.value = true;
      // Reset form
      financingForm.value = {
        first_name: '',
        last_name: '',
        email: '',
        phone_number: '',
        vehicle_of_interest: '',
        financing_type: 'Purchase'
      };
    } else {
      // Display the error message from the service
      formSubmitError.value = result.error || 'An error occurred while submitting the form. Please try again.';

      // Log additional details if available
      if (result.details) {
        console.error('[FinancingView] Detailed error:', result.details);
      }
    }
  } catch (error) {
    console.error('[FinancingView] Error submitting pre-qualification form:', error);
    formSubmitError.value = 'An unexpected error occurred. Please try again later.';
  } finally {
    isSubmittingForm.value = false;
  }
};

// Counter animation function
const animateCounter = (element, target, duration = 2000) => {
  let start = 0;
  const increment = target / (duration / 16); // 16ms for ~60fps
  const updateCounter = () => {
    start += increment;
    if (start >= target) {
      element.textContent = target.toLocaleString(); // Format with commas if needed
    } else {
      element.textContent = Math.floor(start).toLocaleString();
      requestAnimationFrame(updateCounter);
    }
  };
  requestAnimationFrame(updateCounter); // Start the animation frame loop
};


// --- Custom Particles Animation ---
const particlesCanvas = ref(null);
let animationFrameId = null;
let ctx = null;
let particles = [];
let mouseX = 0;
let mouseY = 0;
let isMouseOver = false;

class Particle {
  constructor(canvas) {
    this.canvas = canvas;
    this.x = Math.random() * canvas.width;
    this.y = Math.random() * canvas.height;
    this.size = Math.random() * 20 + 20;
    this.speedY = -Math.random() * 0.6 - 0.3;
    this.speedX = Math.random() * 0.6 - 0.3;
    this.targetOpacity = Math.random() * 0.2 + 0.1; // Store target opacity
    this.opacity = 0; // Start with opacity 0 for fade-in effect
    this.fadeSpeed = 0.005 + Math.random() * 0.01; // Random fade speed for each particle
    this.rotation = Math.random() * 360;
    this.rotationSpeed = Math.random() * 1 - 0.5;
  }

  update() {
    // Update position and rotation
    this.y += this.speedY;
    this.x += this.speedX;
    this.rotation += this.rotationSpeed;

    // Fade in effect
    if (this.opacity < this.targetOpacity) {
      this.opacity += this.fadeSpeed;
      if (this.opacity > this.targetOpacity) {
        this.opacity = this.targetOpacity;
      }
    }

    // Reset particle if it goes off screen
    if (this.y < -this.size * 2) {
      this.y = this.canvas.height + this.size;
      this.x = Math.random() * this.canvas.width;
    }

    // Mouse interaction
    if (isMouseOver) {
      const dx = this.x - mouseX;
      const dy = this.y - mouseY;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const maxDistance = 200;
      if (distance < maxDistance) {
        const force = (maxDistance - distance) / maxDistance;
        const angle = Math.atan2(dy, dx);
        this.x += Math.cos(angle) * force * 3;
        this.y += Math.sin(angle) * force * 3;
      }
    }
  }

  draw(ctx) {
    ctx.save();
    ctx.translate(this.x, this.y);
    ctx.rotate(this.rotation * Math.PI / 180);
    ctx.font = `bold ${this.size}px Arial`;
    ctx.fillStyle = `rgba(255, 255, 255, ${this.opacity})`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('$', 0, 0);
    ctx.restore();
  }
}

let resizeCanvas, createParticles, handleMouseMove, handleMouseEnter, handleMouseLeave;

const initParticles = () => {
  if (!particlesCanvas.value) return;
  const canvas = particlesCanvas.value;
  ctx = canvas.getContext('2d');
  if (!ctx) { console.error("Failed to get 2D context"); return; } // Added context check
  resizeCanvas = () => { const container = canvas.parentElement; if (!container) return; canvas.width = container.offsetWidth; canvas.height = container.offsetHeight; createParticles(); };
  createParticles = () => { particles = []; const particleCount = Math.max(10, Math.floor((canvas.width * canvas.height) / 20000)); for (let i = 0; i < particleCount; i++) { particles.push(new Particle(canvas)); } }; // Ensure minimum particles
  handleMouseMove = (e) => { const rect = canvas.getBoundingClientRect(); mouseX = e.clientX - rect.left; mouseY = e.clientY - rect.top; isMouseOver = true; };
  handleMouseEnter = () => { isMouseOver = true; };
  handleMouseLeave = () => { isMouseOver = false; };
  canvas.addEventListener('mousemove', handleMouseMove); canvas.addEventListener('mouseenter', handleMouseEnter); canvas.addEventListener('mouseleave', handleMouseLeave);
  const heroSection = canvas.closest('section'); if (heroSection) { heroSection.addEventListener('mousemove', handleMouseMove); }
  resizeCanvas(); // Initial setup
  window.addEventListener('resize', resizeCanvas);
  const animate = () => { if (!ctx || !canvas || !particles) return; ctx.clearRect(0, 0, canvas.width, canvas.height); particles.forEach(particle => { particle.update(); particle.draw(ctx); }); animationFrameId = requestAnimationFrame(animate); };
  animate();
};

const cleanupParticles = () => {
  if (animationFrameId) { cancelAnimationFrame(animationFrameId); animationFrameId = null; }
  if (particlesCanvas.value) {
    const canvas = particlesCanvas.value;
    if (handleMouseMove) canvas.removeEventListener('mousemove', handleMouseMove);
    if (handleMouseEnter) canvas.removeEventListener('mouseenter', handleMouseEnter);
    if (handleMouseLeave) canvas.removeEventListener('mouseleave', handleMouseLeave);
    const heroSection = canvas.closest('section');
    if (heroSection && handleMouseMove) { heroSection.removeEventListener('mousemove', handleMouseMove); }
    if (resizeCanvas) window.removeEventListener('resize', resizeCanvas);
  }
  ctx = null; particles = []; isMouseOver = false;
};


// Lifecycle Hooks
onMounted(async () => {
  // Set fixed heights and positions immediately to prevent layout shifts
  if (particlesCanvas.value && particlesCanvas.value.parentElement) {
    particlesCanvas.value.parentElement.style.minHeight = '700px';
  }

  // Pre-position elements to prevent layout shifts
  if (pageTitle.value) {
    pageTitle.value.style.transform = 'none';
    pageTitle.value.style.position = 'relative';
  }

  if (financingOptionsSection.value) {
    financingOptionsSection.value.style.transform = 'none';
    financingOptionsSection.value.style.position = 'relative';
  }

  // Fetch interest rates and FAQs
  await Promise.all([
    fetchInterestRates(),
    fetchFinancingFaqs()
  ]);

  // Initialize particles and animations AFTER fetching
  initParticles();

  // Add animation classes with delay - restore original animations
  if (pageTitle.value) { setTimeout(() => { pageTitle.value.style.opacity = '1'; pageTitle.value.classList.add('animate-fade-in-down'); }, 100); }
  if (financingOptionsSection.value) { setTimeout(() => { financingOptionsSection.value.style.opacity = '1'; financingOptionsSection.value.classList.add('animate-fade-in-up'); }, 300); }
  if (benefitsCards.value.length) {
    benefitsCards.value.forEach((card, index) => {
      if (card instanceof HTMLElement) { setTimeout(() => { card.style.opacity = '1'; card.classList.add('animate-fade-in-up'); }, 500 + (index * 100)); }
      else { console.warn('Benefit card ref item is not an HTMLElement:', card); }
    });
  }

  // Intersection Observer for application section
  if (applicationSection.value) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Ensure animations run only once
          if (!entry.target.dataset.animated) {
            entry.target.dataset.animated = 'true'; // Mark as animated

            const formContainer = applicationSection.value.querySelector('.form-container');
            const processStepsEl = applicationSection.value.querySelector('.process-steps'); // Use specific name

            // Stagger the two main column animations slightly
            if (formContainer) {
                setTimeout(() => {
                    formContainer.style.opacity = '1';
                    formContainer.classList.add('animate-fade-in-left');
                }, 100); // Slight delay for left column
            }
            if (processStepsEl) {
                setTimeout(() => {
                    processStepsEl.style.opacity = '1';
                    processStepsEl.classList.add('animate-fade-in-right');
                }, 200); // Slightly later delay for right column

                // Animate the step cards within the process steps (right column)
                const stepCards = processStepsEl.querySelectorAll('.step-card');
                stepCards.forEach((card, index) => {
                  setTimeout(() => {
                    card.style.opacity = '1';
                    card.classList.add('animate-fade-in-up');
                  }, 400 + (index * 150)); // Base delay after right column starts fading in
                });
            }
            observer.unobserve(entry.target); // Stop observing after triggering
          }
        }
      });
    }, { threshold: 0.1 }); // Lower threshold slightly
    observer.observe(applicationSection.value);
  }

  // Animate counters (ensure elements exist before animating)
  const animateCounters = () => {
    const counters = document.querySelectorAll('.animated-counter');
    counters.forEach(counter => {
      const target = parseInt(counter.getAttribute('data-target'), 10);
      if (!isNaN(target) && counter) { // Added check for counter existence
        counter.textContent = '0'; // Initialize
        setTimeout(() => { animateCounter(counter, target, 1500); }, 100); // Delay slightly
      } else { console.warn('Invalid data-target or element not found for counter:', counter); }
    });
  };
  // Use setTimeout to ensure the DOM is ready after initial render and data fetch
  setTimeout(animateCounters, 600); // Increased delay slightly to allow layout settle

});

onUnmounted(() => {
  cleanupParticles();
});

</script>
<template>
  <!-- Hero Section with Advanced Animations -->
  <section class="relative overflow-hidden bg-gradient-to-b from-primary to-primary-dark py-20 md:py-28 min-h-[700px] z-[1] pb-24">


    <!-- START: Custom Particles Canvas -->
    <canvas ref="particlesCanvas" class="absolute inset-0 z-0 w-full h-full"></canvas>
    <!-- END: Custom Particles Canvas -->

    <!-- Main Content Container -->
    <div class="container-custom relative z-10">
      <div class="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12 mb-20">
        <!-- Content section - with original animations but better positioning -->
        <div class="w-full lg:w-1/2 text-white px-4 sm:px-0" ref="pageTitle" style="opacity: 0; min-height: 300px; transform: none;">
          <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight">
            <span class="relative inline-block mr-2 animated-text">
              Flexible
              <span class="absolute bottom-1 left-0 w-full h-1 bg-secondary transform scale-x-0 origin-left transition-transform duration-700 delay-300"></span>
            </span>
            <span class="relative inline-block animated-text" style="animation-delay: 0.2s;">
              Financing
              <span class="absolute bottom-1 left-0 w-full h-1 bg-secondary transform scale-x-0 origin-left transition-transform duration-700 delay-700"></span>
            </span>
            <br class="md:hidden">
            <span class="relative inline-block animated-text" style="animation-delay: 0.4s;">
              Solutions
              <span class="absolute bottom-1 left-0 w-full h-1 bg-secondary transform scale-x-0 origin-left transition-transform duration-700 delay-1100"></span>
            </span>
          </h1>
          <p class="text-base sm:text-lg md:text-xl text-white/80 mb-6 max-w-lg animated-text" style="animation-delay: 0.6s;">
            Customized financing options designed to get you behind the wheel of your dream performance vehicle.
          </p>
          <div class="flex flex-wrap animated-text" style="animation-delay: 0.8s;">
            <a href="#apply" class="btn btn-secondary py-3 px-6 md:px-8 text-sm md:text-base relative overflow-hidden group mr-4 sm:mr-5">
              <span class="relative z-10">Apply Now</span>
              <span class="absolute inset-0 bg-gray-800 transform scale-x-0 origin-left group-hover:scale-x-100 transition-transform duration-300"></span>
            </a>
            <a href="#options" class="btn btn-outline border-white text-white hover:bg-white hover:text-primary active:bg-white active:text-primary py-3 px-6 md:px-8 text-sm md:text-base relative overflow-hidden group">
              <span class="relative z-10">Explore Options</span>
            </a>
          </div>
          <!-- Trust indicators with Canadian lender logos -->
          <div class="mt-8 md:mt-10 animated-text" style="animation-delay: 1s;">
            <p class="text-sm font-medium text-white/70 mb-3">Trusted By Top Canadian Lenders:</p>
            <div class="flex flex-wrap items-center gap-4 md:gap-6">
              <!-- RBC Logo -->
              <div class="bg-white/10 rounded-lg p-2 backdrop-blur-sm">
                <img src="/lenders/rbc.svg" alt="RBC Logo" class="h-6 md:h-8 w-auto opacity-70 filter grayscale hover:opacity-100 hover:filter-none transition-all duration-300">
              </div>
              <!-- TD Logo -->
              <div class="bg-white/10 rounded-lg p-2 backdrop-blur-sm">
                <img src="/lenders/td.png" alt="TD Bank Logo" class="h-6 md:h-8 w-auto opacity-70 filter grayscale hover:opacity-100 hover:filter-none transition-all duration-300">
              </div>
              <!-- BMO Logo -->
              <div class="bg-white/10 rounded-lg p-2 backdrop-blur-sm">
                <img src="/lenders/bmo.png" alt="BMO Logo" class="h-6 md:h-8 w-auto opacity-70 filter grayscale hover:opacity-100 hover:filter-none transition-all duration-300">
              </div>
              <!-- CIBC Logo -->
              <div class="bg-white/10 rounded-lg p-2 backdrop-blur-sm">
                <img src="/lenders/cibc.png" alt="CIBC Logo" class="h-6 md:h-8 w-auto opacity-70 filter grayscale hover:opacity-100 hover:filter-none transition-all duration-300">
              </div>
            </div>
          </div>
        </div>

        <!-- Calculator Card Container with fixed dimensions -->
        <div class="w-full lg:w-1/2 perspective-container" ref="financingOptionsSection" style="opacity: 0; min-height: 450px; transform: none;">
          <div class="relative w-full max-w-lg mx-auto perspective-card shadow-2xl" style="min-height: 500px;">
            <!-- Card with parallax effect -->
            <div class="relative bg-white rounded-xl overflow-hidden transform-3d h-full">
              <!-- Header part -->
              <div class="bg-secondary p-5 text-white">
                <h3 class="text-xl font-bold mb-1">Instant Rate Calculator</h3>
                <p class="text-sm text-white/80">See your estimated monthly payment</p>
              </div>

              <!-- Calculator form -->
              <div class="p-4 pt-3 relative min-h-[450px]"> <!-- Increased min-height for consistency -->
                <!-- Loading Overlay -->
                <div v-if="isLoadingRates" class="absolute inset-0 bg-white/90 backdrop-blur-sm z-10 flex flex-col items-center justify-center rounded-b-xl" style="min-height: 450px;">
                  <svg class="animate-spin h-8 w-8 text-primary mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p class="text-gray-600 font-medium">Loading Rates...</p>
                </div>

                <!-- Error State - Only shown when there's an error -->
                <div v-if="!isLoadingRates && loadingErrorRates" class="absolute inset-0 bg-white/95 z-10 flex flex-col items-center justify-center text-center px-4 rounded-b-xl" style="min-height: 450px;">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-red-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p class="text-red-600 font-medium mb-2">Error Loading Rates</p>
                    <p class="text-sm text-gray-600">{{ loadingErrorRates }}</p>
                    <button @click="fetchInterestRates" class="mt-4 btn btn-outline text-sm py-1 px-3">
                      Try Again
                    </button>
                </div>

                <!-- Form (only shown if no error and not loading) -->
                <form
                  v-if="!isLoadingRates && !loadingErrorRates"
                  @submit.prevent="calculateFinancePayment"
                  class="space-y-3 transition-opacity duration-300"
                  style="min-height: 450px;"
                >
                  <div>
                    <label for="vehiclePriceCalc" class="block text-sm font-medium text-gray-700 mb-1">Vehicle Price</label>
                    <div class="relative">
                      <span class="absolute inset-y-0 left-0 pl-2 flex items-center text-gray-500">$</span>
                      <input type="text" inputmode="decimal" pattern="[0-9]*[.,]?[0-9]*" id="vehiclePriceCalc" v-model="vehiclePriceInput" class="w-full px-3 py-2 pl-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary" placeholder="  e.g., 50000" required>
                    </div>
                  </div>

                  <div>
                    <label for="downPaymentCalc" class="block text-sm font-medium text-gray-700 mb-1">Down Payment</label>
                    <div class="relative">
                      <span class="absolute inset-y-0 left-0 pl-2 flex items-center text-gray-500">$</span>
                      <input type="text" inputmode="decimal" pattern="[0-9]*[.,]?[0-9]*" id="downPaymentCalc" v-model="downPaymentInput" class="w-full px-3 py-2 pl-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary" placeholder="  e.g., 10000" required>
                    </div>
                  </div>

                  <div>
                    <label for="termLengthCalc" class="block text-sm font-medium text-gray-700 mb-1">Term Length</label>
                    <select id="termLengthCalc" v-model="termLengthInput" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary bg-white" required>
                      <option value="36">36 Months</option>
                      <option value="48">48 Months</option>
                      <option value="60">60 Months</option>
                      <option value="72">72 Months</option>
                      <option value="84">84 Months</option>
                    </select>
                  </div>

                  <div>
                    <label for="creditScoreCalc" class="block text-sm font-medium text-gray-700 mb-1">Estimated Credit Score</label>
                    <select id="creditScoreCalc" v-model="creditScoreTierInput" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary bg-white" required>
                      <option value="excellent">Excellent (720+)</option>
                      <option value="good">Good (680-719)</option>
                      <option value="fair">Fair (620-679)</option>
                      <option value="needs-help">Needs Help (below 620)</option>
                    </select>
                  </div>

                  <!-- Display Calculation Error -->
                  <div v-if="calculationError" class="pt-1 text-center text-red-600 text-sm font-medium">
                     {{ calculationError }}
                   </div>

                  <div class="pt-2">
                    <button
                      type="submit"
                      :disabled="isLoadingCalculation"
                      class="w-full btn btn-secondary py-3 relative overflow-hidden group disabled:opacity-50 disabled:cursor-not-allowed transition-opacity duration-200"
                    >
                      <span v-if="!isLoadingCalculation" class="relative z-10">Calculate Payment</span>
                      <span v-else class="relative z-10 flex items-center justify-center">
                          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"> <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle> <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path> </svg>
                          Calculating...
                      </span>
                      <span v-if="!isLoadingCalculation" class="absolute inset-0 bg-gray-800 transform scale-x-0 origin-left group-hover:scale-x-100 transition-transform duration-300"></span>
                    </button>
                  </div>

                  <div class="text-center text-xs bg-gray-50 p-2 rounded-md border border-gray-200 mt-2">
                    <div class="flex items-center justify-center mb-1">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span class="font-medium text-gray-600">Important Note</span>
                    </div>
                    <p class="text-gray-500">This is an estimate. Final rates subject to credit approval. Excludes taxes & fees.</p>
                  </div>
                </form> <!-- End of form -->
              </div>

              <!-- Floating geometric elements for 3D effect -->
              <div class="absolute -bottom-6 -right-6 w-24 h-24 bg-secondary/20 rounded-full -z-10"></div>
              <div class="absolute -top-6 -left-6 w-16 h-16 bg-primary/20 rounded-full -z-10"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Overlay -->
     <Transition name="payment-overlay">
        <div
          v-if="showPaymentOverlay"
          class="fixed inset-0 flex items-center justify-center z-50"
          @click="showPaymentOverlay = false"
        >
          <!-- Backdrop -->
          <div class="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
          <!-- Payment Card -->
          <div
            class="relative bg-white rounded-xl shadow-2xl p-8 max-w-md w-full mx-4"
            @click.stop
          >
            <button @click="showPaymentOverlay = false" class="absolute top-3 right-3 text-gray-400 hover:text-gray-600 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /> </svg>
            </button>
            <div class="text-center">
              <h3 class="text-xl font-bold mb-6 text-gray-800">Estimated Monthly Payment</h3>
              <div class="mb-6">
                <div class="payment-amount-container relative inline-block">
                  <span class="text-4xl md:text-5xl font-bold text-secondary payment-amount">
                    ${{ calculatedPayment }}
                  </span>
                </div>
              </div>
              <p class="text-gray-600 mb-2">Based on {{ termLengthInput }} months</p>
              <p class="text-sm text-gray-500">
                Vehicle Price: ${{ parseFloat(vehiclePriceInput.replace(/[^0-9.]/g, '') || 0).toLocaleString() }} | Down Payment: ${{ parseFloat(downPaymentInput.replace(/[^0-9.]/g, '') || 0).toLocaleString() }}
              </p>

              <!-- Disclaimer in payment overlay -->
              <div class="mt-4 mb-2 text-xs bg-gray-50 p-2 rounded-md border border-gray-200">
                <div class="flex items-center justify-center mb-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="font-medium text-gray-600">Important Note</span>
                </div>
                <p class="text-gray-500">This is an estimate. Final rates subject to credit approval. Excludes taxes & fees.</p>
              </div>

              <button @click="showPaymentOverlay = false" class="mt-4 btn btn-secondary py-3 px-8 inline-block">
                Got it
              </button>
            </div>
          </div>
        </div>
      </Transition>

    <!-- Stats bar at bottom - with absolute positioning to prevent shifting -->
    <div class="absolute bottom-0 left-0 right-0 py-4 bg-black/20 backdrop-blur-sm z-10">
      <div class="container-custom">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center px-4 sm:px-0">
          <div class="text-white counter-stat">
            <div class="text-2xl md:text-3xl font-bold"><span class="animated-counter" data-target="93">0</span>%</div>
            <div class="text-sm text-white/70">Approval Rate</div>
          </div>
          <div class="text-white counter-stat">
            <div class="text-2xl md:text-3xl font-bold"><span class="animated-counter" data-target="15">0</span> min</div>
            <div class="text-sm text-white/70">Average Application Time</div>
          </div>
          <div class="text-white counter-stat">
            <div class="text-2xl md:text-3xl font-bold">$<span class="animated-counter" data-target="45">0</span>K+</div>
            <div class="text-sm text-white/70">Average Financing Amount</div>
          </div>
          <div class="text-white counter-stat">
            <div class="text-2xl md:text-3xl font-bold"><span class="animated-counter" data-target="30">0</span>+</div>
            <div class="text-sm text-white/70">Lending Partners</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Financing Options Section -->
  <section id="options" class="section bg-light border-t border-gray-200">
    <div class="container-custom">
      <div class="text-center mb-10 md:mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 md:mb-6">Financing Options</h2>
        <p class="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto px-4">
          We offer a variety of financing solutions tailored to meet your needs and budget. Explore your options below.
        </p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 px-4 sm:px-0">
        <div v-for="(option, index) in financingOptions" :key="index" class="financing-option-card bg-white rounded-xl shadow-custom relative overflow-hidden" @mouseenter="hoveredOption = index" @mouseleave="hoveredOption = null" @click="hoveredOption = hoveredOption === index ? null : index">
          <div class="absolute inset-0 bg-white z-10 transition-opacity duration-300 pointer-events-none" :class="{ 'opacity-90': hoveredOption === index, 'opacity-0': hoveredOption !== index }"></div>
          <div class="absolute inset-0 z-20 flex flex-col items-center justify-center p-4 transition-opacity duration-300 pointer-events-none" :class="{ 'opacity-100': hoveredOption === index, 'opacity-0': hoveredOption !== index }">
            <h4 class="text-sm font-bold mb-3 text-secondary">Features:</h4>
            <ul class="space-y-2 max-w-xs mx-auto">
              <li v-for="(feature, featureIndex) in option.features" :key="featureIndex" class="flex items-start feature-item" :style="{ 'transition-delay': `${featureIndex * 0.05}s` }">
                <svg class="h-4 w-4 text-secondary mt-0.5 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /> </svg>
                <span class="text-xs md:text-sm text-gray-800">{{ feature }}</span>
              </li>
            </ul>
          </div>
          <div class="option-image-container mb-4 relative z-0"> <img :src="option.image" :alt="option.title" class="transition-transform duration-500" /> </div>
          <div class="p-4 md:p-6 flex-grow flex flex-col relative z-0">
            <h3 class="text-xl font-bold mb-2">{{ option.title }}</h3>
            <p class="text-sm text-gray-600 mb-4">{{ option.description }}</p>
            <div class="mt-auto">
              <button class="mt-4 text-sm font-medium text-secondary flex items-center focus:outline-none">
                <span>{{ hoveredOption === index ? 'Show Less' : 'Learn More' }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transition-transform duration-300" :class="{ 'transform rotate-180': hoveredOption === index }" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /> </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Benefits Section -->
  <section class="section bg-white">
    <div class="container-custom">
      <div class="text-center mb-10 md:mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 md:mb-6">Why Finance With Us</h2>
        <p class="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto px-4">
          Experience the GT Motorsports advantage with our streamlined financing process and customer-focused approach.
        </p>
      </div>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 px-4 sm:px-0">
        <div v-for="(benefit, index) in benefits" :key="index" :ref="el => { if (el) benefitsCards[index] = el }" class="bg-light rounded-lg p-4 md:p-6 shadow-custom transition-all duration-300 hover:shadow-lg hover:-translate-y-1 active:shadow-lg active:-translate-y-1 group benefit-card opacity-0">
          <div class="bg-primary/10 p-2 md:p-3 rounded-full w-12 h-12 md:w-14 md:h-14 flex items-center justify-center mb-3 md:mb-4 group-hover:bg-secondary/20 transition-colors duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 md:h-8 md:w-8 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="benefit.icon" /> </svg>
          </div>
          <h3 class="text-lg md:text-xl font-bold mb-2 md:mb-3">{{ benefit.title }}</h3>
          <p class="text-sm md:text-base text-gray-600">{{ benefit.description }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Application Section -->
  <section id="apply" ref="applicationSection" class="section bg-light border-t border-gray-200">
    <div class="container-custom">
      <div class="text-center mb-10 md:mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 md:mb-6">Apply for Financing</h2>
        <p class="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto px-4">
          Get pre-approved quickly with our simple application process. We work with multiple lenders to find you the best rates.
        </p>
      </div>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-start">
        <!-- Application Form -->
        <div class="bg-white rounded-xl shadow-custom p-6 md:p-8 form-container opacity-0 mx-auto w-full max-w-lg">
          <h3 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">Pre-Qualification Form</h3>

          <!-- Success Message -->
          <div v-if="formSubmitSuccess" class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md mb-6">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span class="font-medium">Thank you for your submission!</span>
            </div>
            <p class="mt-1 text-sm">We've received your pre-qualification request and will contact you shortly.</p>
          </div>

          <!-- Error Message -->
          <div v-if="formSubmitError" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
              <span class="font-medium">There was a problem with your submission.</span>
            </div>
            <p class="mt-1 text-sm">{{ formSubmitError }}</p>

            <!-- Additional guidance for RLS policy issues -->
            <div v-if="formSubmitError.includes('Row Level Security')" class="mt-3 p-3 bg-blue-50 border border-blue-200 text-blue-700 rounded">
              <p class="text-sm font-medium">Administrator Action Required:</p>
            </div>
          </div>

          <form v-if="!formSubmitSuccess" @submit.prevent="submitFinancingForm" class="space-y-4 md:space-y-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1" for="firstName">First Name</label>
                <input
                  type="text"
                  id="firstName"
                  v-model="financingForm.first_name"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary"
                  placeholder="Enter first name"
                  required
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1" for="lastName">Last Name</label>
                <input
                  type="text"
                  id="lastName"
                  v-model="financingForm.last_name"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary"
                  placeholder="Enter last name"
                  required
                />
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1" for="email">Email Address</label>
              <input
                type="email"
                id="email"
                v-model="financingForm.email"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary"
                placeholder="Enter email address"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1" for="phone">Phone Number</label>
              <input
                type="tel"
                id="phone"
                v-model="financingForm.phone_number"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary"
                placeholder="(XXX) XXX-XXXX"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1" for="vehicleInterest">Vehicle of Interest (Optional)</label>
              <input
                type="text"
                id="vehicleInterest"
                v-model="financingForm.vehicle_of_interest"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary"
                placeholder="Year, Make, Model"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Financing Type</label>
              <div class="grid grid-cols-2 gap-3">
                <div class="flex items-center">
                  <input
                    type="radio"
                    id="purchase"
                    name="financingType"
                    value="Purchase"
                    v-model="financingForm.financing_type"
                    class="h-4 w-4 text-secondary focus:ring-secondary"
                  />
                  <label for="purchase" class="ml-2 text-sm text-gray-700">Purchase</label>
                </div>
                <div class="flex items-center">
                  <input
                    type="radio"
                    id="lease"
                    name="financingType"
                    value="Lease"
                    v-model="financingForm.financing_type"
                    class="h-4 w-4 text-secondary focus:ring-secondary"
                  />
                  <label for="lease" class="ml-2 text-sm text-gray-700">Lease</label>
                </div>
              </div>
            </div>
            <div class="pt-2">
              <button
                type="submit"
                class="w-full btn btn-secondary py-2 md:py-3"
                :disabled="isSubmittingForm"
              >
                <span v-if="!isSubmittingForm">Submit Pre-Qualification</span>
                <span v-else class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Submitting...
                </span>
              </button>
              <p class="text-xs text-gray-500 mt-2 text-center">
                This is a secure, no-obligation pre-qualification that won't affect your credit score.
              </p>
            </div>
          </form>
          <div class="mt-6 md:mt-8">
            <h4 class="text-base md:text-lg font-bold mb-3">Required Documents</h4>
            <ul class="space-y-2">
              <li v-for="(document, index) in requiredDocuments" :key="index" class="flex items-start">
                <svg class="h-4 w-4 md:h-5 md:w-5 text-secondary mt-0.5 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /> </svg>
                <span class="text-sm text-gray-600">{{ document }}</span>
              </li>
            </ul>
          </div>
        </div>
        <!-- Financing Process (Right Column) -->
        <div class="process-steps opacity-0">
          <h3 class="text-xl md:text-2xl font-bold mb-6 md:mb-8 text-center lg:text-left">Our Financing Process</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
             <!-- Individual Step Cards -->
            <div v-for="(step, index) in financingProcess" :key="index" class="bg-white rounded-lg p-4 md:p-6 shadow-custom transition-all duration-300 hover:shadow-lg hover:-translate-y-1 active:shadow-lg active:-translate-y-1 group step-card opacity-0">
              <div class="flex items-start">
                <div class="bg-primary/10 p-2 md:p-3 rounded-full w-10 h-10 md:w-12 md:h-12 flex items-center justify-center mr-3 md:mr-4 group-hover:bg-secondary/20 transition-colors duration-300 flex-shrink-0">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="step.icon" /> </svg>
                </div>
                <div> <h4 class="text-base md:text-lg font-bold mb-1">{{ step.title }}</h4> <p class="text-sm text-gray-600">{{ step.description }}</p> </div>
              </div>
            </div>
          </div>
          <!-- Additional Info -->
          <div class="mt-8 md:mt-10 bg-primary/5 rounded-lg p-4 md:p-6 border border-primary/10">
            <h4 class="text-base md:text-lg font-bold mb-3 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg>
              Need Assistance?
            </h4>
            <p class="text-sm md:text-base text-gray-600 mb-4"> Our finance specialists are available to help you through every step of the financing process. </p>
            <div class="flex flex-wrap gap-3">
              <a href="tel:+14034022015" class="btn btn-outline flex items-center text-sm py-2 px-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" /> </svg>
                Call Us
              </a>
              <router-link to="/contact" class="btn btn-outline flex items-center text-sm py-2 px-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" /> </svg>
                Contact Us
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="section bg-white">
    <div class="container-custom">
      <div class="text-center mb-10 md:mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 md:mb-6">Frequently Asked Questions</h2>
        <p class="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto px-4">
          Find answers to common questions about our financing options and application process.
        </p>
      </div>

      <!-- Loading State -->
      <div v-if="isLoadingFaqs" class="text-center py-10">
        <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-12 w-12 mx-auto mb-3" />
        <p class="text-gray-500">Loading FAQ information...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="loadingErrorFaqs" class="max-w-3xl mx-auto px-4 sm:px-0 text-center py-6">
        <div class="bg-red-50 text-red-600 p-4 rounded-lg">
          <p>{{ loadingErrorFaqs }}</p>
          <p class="mt-2 text-sm">Please try refreshing the page.</p>
        </div>
      </div>

      <!-- FAQ Content -->
      <div v-else class="max-w-3xl mx-auto px-4 sm:px-0">
        <div class="space-y-3 md:space-y-4">
          <div v-for="(faq, index) in faqItems" :key="index" class="bg-light rounded-lg overflow-hidden shadow-sm">
            <button @click="toggleFaq(index)" class="w-full flex justify-between items-center p-4 md:p-5 text-left focus:outline-none">
              <span class="font-medium text-base md:text-lg">{{ faq.question }}</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary transition-transform duration-300" :class="faq.isOpen ? 'transform rotate-180' : ''" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /> </svg>
            </button>
            <div class="overflow-hidden transition-all duration-300 ease-in-out" :style="{ maxHeight: faq.isOpen ? '500px' : '0px' }">
              <div class="p-4 md:p-5 pt-0 text-sm md:text-base text-gray-600 border-t border-gray-200"> {{ faq.answer }} </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section class="section bg-light overflow-hidden border-t border-gray-200">
    <div class="container-custom">
      <div class="text-center mb-10 md:mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 md:mb-6">Customer Success Stories</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Hear from our customers who have successfully financed their dream vehicles through GT Motorsports.
        </p>
      </div>
      <div class="relative">
        <div class="relative overflow-hidden testimonial-container">
          <div class="absolute left-0 top-0 bottom-0 w-12 md:w-24 z-10 bg-gradient-to-r from-light to-transparent pointer-events-none"></div>
          <div class="absolute right-0 top-0 bottom-0 w-12 md:w-24 z-10 bg-gradient-to-l from-light to-transparent pointer-events-none"></div>
          <div ref="testimonialContainer" class="testimonial-scroll" :class="{ 'pause-animation': isTestimonialAnimationPaused }" @mouseenter="pauseTestimonialAnimation" @mouseleave="resumeTestimonialAnimation" @touchstart="pauseTestimonialAnimation" @touchend="resumeTestimonialAnimation">
            <!-- Original Testimonials -->
            <div v-for="(testimonial, index) in testimonials" :key="index" class="bg-white rounded-lg p-4 md:p-6 shadow-custom relative flex-shrink-0 w-[85vw] sm:w-[320px] md:w-80 mx-2 md:mx-3 transition-all duration-300 hover:shadow-lg active:shadow-lg">
              <!-- Improved quotation mark styling -->
              <div class="absolute top-4 right-4 md:top-6 md:right-6">
                <div class="quote-mark">
                  <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path>
                    <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path>
                  </svg>
                </div>
              </div>
              <p class="text-sm md:text-base text-gray-600 mb-4 md:mb-6 relative z-10">{{ testimonial.quote }}</p>
              <div class="flex items-center">
                <div class="mr-3 md:mr-4"> <div class="w-10 h-10 md:w-12 md:h-12 bg-secondary/20 rounded-full flex items-center justify-center text-secondary font-bold"> {{ testimonial.name.charAt(0) }} </div> </div>
                <div> <h4 class="font-bold text-sm md:text-base">{{ testimonial.name }}</h4> <p class="text-xs md:text-sm text-gray-500">{{ testimonial.vehicle }}</p> </div>
              </div>
              <div class="mt-3 md:mt-4 flex"> <svg v-for="star in testimonial.rating" :key="star" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor"> <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" /> </svg> </div>
            </div>
            <!-- Cloned Testimonials for Seamless Loop -->
            <div v-for="i in Math.min(3, testimonials.length)" :key="`clone-${i}`" class="bg-white rounded-lg p-4 md:p-6 shadow-custom relative flex-shrink-0 w-[85vw] sm:w-[320px] md:w-80 mx-2 md:mx-3 transition-all duration-300 hover:shadow-lg active:shadow-lg" aria-hidden="true">
              <!-- Improved quotation mark styling (cloned) -->
              <div class="absolute top-4 right-4 md:top-6 md:right-6">
                <div class="quote-mark">
                  <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path>
                    <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path>
                  </svg>
                </div>
              </div>
              <p class="text-sm md:text-base text-gray-600 mb-4 md:mb-6 relative z-10">{{ testimonials[i - 1]?.quote }}</p>
              <div class="flex items-center">
                <div class="mr-3 md:mr-4"> <div class="w-10 h-10 md:w-12 md:h-12 bg-secondary/20 rounded-full flex items-center justify-center text-secondary font-bold"> {{ testimonials[i - 1]?.name?.charAt(0) }} </div> </div>
                <div> <h4 class="font-bold text-sm md:text-base">{{ testimonials[i - 1]?.name }}</h4> <p class="text-xs md:text-sm text-gray-500">{{ testimonials[i - 1]?.vehicle }}</p> </div>
              </div>
              <div class="mt-3 md:mt-4 flex"> <svg v-for="star in testimonials[i - 1]?.rating" :key="star" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor"> <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" /> </svg> </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="relative w-full py-16 md:py-20 bg-white overflow-hidden">
    <!-- Simple solid background -->
    <div class="absolute inset-0 z-0 bg-primary"></div>

    <!-- Content container -->
    <div class="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-col items-center justify-center text-center">
        <!-- Simple decorative element -->
        <div class="w-16 h-1 bg-secondary mb-6 rounded-full"></div>

        <!-- Heading with enhanced styling -->
        <h2 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 md:mb-5 text-white leading-tight">
          Ready to Get <span class="text-secondary">Started</span>?
        </h2>

        <!-- Subheading with improved readability -->
        <p class="text-base sm:text-lg text-white/90 max-w-2xl mx-auto mb-8">
          Apply for financing today and take the first step toward driving your dream vehicle.
        </p>

        <!-- Buttons with improved layout and styling -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center w-full max-w-md mx-auto">
          <a
            href="#apply"
            class="btn btn-secondary text-base py-3 px-6 font-medium w-full sm:w-auto"
          >
            Apply Now
          </a>

          <a
            href="tel:+14034022015"
            class="btn btn-outline border-2 border-white text-white hover:bg-white hover:text-primary flex items-center justify-center text-base py-3 px-6 font-medium w-full sm:w-auto"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            Call (*************
          </a>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
/* Base Animation Keyframes */
@keyframes fadeInDown { from { opacity: 0; transform: translateY(-25px); } to { opacity: 1; transform: translateY(0); } }
@keyframes fadeInUp { from { opacity: 0; transform: translateY(25px); } to { opacity: 1; transform: translateY(0); } }
@keyframes fadeInLeft { from { opacity: 0; transform: translateX(-30px); } to { opacity: 1; transform: translateX(0); } }
@keyframes fadeInRight { from { opacity: 0; transform: translateX(30px); } to { opacity: 1; transform: translateX(0); } }
@keyframes pulse { 0% { opacity: 0.6; } 50% { opacity: 0.8; } 100% { opacity: 0.6; } }
@keyframes numberCountUp { 0% { opacity: 0; transform: translateY(20px) scale(0.8); } 100% { opacity: 1; transform: translateY(0) scale(1); } }
@keyframes lineGrow { 0% { transform: scaleX(0); } 100% { transform: scaleX(1); } }
@keyframes scroll { 0% { transform: translateX(0); } 100% { transform: translateX(calc((var(--testimonial-width-md, 320px) + var(--testimonial-gap, 1rem)) * -11)); } } /* Assume 8 original + 3 clones */

/* Animation Classes */
.animate-fade-in-down { animation: fadeInDown 0.8s ease-out forwards; }
.animate-fade-in-up { animation: fadeInUp 0.8s ease-out forwards; }
.animate-fade-in-left { animation: fadeInLeft 0.8s ease-out forwards; }
.animate-fade-in-right { animation: fadeInRight 0.8s ease-out forwards; }

/* Initial Animation States - with dimensions preserved */
.form-container, .process-steps, .benefit-card, .step-card { opacity: 0; visibility: visible; }
/* No need for .benefit-card.animate-fade-in-up etc. here, applied dynamically */

/* Hero Section Styling */
.bg-primary-dark { background-color: #0f2942; } /* Define if not in Tailwind config */
.bg-white\/10 { background-color: rgba(255, 255, 255, 0.1); }
.animated-text { opacity: 0; transform: translateY(20px); transition: opacity 0.8s ease, transform 0.8s ease; will-change: opacity, transform; display: inline-block; height: auto; }
.animate-fade-in-down .animated-text { opacity: 1; transform: translateY(0); } /* Trigger text animation with parent */
.perspective-container { perspective: 1000px; transform-style: preserve-3d; }
.perspective-card { transform-style: preserve-3d; transition: transform 0.3s ease; min-height: 500px; /* Increased card height */ display: flex; flex-direction: column; }
.perspective-card > .transform-3d { flex-grow: 1; display: flex; flex-direction: column; }
.perspective-card > .transform-3d > div:last-child { flex-grow: 1; } /* Ensure form area fills space */
.transform-3d { transform-style: preserve-3d; transition: transform 0.3s ease; }
.-z-10 { z-index: -10; }

/* Particles Canvas */
canvas[ref="particlesCanvas"] { position: absolute !important; top: 0 !important; left: 0 !important; right: 0 !important; bottom: 0 !important; width: 100% !important; height: 100% !important; z-index: 0 !important; pointer-events: auto !important; display: block !important; }

/* Financing Options Card Styling */
.financing-option-card { transition: box-shadow 0.4s ease; overflow: hidden; height: 100%; cursor: pointer; display: flex; flex-direction: column; position: relative; }
.financing-option-card:hover { box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.option-image-container { position: relative; overflow: hidden; border-radius: 0.5rem; height: 160px; width: 100%; }
@media (min-width: 768px) { .option-image-container { height: 180px; } }
.option-image-container img { width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s ease; transform-origin: center; }
.feature-item { opacity: 0; transform: translateY(10px); transition: opacity 0.3s ease, transform 0.3s ease; }
.financing-option-card:hover .feature-item { opacity: 1; transform: translateY(0); }
/* Stagger Feature Items */
.feature-item:nth-child(1) { transition-delay: 0.05s; }
.feature-item:nth-child(2) { transition-delay: 0.1s; }
.feature-item:nth-child(3) { transition-delay: 0.15s; }
.feature-item:nth-child(4) { transition-delay: 0.2s; }
.feature-item:nth-child(5) { transition-delay: 0.25s; }
@media (min-width: 768px) {
  .feature-item:nth-child(1) { transition-delay: 0.1s; }
  .feature-item:nth-child(2) { transition-delay: 0.15s; }
  .feature-item:nth-child(3) { transition-delay: 0.2s; }
  .feature-item:nth-child(4) { transition-delay: 0.25s; }
  .feature-item:nth-child(5) { transition-delay: 0.3s; }
}

/* FAQ Styling */
.faq-item .max-h-96 { max-height: 500px; /* Ensure enough height */ }

/* Testimonial Styling */
.scrollbar-hide::-webkit-scrollbar { display: none; }
.scrollbar-hide { -ms-overflow-style: none; scrollbar-width: none; }
.testimonial-container { --testimonial-width-mobile: 85vw; --testimonial-width-sm: 320px; --testimonial-width-md: 320px; --testimonial-gap: 1rem; --scroll-duration: 60s; width: 100%; overflow: hidden; position: relative; padding: 0.5rem 0; }
@media (max-width: 639px) { .testimonial-container { --testimonial-gap: 0.5rem; --scroll-duration: 50s; } @keyframes scroll { 0% { transform: translateX(0); } 100% { transform: translateX(calc((var(--testimonial-width-mobile) + var(--testimonial-gap)) * -11)); } } }
@media (min-width: 640px) { .testimonial-container { --scroll-duration: 70s; } }
.testimonial-scroll { display: flex; flex-wrap: nowrap; gap: var(--testimonial-gap); padding-bottom: 1rem; margin: 0; animation: scroll var(--scroll-duration) linear infinite; width: fit-content; touch-action: pan-x; }
.testimonial-scroll.pause-animation { animation-play-state: paused; }
.testimonial-scroll > div { flex: 0 0 auto; width: var(--testimonial-width-mobile); transition: transform 0.3s ease, box-shadow 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
@media (min-width: 640px) { .testimonial-scroll > div { width: var(--testimonial-width-sm); } } @media (min-width: 768px) { .testimonial-scroll > div { width: var(--testimonial-width-md); } }
.testimonial-scroll > div:hover, .testimonial-scroll > div:active { transform: translateY(-5px); box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Quote mark styling */
.quote-mark {
  position: relative;
  color: var(--color-secondary);
  opacity: 0.15;
  transform: rotate(180deg);
  transition: all 0.3s ease;
}

.quote-mark svg {
  filter: drop-shadow(0px 0px 1px rgba(0, 0, 0, 0.2));
}

/* Hover effect for quote marks */
.testimonial-scroll > div:hover .quote-mark {
  opacity: 0.25;
  transform: rotate(180deg) scale(1.1);
}

/* Payment Overlay Animation */
.payment-overlay-enter-active,
.payment-overlay-leave-active {
  transition: opacity 0.3s ease;
}
.payment-overlay-enter-active .relative.bg-white, /* Target the inner card */
.payment-overlay-leave-active .relative.bg-white {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy effect */
}
.payment-overlay-enter-from,
.payment-overlay-leave-to {
  opacity: 0;
}
.payment-overlay-enter-from .relative.bg-white,
.payment-overlay-leave-to .relative.bg-white {
  opacity: 0;
  transform: scale(0.9);
}
.payment-amount { display: inline-block; animation: numberCountUp 1.2s cubic-bezier(0.16, 1, 0.3, 1) forwards; }
.payment-amount-container::after { content: ''; position: absolute; bottom: -8px; left: 0; width: 100%; height: 4px; background-color: var(--color-secondary, #38bdf8); transform: scaleX(0); transform-origin: left; animation: lineGrow 1s ease-out 0.5s forwards; }

/* Utilities */
.pointer-events-none { pointer-events: none; }
.animated-counter { display: inline-block; }
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button { -webkit-appearance: none; margin: 0; }
input[type=number] { -moz-appearance: textfield; }

/* Focus States */
input:focus, select:focus, button:focus, a:focus { outline: 2px solid transparent; outline-offset: 2px; box-shadow: 0 0 0 2px var(--color-primary, #1d4ed8), 0 0 0 4px rgba(59, 130, 246, 0.4); }
.btn:focus { outline: 2px solid transparent; outline-offset: 2px; box-shadow: 0 0 0 2px var(--color-primary, #1d4ed8), 0 0 0 4px rgba(59, 130, 246, 0.4); }
.btn-secondary:focus { box-shadow: 0 0 0 2px var(--color-secondary, #38bdf8), 0 0 0 4px rgba(56, 189, 248, 0.4); }
.btn-outline:focus { box-shadow: 0 0 0 2px var(--color-primary, #1d4ed8), 0 0 0 4px rgba(59, 130, 246, 0.4); }
.btn-outline.border-white:focus { box-shadow: 0 0 0 2px white, 0 0 0 4px rgba(255, 255, 255, 0.4); }

/* FAQ Transition Max Height Adjustment */
.faq-item .transition-all {
    transition-property: max-height, opacity; /* Add opacity transition */
    transition-duration: 300ms;
    transition-timing-function: ease-in-out;
}
</style>