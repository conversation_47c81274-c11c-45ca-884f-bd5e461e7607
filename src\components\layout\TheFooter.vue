<script setup>
import { ref, computed, onMounted } from 'vue';
import BusinessInfoProvider from '../BusinessInfoProvider.vue';
import stylingStore from '../../store/styling';

const currentYear = new Date().getFullYear();
const email = ref('');

// Initialize styling store if not already initialized
onMounted(() => {
  if (!stylingStore.styling.media) {
    stylingStore.initStore();
  }
});

// Get styling from store
const styling = computed(() => stylingStore.styling);

// Helper function to determine if a link is internal or external
const isExternalLink = (path) => {
  return path.startsWith('http') || path.startsWith('tel:');
};

const footerLinks = [
  {
    title: 'Quick Links',
    links: [
      { name: 'Home', path: '/' },
      { name: 'Inventory', path: '/inventory' },
      { name: 'Contact', path: '/contact' },
      { name: 'Financing', path: '/financing' },
    ]
  },
  {
    title: 'Services',
    links: [
      { name: 'Vehicle Sales', path: '/services#sales' },
      { name: 'Vehicle Financing', path: '/financing' },
      { name: 'Vehicle Maintenance', path: '/services#maintenance' },
      { name: 'Trade-In Appraisal', path: '/services#trade-in' },
      { name: 'Custom Orders', path: '/services#custom-orders' },
    ]
  },
  {
    title: 'Contact Us',
    links: [
      { name: '40 Hopewell Way NE, Unit 10, Calgary, AB, T3J 5H7', path: 'https://maps.google.com' },
      { name: '************', path: 'tel:+14034022015' },
      { name: 'Mon - Thurs: 10:00 AM - 7:00 PM', path: '/contact' },
      { name: 'Fri - Sat: 10:00 AM - 6:00 PM', path: '/contact' },
      { name: 'Sun: 10:00 AM - 3:00 PM', path: '/contact' },
    ]
  }
];

// Social media icons
const socialIcons = {
  facebook: 'M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z',
  instagram: 'M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z',
  twitter: 'M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84',
  youtube: 'M23.498 6.186a3.016 3.016 0 00-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 00.502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 002.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 002.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z',
  linkedin: 'M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'
};

// Helper function to ensure URLs are properly formatted
const formatSocialUrl = (url, defaultDomain) => {
  if (!url) return '';

  // If URL doesn't start with http:// or https://, add the default domain
  if (!url.match(/^https?:\/\//i)) {
    // Check if the URL already contains the domain
    if (url.includes(defaultDomain.replace(/^https?:\/\//i, ''))) {
      return `https://${url}`;
    } else {
      // If it's just a username or handle, prepend the full domain
      return `https://${defaultDomain}/${url.replace(/^[@/]+/, '')}`;
    }
  }

  return url;
};

// Function to get social media links with fallbacks
const getSocialLinks = (socialData) => {
  if (!socialData) return [];

  const links = [];

  // Add Facebook if available
  if (socialData.facebook) {
    links.push({
      name: 'Facebook',
      icon: socialIcons.facebook,
      url: formatSocialUrl(socialData.facebook, 'facebook.com')
    });
  }

  // Add Instagram if available
  if (socialData.instagram) {
    links.push({
      name: 'Instagram',
      icon: socialIcons.instagram,
      url: formatSocialUrl(socialData.instagram, 'instagram.com')
    });
  }

  // Add Twitter if available
  if (socialData.twitter) {
    links.push({
      name: 'Twitter',
      icon: socialIcons.twitter,
      url: formatSocialUrl(socialData.twitter, 'twitter.com')
    });
  }

  // Add YouTube if available
  if (socialData.youtube) {
    links.push({
      name: 'YouTube',
      icon: socialIcons.youtube,
      url: formatSocialUrl(socialData.youtube, 'youtube.com')
    });
  }

  // Add LinkedIn if available
  if (socialData.linkedin) {
    links.push({
      name: 'LinkedIn',
      icon: socialIcons.linkedin,
      url: formatSocialUrl(socialData.linkedin, 'linkedin.com')
    });
  }

  return links;
};

const handleSubscribe = (businessEmail) => {
  // In a real app, this would send the email to a backend service
  if (email.value) {
    // If we have a business email, we would send the subscription to that email
    const recipient = businessEmail || '<EMAIL>';
    console.log(`Sending subscription request to ${recipient} for email: ${email.value}`);

    alert(`Thank you for subscribing with ${email.value}!`);
    email.value = '';
  }
};
</script>

<template>
  <BusinessInfoProvider v-slot="{
    title,
    address,
    phone,
    email: businessEmail,
    hours,
    social,
    isLoading
  }">
    <footer class="relative bg-gradient-to-b from-dark to-black text-white pt-20 pb-10 overflow-hidden">
      <!-- Luxury background pattern -->
      <div class="absolute inset-0 opacity-5" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>

      <div class="container-custom relative z-10">
        <!-- Main Footer Content -->
        <div class="grid grid-cols-1 md:grid-cols-12 gap-x-8 gap-y-12 mb-16">
          <!-- Logo and About -->
          <div class="md:col-span-4">
            <div class="mb-6">
              <!-- Show uploaded logo if available -->
              <div v-if="styling.media.logo" class="mb-2">
                <img :src="styling.media.logo" alt="GT Motor Sports" class="h-12" />
              </div>
              <!-- Otherwise show text logo -->
              <span v-else class="text-3xl font-heading font-bold tracking-tight">
                <span v-if="!isLoading" class="flex items-center">
                  <span class="bg-secondary text-white px-2 py-1 mr-2">GT</span>
                  <span class="text-white">{{ title ? title.replace('GT', '') : 'Motor Sports' }}</span>
                </span>
                <span v-else class="flex items-center">
                  <span class="bg-secondary text-white px-2 py-1 mr-2">GT</span>
                  <span class="text-white">Motor Sports</span>
                </span>
              </span>
            </div>
            <p class="text-gray-300 mb-8 leading-relaxed">
              Calgary's premier destination for luxury and performance vehicles.
              Experience excellence in automotive craftsmanship and service.
            </p>

            <!-- Social Links - Ferrari-inspired styling -->
            <div class="flex space-x-5">
              <template v-if="social && Object.values(social).some(link => !!link)">
                <a
                  v-for="socialLink in getSocialLinks(social)"
                  :key="socialLink.name"
                  :href="socialLink.url"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="group"
                  :aria-label="socialLink.name"
                >
                  <div class="w-10 h-10 flex items-center justify-center rounded-full bg-white/5 border border-white/10 group-hover:bg-secondary group-hover:border-secondary transition-all duration-300 transform group-hover:scale-110">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path :d="socialLink.icon" />
                    </svg>
                  </div>
                </a>
              </template>
              <template v-else>
                <!-- Fallback to default social links if none are provided -->
                <a
                  href="https://facebook.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="group"
                  aria-label="Facebook"
                >
                  <div class="w-10 h-10 flex items-center justify-center rounded-full bg-white/5 border border-white/10 group-hover:bg-secondary group-hover:border-secondary transition-all duration-300 transform group-hover:scale-110">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path :d="socialIcons.facebook" />
                    </svg>
                  </div>
                </a>
                <a
                  href="https://instagram.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="group"
                  aria-label="Instagram"
                >
                  <div class="w-10 h-10 flex items-center justify-center rounded-full bg-white/5 border border-white/10 group-hover:bg-secondary group-hover:border-secondary transition-all duration-300 transform group-hover:scale-110">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path :d="socialIcons.instagram" />
                    </svg>
                  </div>
                </a>
                <a
                  href="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="group"
                  aria-label="Twitter"
                >
                  <div class="w-10 h-10 flex items-center justify-center rounded-full bg-white/5 border border-white/10 group-hover:bg-secondary group-hover:border-secondary transition-all duration-300 transform group-hover:scale-110">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path :d="socialIcons.twitter" />
                    </svg>
                  </div>
                </a>
              </template>
            </div>
          </div>

          <!-- Footer Links - Quick Links and Services -->
          <div
            v-for="(section, index) in footerLinks.slice(0, 2)"
            :key="index"
            class="md:col-span-2"
          >
            <h3 class="text-lg font-heading font-bold mb-6 tracking-wide uppercase text-sm">{{ section.title }}</h3>
            <ul class="space-y-4">
              <li v-for="(link, linkIndex) in section.links" :key="linkIndex">
                <router-link
                  v-if="!isExternalLink(link.path)"
                  :to="link.path"
                  class="text-gray-400 hover:text-white transition-colors duration-300 text-sm tracking-wide"
                >
                  {{ link.name }}
                </router-link>
                <a
                  v-else
                  :href="link.path"
                  class="text-gray-400 hover:text-white transition-colors duration-300 text-sm tracking-wide"
                >
                  {{ link.name }}
                </a>
              </li>
            </ul>
          </div>

          <!-- Contact Us Section -->
          <div class="md:col-span-4">
            <h3 class="text-lg font-heading font-bold mb-6 tracking-wide uppercase text-sm">{{ footerLinks[2].title }}</h3>
            <ul class="space-y-5">
              <!-- Address -->
              <li class="flex items-start group">
                <div class="w-10 h-10 flex items-center justify-center rounded-full bg-white/5 border border-white/10 mr-4 group-hover:bg-secondary group-hover:border-secondary transition-all duration-300">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                </div>
                <div>
                  <router-link
                    v-if="!isExternalLink(footerLinks[2].links[0].path)"
                    :to="footerLinks[2].links[0].path"
                    class="text-gray-400 hover:text-white transition-colors duration-300 text-sm tracking-wide"
                  >
                    {{ address || footerLinks[2].links[0].name }}
                  </router-link>
                  <a
                    v-else
                    :href="`https://maps.google.com/?q=${encodeURIComponent(address || footerLinks[2].links[0].name)}`"
                    class="text-gray-400 hover:text-white transition-colors duration-300 text-sm tracking-wide"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {{ address || footerLinks[2].links[0].name }}
                  </a>
                </div>
              </li>

              <!-- Phone -->
              <li class="flex items-start group">
                <div class="w-10 h-10 flex items-center justify-center rounded-full bg-white/5 border border-white/10 mr-4 group-hover:bg-secondary group-hover:border-secondary transition-all duration-300">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                  </svg>
                </div>
                <div>
                  <router-link
                    v-if="!isExternalLink(footerLinks[2].links[1].path)"
                    :to="footerLinks[2].links[1].path"
                    class="text-gray-400 hover:text-white transition-colors duration-300 text-sm tracking-wide"
                  >
                    {{ phone || footerLinks[2].links[1].name }}
                  </router-link>
                  <a
                    v-else
                    :href="`tel:${phone || footerLinks[2].links[1].name.replace(/[^0-9+]/g, '')}`"
                    class="text-gray-400 hover:text-white transition-colors duration-300 text-sm tracking-wide"
                  >
                    {{ phone || footerLinks[2].links[1].name }}
                  </a>
                </div>
              </li>

              <!-- Hours -->
              <li class="flex items-start group">
                <div class="w-10 h-10 flex items-center justify-center rounded-full bg-white/5 border border-white/10 mr-4 group-hover:bg-secondary group-hover:border-secondary transition-all duration-300">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <span class="text-gray-300 font-medium text-sm tracking-wide">Our Hours</span>
                  <ul class="space-y-1 mt-2">
                    <li v-if="hours && hours.length > 0" v-for="day in hours" :key="day.day" class="text-gray-400 text-sm">
                      <span class="font-medium">{{ day.day }}:</span>
                      <span v-if="day.open && day.close"> {{ day.open }} - {{ day.close }}</span>
                      <span v-else> Closed</span>
                    </li>
                    <li v-else v-for="(link, linkIndex) in footerLinks[2].links.slice(2)" :key="linkIndex" class="text-gray-400 text-sm">
                      {{ link.name }}
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <!-- Newsletter - Ferrari-inspired premium design -->
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-secondary/20 to-transparent opacity-10 rounded-lg"></div>
          <div class="relative border border-white/10 rounded-lg p-8 mb-16">
            <div class="max-w-3xl mx-auto text-center">
              <h3 class="text-xl font-heading font-bold mb-3 tracking-wide">Subscribe to Our Newsletter</h3>
              <p class="text-gray-400 mb-6 text-sm max-w-xl mx-auto">Stay updated with our latest inventory arrivals, exclusive offers, and automotive insights delivered directly to your inbox.</p>
              <form @submit.prevent="() => handleSubscribe(businessEmail)" class="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
                <input
                  v-model="email"
                  type="email"
                  placeholder="Your email address"
                  class="flex-grow px-5 py-3 rounded-md bg-white/5 border border-white/10 focus:outline-none focus:border-secondary focus:bg-white/10 text-white text-sm transition-all duration-300"
                  required
                />
                <button type="submit" class="px-8 py-3 bg-secondary text-white rounded-md hover:bg-secondary/90 transition-all duration-300 text-sm font-medium tracking-wide whitespace-nowrap transform hover:translate-y-[-2px]">
                  Subscribe
                </button>
              </form>
            </div>
          </div>
        </div>

        <!-- Copyright - Ferrari-inspired clean design -->
        <div class="border-t border-white/10 pt-8 flex flex-col md:flex-row justify-between items-center text-gray-500 text-sm">
          <p>&copy; {{ currentYear }} {{ title || 'GT Motor Sports' }}. All rights reserved.</p>
          <div class="mt-4 md:mt-0 flex space-x-6">
            <a href="/privacy-policy" class="hover:text-white transition-colors duration-300">Privacy Policy</a>
            <a href="/terms" class="hover:text-white transition-colors duration-300">Terms of Service</a>
            <a href="/sitemap" class="hover:text-white transition-colors duration-300">Sitemap</a>
          </div>
        </div>
      </div>
    </footer>
  </BusinessInfoProvider>
</template>