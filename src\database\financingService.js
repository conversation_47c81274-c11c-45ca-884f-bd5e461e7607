/**
 * Supabase Service for GT Motorsports financing pre-qualifications
 * This file provides functions to interact with the Supabase 'pre_qualifications' table
 * for handling financing pre-qualification form submissions.
 */
import { supabase } from '../utils/supabase';

/**
 * Submit a financing pre-qualification form.
 * @param {object} formData - The pre-qualification form data.
 * @param {string} formData.first_name - Customer's first name.
 * @param {string} formData.last_name - Customer's last name.
 * @param {string} formData.email - Customer's email address.
 * @param {string} formData.phone_number - Customer's phone number.
 * @param {string} [formData.vehicle_of_interest] - Optional vehicle details.
 * @param {string} formData.financing_type - Type of financing ('Purchase' or 'Lease').
 * @returns {Promise<{success: boolean, data?: object, error?: string}>} - Result object with success status and data or error.
 */
export const submitPreQualification = async (formData) => {
  console.log('[Financing Service] Submitting pre-qualification form:', formData);

  try {
    // Validate required fields
    const requiredFields = ['first_name', 'last_name', 'email', 'phone_number', 'financing_type'];
    const missingFields = requiredFields.filter(field => !formData[field]);
    
    if (missingFields.length > 0) {
      console.error(`[Financing Service] Missing required fields: ${missingFields.join(', ')}`);
      return {
        success: false,
        error: `Missing required fields: ${missingFields.join(', ')}`
      };
    }

    // Validate financing_type is either 'Purchase' or 'Lease'
    if (!['Purchase', 'Lease'].includes(formData.financing_type)) {
      console.error(`[Financing Service] Invalid financing type: ${formData.financing_type}`);
      return {
        success: false,
        error: 'Financing type must be either "Purchase" or "Lease"'
      };
    }

    // Insert the pre-qualification data into the Supabase table
    const { data, error } = await supabase
      .from('pre_qualifications')
      .insert([
        {
          first_name: formData.first_name,
          last_name: formData.last_name,
          email: formData.email,
          phone_number: formData.phone_number,
          vehicle_of_interest: formData.vehicle_of_interest || null,
          financing_type: formData.financing_type
        }
      ])
      .select();

    if (error) {
      console.error('[Financing Service] Error submitting pre-qualification form:', error);
      
      // Check for RLS policy violation error
      if (error.code === '42501' || error.message.includes('violates row-level security policy')) {
        return {
          success: false,
          error: 'Permission denied: This appears to be a Supabase Row Level Security (RLS) policy issue. Please apply the SQL fixes in supabase_policy_fix.sql to your Supabase project.',
          details: error.message
        };
      }
      
      return {
        success: false,
        error: error.message || 'Failed to submit pre-qualification form'
      };
    }

    console.log('[Financing Service] Pre-qualification form submitted successfully:', data);
    return {
      success: true,
      data: data[0]
    };
  } catch (error) {
    console.error('[Financing Service] Unexpected error submitting pre-qualification form:', error);
    return {
      success: false,
      error: error.message || 'An unexpected error occurred'
    };
  }
};

/**
 * Get all pre-qualification submissions.
 * This function would typically be used in an admin interface.
 * @returns {Promise<{success: boolean, data?: Array<object>, error?: string}>} - Result object with success status and data or error.
 */
export const getAllPreQualifications = async () => {
  console.log('[Financing Service] Fetching all pre-qualification submissions');

  try {
    const { data, error } = await supabase
      .from('pre_qualifications')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('[Financing Service] Error fetching pre-qualification submissions:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch pre-qualification submissions'
      };
    }

    console.log(`[Financing Service] Successfully fetched ${data.length} pre-qualification submissions`);
    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('[Financing Service] Unexpected error fetching pre-qualification submissions:', error);
    return {
      success: false,
      error: error.message || 'An unexpected error occurred'
    };
  }
};