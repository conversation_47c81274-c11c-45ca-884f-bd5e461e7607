<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-50">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-50" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-6xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 flex justify-between items-center">
                <span>Vehicle Preview: {{ vehicle?.title }}</span>
                <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </DialogTitle>

              <div v-if="vehicle" class="mt-4 grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Image Gallery Section -->
                <div class="relative">
                  <div v-if="vehicleImages.length > 0" class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg overflow-hidden shadow-lg">
                    <img
                      :src="currentImage"
                      alt="Vehicle Image"
                      class="object-contain w-full h-full"
                      @error="$event.target.src='/images/no-image-available.jpg'"
                    />
                  </div>
                  <div v-else class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg flex items-center justify-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 opacity-30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p class="ml-2">No Image Available</p>
                  </div>

                  <!-- Navigation Buttons -->
                  <div v-if="vehicleImages.length > 1" class="absolute inset-0 flex items-center justify-between px-4">
                    <button
                      @click="prevImage"
                      :disabled="currentImageIndex === 0"
                      class="bg-black bg-opacity-60 text-white rounded-full p-3 hover:bg-opacity-80 disabled:opacity-40 disabled:cursor-not-allowed transition-all transform hover:scale-110"
                      aria-label="Previous Image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <button
                      @click="nextImage"
                      :disabled="currentImageIndex === vehicleImages.length - 1"
                      class="bg-black bg-opacity-60 text-white rounded-full p-3 hover:bg-opacity-80 disabled:opacity-40 disabled:cursor-not-allowed transition-all transform hover:scale-110"
                      aria-label="Next Image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>

                  <!-- Image Counter -->
                  <div v-if="vehicleImages.length > 1" class="absolute bottom-3 right-3 bg-black bg-opacity-70 text-white text-sm px-3 py-1.5 rounded-full font-medium">
                    {{ currentImageIndex + 1 }} / {{ vehicleImages.length }}
                  </div>

                  <!-- Thumbnail Gallery -->
                  <div v-if="vehicleImages.length > 1" class="mt-4 flex space-x-2 overflow-x-auto pb-2">
                    <button
                      v-for="(image, index) in vehicleImages"
                      :key="index"
                      @click="currentImageIndex = index"
                      class="flex-shrink-0 h-16 w-24 rounded-md overflow-hidden focus:outline-none focus:ring-2 focus:ring-primary"
                      :class="{'ring-2 ring-primary': currentImageIndex === index}"
                    >
                      <img
                        :src="image"
                        alt="Thumbnail"
                        class="h-full w-full object-cover"
                        @error="$event.target.src='/images/no-image-available.jpg'"
                      />
                    </button>
                  </div>
                </div>

                <!-- Vehicle Details Section -->
                <div class="space-y-5">
                  <!-- Title and Subtitle -->
                  <div class="border-b pb-3">
                    <h4 class="text-2xl font-bold text-gray-800">{{ vehicle.year }} {{ vehicle.make }} {{ vehicle.model }}</h4>
                    <h5 class="text-lg text-gray-600 mt-1">{{ vehicle.trim }}</h5>
                  </div>

                  <!-- Price -->
                  <div class="text-2xl font-bold text-primary bg-gray-50 p-3 rounded-lg inline-block">
                    <span v-if="vehicle.specialPrice">{{ formatPrice(vehicle.specialPrice) }}</span>
                    <span v-else>{{ formatPrice(vehicle.price) }}</span>
                    <span v-if="vehicle.specialPrice" class="ml-2 text-base font-normal text-gray-500 line-through">{{ formatPrice(vehicle.price) }}</span>
                  </div>

                  <!-- Key Details Grid -->
                  <!-- Key Details Grid -->
                  <div class="border rounded-lg overflow-hidden">
                    <div class="bg-gray-50 px-4 py-2 border-b">
                      <h6 class="font-medium text-gray-700">Vehicle Specifications</h6>
                    </div>
                    <div class="grid grid-cols-2 gap-x-4 gap-y-3 p-4 text-sm">
                      <DetailItem label="Stock #" :value="vehicle.stockNumber" />
                      <DetailItem label="VIN" :value="vehicle.vin" />
                      <DetailItem label="Mileage" :value="formatMileage(vehicle.mileage)" />
                      <DetailItem label="Body Type" :value="vehicle.bodyStyle" />
                      <DetailItem label="Engine" :value="vehicle.engine" />
                      <DetailItem label="Transmission" :value="vehicle.transmission" />
                      <DetailItem label="Drivetrain" :value="vehicle.drivetrain" />
                      <DetailItem label="Exterior Color" :value="vehicle.exteriorColor" />
                      <DetailItem label="Interior Color" :value="vehicle.interiorColor" />
                    </div>
                  </div>

                  <!-- Features -->
                  <!-- Features -->
                  <div v-if="vehicle.features && vehicle.features.length > 0" class="border rounded-lg overflow-hidden">
                    <div class="bg-gray-50 px-4 py-2 border-b">
                      <h6 class="font-medium text-gray-700">Features</h6>
                    </div>
                    <div class="p-4">
                      <ul class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 text-sm text-gray-700">
                        <!-- Assuming features is an array of strings -->
                        <li v-for="(feature, index) in vehicle.features" :key="index" class="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary flex-shrink-0 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                          </svg>
                          <span>{{ feature }}</span>
                        </li>
                        <!-- If features is a comma-separated string: -->
                        <!-- <li v-for="(feature, index) in vehicle.features.split(',').map(f => f.trim()).filter(f => f)" :key="index">{{ feature }}</li> -->
                      </ul>
                    </div>
                  </div>

                  <!-- Description -->
                  <!-- Description -->
                  <div v-if="vehicle.description" class="border rounded-lg overflow-hidden">
                    <div class="bg-gray-50 px-4 py-2 border-b">
                      <h6 class="font-medium text-gray-700">Description</h6>
                    </div>
                    <div class="p-4">
                      <p class="text-sm text-gray-700 whitespace-pre-wrap leading-relaxed">{{ vehicle.description }}</p>
                    </div>
                  </div>

                </div>
              </div>
              <div v-else class="mt-4 text-center text-gray-500">
                Loading vehicle data...
              </div>

              <div class="mt-6 flex justify-end space-x-4 border-t pt-4">
                <button
                  type="button"
                  class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-5 py-2.5 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
                  @click="closeModal"
                >
                  Close
                </button>
                <button
                  type="button"
                  class="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-5 py-2.5 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
                  @click="openAiStudio"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  AI Studio
                </button>
                <button
                  type="button"
                  class="inline-flex justify-center rounded-md border border-transparent bg-primary px-5 py-2.5 text-sm font-medium text-white shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
                  @click="editVehicle"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Edit Vehicle
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue';
import DetailItem from './DetailItem.vue'; // We'll create this helper component next

// Helper function to transform image URLs
const transformImageUrl = (url) => {
  if (!url) return '/images/no-image-available.jpg';

  // If the URL is already a full URL, return it as is
  if (url.startsWith('http')) return url;

  // If it's a relative URL or just a path, construct the full URL
  const projectId = 'wjqlfcxgrdfyqpjsbnyp';
  return `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${url}`;
};

const props = defineProps({
  vehicle: {
    type: Object,
    required: true,
  },
  isOpen: {
    type: Boolean,
    required: true,
  },
});
const emit = defineEmits(['close', 'edit', 'openAiStudio']);


const currentImageIndex = ref(0);

// --- Image Handling ---
// Assumes props.vehicle.images is an array of image URLs.
// Adjust this computed property if your image data structure is different
// (e.g., if you have vehicle.image for primary and vehicle.additionalImages for others)
const vehicleImages = computed(() => {
  if (!props.vehicle) return [];

  // Check if gallery array exists
  if (Array.isArray(props.vehicle.gallery) && props.vehicle.gallery.length > 0) {
    return props.vehicle.gallery.map(img => transformImageUrl(img));
  }

  // Fallback to single image if gallery doesn't exist
  if (props.vehicle.image) {
    return [transformImageUrl(props.vehicle.image)];
  }

  return [];
});

const currentImage = computed(() => {
  if (vehicleImages.value.length === 0) {
    // Return a placeholder image URL
    return '/images/no-image-available.jpg';
  }
  return vehicleImages.value[currentImageIndex.value];
});

function nextImage() {
  if (currentImageIndex.value < vehicleImages.value.length - 1) {
    currentImageIndex.value++;
  }
}

function prevImage() {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--;
  }
}

// Reset image index when the modal opens or the vehicle changes
watch(() => props.isOpen, (newVal) => {
  if (newVal) {
    currentImageIndex.value = 0;
  }
});

watch(() => props.vehicle, () => {
    currentImageIndex.value = 0;
}, { deep: true }); // Use deep watch if needed, though watching isOpen might be sufficient


// --- Formatting ---
const formatPrice = (price) => {
  if (price === null || price === undefined) return 'N/A';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'CAD',
    maximumFractionDigits: 0,
  }).format(price);
};

const formatMileage = (mileage) => {
  if (mileage === null || mileage === undefined) return 'N/A';
  return `${mileage.toLocaleString()} km`;
};

// --- Actions ---
function closeModal() {
  emit('close');
}

function editVehicle() {
  emit('edit', props.vehicle.id); // Emit the ID for the parent handler
}

function openAiStudio() {
  emit('openAiStudio', props.vehicle.id); // Emit the ID for the parent handler
}
</script>

<style scoped>
/* Add aspect ratio plugin if not already included in tailwind.config.js */
/* npm install -D @tailwindcss/aspect-ratio */
/* plugins: [require('@tailwindcss/aspect-ratio')] */
.aspect-w-16 { position: relative; padding-bottom: calc(9 / 16 * 100%); }
.aspect-h-9 { /* Covered by aspect-w-16 */ }
.aspect-w-16 > * { position: absolute; height: 100%; width: 100%; top: 0; right: 0; bottom: 0; left: 0; }

/* Custom scrollbar for thumbnails */
div::-webkit-scrollbar {
  height: 6px;
}
div::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
div::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}
div::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Improve focus visibility for accessibility */
button:focus-visible {
  outline: 2px solid theme('colors.primary');
  outline-offset: 2px;
}
</style>