/**
 * Auto Export Utility
 * 
 * This module provides functionality to automatically trigger CarPages exports
 * when vehicle inventory changes.
 */

/**
 * Trigger a Supabase to CarPages export via the backend API
 * @returns {Promise<Object>} The response from the export API
 */
export async function triggerCarPagesExport() {
  console.log('[AutoExport] Triggering CarPages export after inventory change');
  
  try {
    // Call the backend API to trigger the Supabase export
    const response = await fetch('/api/carpages/export-supabase', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('[AutoExport] Export triggered successfully:', result.message);
      return result;
    } else {
      console.error('[AutoExport] Export trigger failed:', result.message || 'Unknown error');
      return result;
    }
  } catch (error) {
    console.error('[AutoExport] Error triggering export:', error);
    return {
      success: false,
      message: `Error triggering export: ${error.message || 'Network error'}`
    };
  }
}

/**
 * Check if auto-export is enabled
 * Checks localStorage for the user preference
 * @returns {boolean} Whether auto-export is enabled
 */
export function isAutoExportEnabled() {
  // Get the setting from localStorage, default to false (disabled)
  const autoExportEnabled = localStorage.getItem('autoExportEnabled');
  
  // Log a warning if auto-export is enabled
  if (autoExportEnabled === "true") {
    console.warn('[AutoExport] Auto-export is enabled. This will send your entire inventory to CarPages with each change.');
    console.warn('[AutoExport] CarPages recommends daily or weekly updates rather than real-time updates.');
  }
  
  // Return true only if the value is explicitly set to "true"
  return autoExportEnabled === "true";
}

/**
 * Enable or disable auto-export
 * @param {boolean} enabled - Whether to enable auto-export
 */
export function setAutoExportEnabled(enabled) {
  localStorage.setItem('autoExportEnabled', enabled.toString());
  
  if (enabled) {
    console.log('[AutoExport] Auto-export enabled. This will send your entire inventory to CarPages with each change.');
    console.warn('[AutoExport] NOTE: CarPages recommends daily or weekly updates rather than real-time updates.');
  } else {
    console.log('[AutoExport] Auto-export disabled. Using scheduled daily exports is the recommended approach.');
  }
  
  return enabled;
}

/**
 * Get the current auto-export status
 * @returns {boolean} Whether auto-export is currently enabled
 */
export function getAutoExportStatus() {
  return isAutoExportEnabled();
}

export default {
  triggerCarPagesExport,
  isAutoExportEnabled,
  setAutoExportEnabled,
  getAutoExportStatus
};