/**
 * CarPages Export API Handler
 *
 * This module provides API endpoints to trigger CarPages exports from the admin dashboard.
 */

import { exec } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Paths to the export scripts
const EXPORT_SCRIPT_PATH = path.resolve(__dirname, '../scripts/export-to-carpages.js');
const SUPABASE_EXPORT_SCRIPT_PATH = path.resolve(__dirname, '../scripts/export-supabase-to-carpages.js');

// Log directory
const LOG_DIR = path.resolve(__dirname, '../scripts/logs');

/**
 * Trigger a CarPages export
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export function triggerExport(req, res) {
  console.log('Received request to trigger CarPages export');

  // Create logs directory if it doesn't exist
  if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
  }

  // Create a unique log file for this export
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const logFile = path.join(LOG_DIR, `api-export-${timestamp}.log`);
  const logStream = fs.createWriteStream(logFile, { flags: 'a' });

  // Log the request
  logStream.write(`Export triggered via API at ${new Date().toLocaleString()}\n`);
  logStream.write(`Requested by: ${req.ip}\n`);

  // Execute the export script as a separate process
  const child = exec(`node ${EXPORT_SCRIPT_PATH}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`Export execution error: ${error.message}`);
      logStream.write(`Export failed: ${error.message}\n`);
      logStream.end();
      return;
    }

    logStream.write('Export completed\n');
    logStream.end();
  });

  // Capture stdout and stderr
  child.stdout.on('data', (data) => {
    console.log(`Export stdout: ${data}`);
    logStream.write(`stdout: ${data}`);
  });

  child.stderr.on('data', (data) => {
    console.error(`Export stderr: ${data}`);
    logStream.write(`stderr: ${data}`);
  });

  // Respond immediately without waiting for the export to complete
  res.json({
    success: true,
    message: 'Export triggered successfully. Check logs for progress.',
    logFile: logFile
  });
}

/**
 * Trigger a Supabase to CarPages export
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export function triggerSupabaseExport(req, res) {
  console.log('Received request to trigger Supabase to CarPages export');

  // Create logs directory if it doesn't exist
  if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
  }

  // Create a unique log file for this export
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const logFile = path.join(LOG_DIR, `api-supabase-export-${timestamp}.log`);
  const logStream = fs.createWriteStream(logFile, { flags: 'a' });

  // Log the request
  logStream.write(`Supabase export triggered via API at ${new Date().toLocaleString()}\n`);
  logStream.write(`Requested by: ${req.ip}\n`);

  // Execute the export script as a separate process
  const child = exec(`node ${SUPABASE_EXPORT_SCRIPT_PATH}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`Supabase export execution error: ${error.message}`);
      logStream.write(`Supabase export failed: ${error.message}\n`);
      logStream.end();
      return;
    }

    logStream.write('Supabase export completed\n');
    logStream.end();
  });

  // Capture stdout and stderr
  child.stdout.on('data', (data) => {
    console.log(`Supabase export stdout: ${data}`);
    logStream.write(`stdout: ${data}`);
  });

  child.stderr.on('data', (data) => {
    console.error(`Supabase export stderr: ${data}`);
    logStream.write(`stderr: ${data}`);
  });

  // Respond immediately without waiting for the export to complete
  res.json({
    success: true,
    message: 'Supabase export triggered successfully. Check logs for progress.',
    logFile: logFile
  });
}