/**
 * Supabase Anonymous Client
 * This is a dedicated client instance for anonymous operations.
 * It ensures requests are always made with the anon role, even if a user is logged in elsewhere.
 */
import { createClient } from '@supabase/supabase-js'

// Get Supabase credentials from environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_KEY // This should be the anon key

// Check if environment variables are available
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase configuration missing for Anon client. Please set VITE_SUPABASE_URL and VITE_SUPABASE_KEY environment variables.')
}

// Log which values are being used (for debugging)
console.log(`Anon Client: Supabase URL: ${supabaseUrl ? supabaseUrl.substring(0, 20) + '...' : 'undefined'}`)
console.log(`Anon Client: Using ${import.meta.env.VITE_SUPABASE_URL ? 'environment' : 'hardcoded'} Supabase URL`)

// Create and export the Supabase anonymous client
// Important: No auth options are passed to ensure it remains anonymous
export const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false, // Don't persist any session
    autoRefreshToken: false, // Don't refresh tokens
  }
})

// Export default for convenience
export default supabaseAnon