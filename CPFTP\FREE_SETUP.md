# Free CarPages Export Setup for Render.com

This guide explains how to set up scheduled CarPages exports for free using your existing Render.com web service.

## Overview

Instead of using a paid Background Worker on Render.com, we'll use a combination of:

1. An API endpoint in your existing web service
2. A free external cron service to trigger the endpoint on a schedule

This approach doesn't require any additional paid services on Render.com.

## Step 1: Integrate the Export API Endpoint

We've already created the necessary API endpoint in `backend/routes/carpagesExport.js`. You need to integrate this into your main Express application:

1. Open your main Express app file (likely `backend/server.js` or similar)
2. Import the CarPages export routes:
   ```javascript
   import carpagesExportRoutes from './routes/carpagesExport.js';
   ```
3. Add the routes to your Express app:
   ```javascript
   app.use('/api/carpages', carpagesExportRoutes);
   ```

## Step 2: Set Up Environment Variables

1. Add a secure API key to your Render.com environment variables:
   - Go to your web service in the Render dashboard
   - Click on "Environment"
   - Add a new environment variable:
     - Key: `EXPORT_API_KEY`
     - Value: Generate a secure random string (e.g., `d8e7f6g5h4j3k2l1`)

## Step 3: Set Up a Free Cron Service

Now that you've created an account on Cron-job.org, follow our detailed step-by-step guide to set up your cron job:

[Detailed Cron Job Setup Instructions](./CRON_JOB_SETUP.md)

This guide includes:
- Screenshots and detailed steps for configuring your cron job
- Instructions for setting the correct headers and authentication
- Troubleshooting tips if you encounter any issues

### Alternative Free Cron Services

If you prefer not to use Cron-job.org, here are some alternatives:

- [EasyCron](https://www.easycron.com) - Free plan includes 5 cron jobs
- [SetCronJob](https://www.setcronjob.com) - Free plan includes 5 cron jobs
- [CronHub](https://cronhub.io) - Free plan includes 2 monitors

## Step 4: Test the Setup

To test that everything is working correctly:

1. Deploy your updated application to Render
2. Manually trigger the cron job from the cron service dashboard
3. Check your application logs in Render to verify the export ran successfully

## How It Works

1. The cron service will make a POST request to your API endpoint at the scheduled time
2. The API endpoint verifies the API key for security
3. If the key is valid, it triggers the CarPages export process
4. The export runs within your existing web service, fetching data from Supabase and uploading it to CarPages

## Advantages of This Approach

- **No Additional Cost**: Uses your existing Render web service
- **Simplicity**: No need to manage a separate worker service
- **Security**: Protected by an API key
- **Reliability**: External cron services are designed for high reliability

## Troubleshooting

If the scheduled export isn't working:

1. Check your Render logs for any error messages
2. Verify the API key is correctly set in both Render and the cron service
3. Make sure your web service is running (not in sleep mode if using a free Render plan)
4. Test the endpoint manually using a tool like Postman or curl:
   ```
   curl -X POST https://your-render-app.onrender.com/api/carpages/scheduled-export \
   -H "x-api-key: your-api-key-here"