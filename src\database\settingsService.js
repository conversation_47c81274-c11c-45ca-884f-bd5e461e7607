/**
 * Supabase Service for GT Motorsports settings
 * This file provides functions to interact with the Supabase 'settings' table
 * which uses a 'setting_key' column to identify different settings.
 */
import { supabase } from '../utils/supabase'; // Ensure this path is correct

/**
 * Get a specific setting value by its key.
 * @param {string} settingKey - The unique key identifying the setting (e.g., 'interest_rates', 'detailing_prices').
 * @returns {Promise<object|null>} - The settings value (parsed JSON) or null if not found or on error.
 */
export const getSettingByKey = async (settingKey) => {
  console.log(`[Settings Service] Fetching setting for key: ${settingKey}`);

  try {
    // Log the Supabase client to ensure it's properly initialized
    console.log(`[Settings Service] Supabase client initialized:`, !!supabase);

    const { data, error, status } = await supabase
      .from('settings')
      .select('value, setting_key')
      .eq('setting_key', settingKey) // Use 'setting_key' column
      .single(); // Expect only one row for a given key

    console.log(`[Settings Service] Supabase query result for '${settingKey}':`, { data, error, status });

    // Handle specific Supabase errors (like row not found, which isn't always a critical error)
    if (error && status !== 406) { // 406 means single() found 0 rows, which is handled below
      console.error(`[Settings Service] Error fetching setting '${settingKey}':`, error);
      return null;
    }

    if (!data) {
      console.warn(`[Settings Service] No setting found for key: ${settingKey}`);
      // Let's try to get all settings to see what's available
      const { data: allSettings } = await supabase.from('settings').select('setting_key');
      console.log(`[Settings Service] Available settings in database:`, allSettings);
      return null; // Return null if setting doesn't exist yet
    }

    console.log(`[Settings Service] Successfully retrieved setting for key: ${settingKey}`, data);
    // 'value' column is JSONB, Supabase client parses it automatically
    return data.value;
  } catch (error) {
    console.error(`[Settings Service] Unexpected error fetching setting '${settingKey}':`, error);
    return null;
  }
};

/**
 * Update or insert a specific setting value by its key.
 * Requires a UNIQUE constraint on the 'setting_key' column in the Supabase table.
 * @param {string} settingKey - The unique key identifying the setting.
 * @param {object} value - The settings value (JSON object) to store.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
export const updateSettingByKey = async (settingKey, value) => {
  console.log(`[Settings Service] Updating setting for key: ${settingKey}`);

  try {
    // First check if the setting exists
    const { data, error: checkError } = await supabase
      .from('settings')
      .select('id, setting_key')
      .eq('setting_key', settingKey)
      .maybeSingle();

    console.log(`[Settings Service] Check if setting '${settingKey}' exists:`, { data, checkError });

    if (checkError) {
      console.warn(`[Settings Service] Warning when checking setting '${settingKey}':`, checkError);
    }

    let result;

    if (data) {
      // Setting exists, update it
      console.log(`[Settings Service] Setting '${settingKey}' exists with ID ${data.id}, updating...`);
      const updateResult = await supabase
        .from('settings')
        .update({ value })
        .eq('setting_key', settingKey);

      const { error: updateError } = updateResult;
      console.log(`[Settings Service] Update result for '${settingKey}':`, updateResult);

      if (updateError) {
        console.error(`[Settings Service] Error updating setting '${settingKey}':`, updateError);
        return false;
      }

      result = !updateError;
    } else {
      // Setting doesn't exist, insert it
      console.log(`[Settings Service] Setting '${settingKey}' doesn't exist, inserting new record...`);
      const insertResult = await supabase
        .from('settings')
        .insert({
          setting_key: settingKey,
          value
        });

      const { error: insertError } = insertResult;
      console.log(`[Settings Service] Insert result for '${settingKey}':`, insertResult);

      if (insertError) {
        console.error(`[Settings Service] Error inserting setting '${settingKey}':`, insertError);
        return false;
      }

      result = !insertError;
    }

    console.log(`[Settings Service] Successfully updated setting for key: ${settingKey}`, result);
    return result;
  } catch (error) {
    console.error(`[Settings Service] Unexpected error updating setting '${settingKey}':`, error);
    return false;
  }
};

// --- Specific Setting Functions ---

/**
 * Get interest rates for financing.
 * @returns {Promise<object|null>} - The interest rates object or null.
 */
export const getInterestRates = async () => {
  // Uses the specific key defined for interest rates
  return await getSettingByKey('interest_rates');
};

/**
 * Update interest rates for financing.
 * @param {object} rates - The interest rates object to store.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
export const updateInterestRates = async (rates) => {
  // Uses the specific key defined for interest rates
  return await updateSettingByKey('interest_rates', rates);
};

/**
 * Get detailing service prices.
 * @returns {Promise<object|null>} - The detailing prices object or null.
 */
export const getDetailingPrices = async () => {
  // Uses the specific key defined for detailing prices
  console.log('Fetching detailing prices from settings table with key: detailing_prices');
  const result = await getSettingByKey('detailing_prices');
  console.log('Detailing prices result from Supabase:', result);
  return result;
};

/**
 * Update detailing service prices.
 * @param {object} prices - The detailing prices object to store.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
export const updateDetailingPrices = async (prices) => {
  // Uses the specific key defined for detailing prices
  console.log('Saving detailing prices to settings table with key: detailing_prices');
  console.log('Detailing prices data being saved:', JSON.stringify(prices, null, 2));
  const result = await updateSettingByKey('detailing_prices', prices);
  console.log('Result of saving detailing prices:', result);
  return result;
};

/**
 * Get business information.
 * @returns {Promise<object|null>} - The business information object or null.
 */
export const getBusinessInfo = async () => {
  // Uses the specific key defined for business information
  return await getSettingByKey('business_info');
};

/**
 * Update business information.
 * @param {object} info - The business information object to store.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
export const updateBusinessInfo = async (info) => {
  // Uses the specific key defined for business information
  return await updateSettingByKey('business_info', info);
};

/**
 * Get financing FAQs.
 * @returns {Promise<Array|null>} - The financing FAQs array or null.
 */
export const getFinancingFaqs = async () => {
  // Uses the specific key defined for financing FAQs
  return await getSettingByKey('financing_faqs');
};

/**
 * Update financing FAQs.
 * @param {Array} faqs - The financing FAQs array to store.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
export const updateFinancingFaqs = async (faqs) => {
  // Uses the specific key defined for financing FAQs
  return await updateSettingByKey('financing_faqs', faqs);
};

/**
 * Get financing options content.
 * @returns {Promise<object|null>} - The financing options content object or null.
 */
export const getFinancingOptions = async () => {
  // Uses the specific key defined for financing options
  console.log('Fetching financing options from settings table with key: financing_options');
  const result = await getSettingByKey('financing_options');
  console.log('Financing options result from Supabase:', result);
  return result;
};

/**
 * Update financing options content.
 * @param {object} options - The financing options content object to store.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
export const updateFinancingOptions = async (options) => {
  // Uses the specific key defined for financing options
  console.log('Saving financing options to settings table with key: financing_options');
  console.log('Financing options data being saved:', JSON.stringify(options, null, 2));
  const result = await updateSettingByKey('financing_options', options);
  console.log('Result of saving financing options:', result);
  return result;
};

/**
 * Get financing process content.
 * @returns {Promise<object|null>} - The financing process content object or null.
 */
export const getFinancingProcess = async () => {
  // Uses the specific key defined for financing process
  console.log('Fetching financing process from settings table with key: financing_process');
  const result = await getSettingByKey('financing_process');
  console.log('Financing process result from Supabase:', result);
  return result;
};

/**
 * Update financing process content.
 * @param {object} process - The financing process content object to store.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
export const updateFinancingProcess = async (process) => {
  // Uses the specific key defined for financing process
  console.log('Saving financing process to settings table with key: financing_process');
  console.log('Financing process data being saved:', JSON.stringify(process, null, 2));
  const result = await updateSettingByKey('financing_process', process);
  console.log('Result of saving financing process:', result);
  return result;
};