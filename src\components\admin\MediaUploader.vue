<template>
  <div class="media-uploader">
    <label :for="id" class="block text-sm font-medium text-gray-800">{{ label }}</label>
    <div class="mt-1">
      <!-- Media preview -->
      <div v-if="modelValue" class="mb-2">
        <div v-if="isImage" class="relative">
          <img :src="modelValue" :alt="label" class="h-40 w-full object-cover rounded-md" />
          <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
            <button 
              type="button" 
              @click="removeMedia" 
              class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Remove
            </button>
          </div>
        </div>
        <div v-else-if="isVideo" class="relative">
          <video :src="modelValue" controls class="h-40 w-full object-cover rounded-md"></video>
          <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
            <button 
              type="button" 
              @click="removeMedia" 
              class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Remove
            </button>
          </div>
        </div>
        <div v-else class="flex items-center justify-between bg-gray-100 p-2 rounded-md">
          <span class="text-sm text-gray-700 truncate">{{ getFileName(modelValue) }}</span>
          <button 
            type="button" 
            @click="removeMedia" 
            class="inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Remove
          </button>
        </div>
      </div>
      
      <!-- Upload area -->
      <div v-if="!modelValue && !isUploading" class="flex items-center justify-center w-full">
        <label :for="id" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
          <div class="flex flex-col items-center justify-center pt-5 pb-6">
            <svg class="w-8 h-8 mb-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            <p class="mb-1 text-sm text-gray-500">Click to upload {{ acceptLabel }}</p>
            <p class="text-xs text-gray-500">{{ acceptDescription }}</p>
          </div>
          <input 
            :id="id" 
            type="file" 
            class="hidden" 
            :accept="accept" 
            @change="handleFileUpload" 
          />
        </label>
      </div>
      
      <!-- Loading state -->
      <div v-if="isUploading" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 rounded-lg bg-gray-50">
        <img src="/GTWHEEL.png" alt="Uploading..." class="animate-spin h-8 w-8 mb-3" />
        <p class="text-sm text-gray-500">Uploading {{ acceptLabel }}...</p>
      </div>
      
      <p v-if="description" class="mt-1 text-sm text-gray-500">{{ description }}</p>
      <p v-if="error" class="mt-1 text-sm text-red-600">{{ error }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import stylingStore from '../../store/styling';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    required: true
  },
  id: {
    type: String,
    required: true
  },
  section: {
    type: String,
    required: true
  },
  accept: {
    type: String,
    default: 'image/*,video/*'
  },
  description: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

const isUploading = ref(false);
const error = ref('');
const mediaPath = ref('');

// Computed properties for media type
const isImage = computed(() => {
  if (!props.modelValue) return false;
  const ext = getFileExtension(props.modelValue);
  return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext);
});

const isVideo = computed(() => {
  if (!props.modelValue) return false;
  const ext = getFileExtension(props.modelValue);
  return ['mp4', 'webm', 'ogg'].includes(ext);
});

const acceptLabel = computed(() => {
  if (props.accept === 'image/*') return 'image';
  if (props.accept === 'video/*') return 'video';
  return 'media';
});

const acceptDescription = computed(() => {
  if (props.accept === 'image/*') return 'PNG, JPG, GIF, WEBP (Max 5MB)';
  if (props.accept === 'video/*') return 'MP4, WEBM, OGG (Max 5MB)';
  return 'PNG, JPG, GIF, WEBP, MP4, WEBM, OGG (Max 5MB)';
});

// Helper functions
const getFileExtension = (url) => {
  if (!url) return '';
  return url.split('.').pop().toLowerCase();
};

const getFileName = (url) => {
  if (!url) return '';
  return url.split('/').pop();
};

// Handle file upload
const handleFileUpload = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  try {
    error.value = '';
    isUploading.value = true;
    
    // Upload the file
    const result = await stylingStore.uploadMedia(file, props.section);
    
    if (result && result.url) {
      // Store the path for future removal
      mediaPath.value = result.path;
      
      // Emit the URL
      emit('update:modelValue', result.url);
    } else {
      error.value = 'Failed to upload file. Please try again.';
    }
  } catch (err) {
    error.value = err.message || 'An error occurred while uploading the file.';
  } finally {
    isUploading.value = false;
  }
};

// Remove media
const removeMedia = async () => {
  try {
    error.value = '';
    isUploading.value = true;
    
    // If we have a path, remove the file from storage
    if (mediaPath.value) {
      await stylingStore.removeMedia(mediaPath.value);
    }
    
    // Clear the value
    emit('update:modelValue', '');
    mediaPath.value = '';
  } catch (err) {
    error.value = err.message || 'An error occurred while removing the file.';
  } finally {
    isUploading.value = false;
  }
};
</script>
