/**
 * Test Server Script
 * This script helps test if the backend server is running correctly.
 */
import express from 'express';
import cors from 'cors';

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Test endpoint
app.post('/api/pre-qualification', (req, res) => {
  console.log('Received form data:', req.body);
  return res.status(200).json({
    success: true,
    data: {
      id: 1,
      ...req.body,
      created_at: new Date().toISOString()
    },
    message: 'Test server received your form data successfully!'
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Test server is running!' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
  console.log(`Try accessing http://localhost:${PORT}/api/health in your browser`);
});