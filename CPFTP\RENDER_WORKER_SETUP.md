# Setting Up CarPages Exports with Render.com Background Worker

This guide provides detailed step-by-step instructions for setting up automated CarPages exports using a Background Worker on Render.com.

## Overview

Since your website is hosted on Render.com, using a Background Worker is an excellent choice for handling your export tasks. This approach ensures that exports run reliably in the cloud environment without requiring external services.

## Prerequisites

- Your website is already deployed on Render.com
- You have access to the Render.com dashboard
- Your repository contains the export scripts we've created

## Step 1: Log in to Your Render.com Dashboard

1. Go to https://dashboard.render.com/
2. Log in with your credentials
3. You should see your existing web service listed on the dashboard

## Step 2: Create a New Background Worker Service

1. Click the "New +" button in the top right corner of the dashboard
2. Select "Background Worker" from the dropdown menu

   ![New Background Worker](https://i.imgur.com/example1.png)

3. You'll be prompted to connect your repository:
   - Select the same repository that contains your web service
   - If you don't see your repository, click "Connect account" to connect your GitHub/GitLab account

   ![Connect Repository](https://i.imgur.com/example2.png)

## Step 3: Configure the Background Worker

After selecting your repository, you'll see the configuration page. Fill it out as follows:

### Basic Settings

![Basic Settings](https://i.imgur.com/example3.png)

- **Name**: Enter `gt-motorsports-carpages-export` (or any name you prefer)
- **Environment**: Select `Node` from the dropdown
- **Region**: Choose the same region as your web service for best performance
- **Branch**: Select `main` (or your production branch)
- **Build Command**: Enter `npm install`
- **Start Command**: Enter `bash scripts/start-render-worker.sh`

### Environment Variables

Scroll down to the "Environment Variables" section and add the following:

![Environment Variables](https://i.imgur.com/example4.png)

1. Click "Add Environment Variable" for each of these:

   - **Key**: `VITE_SUPABASE_URL`
     **Value**: Your Supabase URL (copy from your web service)

   - **Key**: `VITE_SUPABASE_KEY`
     **Value**: Your Supabase key (copy from your web service)

   - **Key**: `CARPAGES_FTP_HOST`
     **Value**: `ftp.carpages.ca`

   - **Key**: `CARPAGES_FTP_USER`
     **Value**: `GTMotor`

   - **Key**: `CARPAGES_FTP_PASSWORD`
     **Value**: `kYP76iWb3AphEmbyX8GW`

   **For Continuous Workers (without cron)**, add these additional variables:

   - **Key**: `CONTINUOUS_WORKER`
     **Value**: `true`

   - **Key**: `EXPORT_INTERVAL_HOURS`
     **Value**: `24` (or your preferred interval in hours)

### Worker Type (Continuous vs. Scheduled)

Scroll down to the "Cron Job" section:

![Worker Type](https://i.imgur.com/example5.png)

For a continuously running worker (no cron schedule):
1. Leave the "Run as a cron job" checkbox **unchecked**
2. This will make your worker run continuously, and you'll need to implement your own logic for when to run exports

Alternatively, if you do want scheduled exports:
1. Check the "Run as a cron job" checkbox
2. Enter a cron expression like `0 2 * * *` (runs at 2:00 AM UTC daily)

### Plan Selection

![Plan Selection](https://i.imgur.com/example6.png)

1. Select the appropriate plan:
   - For testing, you can start with the free plan, but it has limitations
   - For production, the "Starter" plan ($7/month) is recommended for reliability

2. Click "Create Background Worker" to create the service

## Step 4: Wait for Initial Deployment

After creating the Background Worker, Render will automatically start building and deploying it:

![Deployment in Progress](https://i.imgur.com/example7.png)

1. Wait for the initial build and deployment to complete (this may take a few minutes)
2. You'll see the status change to "Live" when it's ready

## Step 5: Verify the Setup

Once the worker is deployed, you can verify it's working correctly:

1. Click on the "Logs" tab to view the deployment and execution logs

   ![Logs Tab](https://i.imgur.com/example8.png)

2. You should see logs indicating that the worker has started and is running the export script

## Step 6: Test the Worker

To test that the worker is functioning correctly:

1. Click on the "Manual Deploy" button in the top-right corner
2. Select "Clear build cache & deploy" from the dropdown

   ![Manual Deploy](https://i.imgur.com/example9.png)

3. This will trigger a new deployment and run the worker immediately
4. Check the logs to verify that the export ran successfully

## Monitoring and Troubleshooting

### Viewing Logs

To view the logs of your Background Worker:

1. Go to your Render dashboard
2. Click on your Background Worker service
3. Click the "Logs" tab
4. You can filter logs by:
   - "Build" - Shows logs from the build process
   - "Runtime" - Shows logs from the running worker

### Common Issues and Solutions

If your worker isn't running correctly, check for these common issues:

1. **Build Failures**:
   - Check the build logs for errors
   - Make sure all required files exist in your repository
   - Verify that the Node.js version is compatible with your code

2. **Runtime Errors**:
   - Check the runtime logs for error messages
   - Verify that all environment variables are set correctly
   - Make sure the Supabase credentials are valid

3. **FTP Connection Issues**:
   - Check if the FTP credentials are correct
   - Verify that the Render.com IP addresses aren't blocked by the FTP server

## Adjusting the Schedule

If you need to change the schedule:

1. Go to your Background Worker service in the Render dashboard
2. Click "Settings" in the left sidebar
3. Scroll down to the "Cron Job" section
4. Update the cron expression
5. Click "Save Changes"

Common cron expressions:
- `0 2 * * *`: Daily at 2:00 AM UTC
- `0 2 * * 1`: Weekly on Monday at 2:00 AM UTC
- `0 2 1 * *`: Monthly on the 1st at 2:00 AM UTC

## Understanding How It Works

The Background Worker runs using two key files:

1. `scripts/start-render-worker.sh`: A bash script that Render executes to start the worker
2. `scripts/render-export-worker.js`: The Node.js script that performs the actual export

### For Continuous Workers (No Cron)

If you're using a continuous worker (no cron schedule), you have two options:

1. **Run Once and Exit**: The current script will run the export once and then exit. Render will automatically restart it, creating a delay between export attempts.

2. **Implement a Timer**: You could modify the `render-export-worker.js` script to include a timer that runs the export at specific intervals:

```javascript
// Add this to the end of render-export-worker.js
// This will run the export every 24 hours
setInterval(() => {
  console.log('Running scheduled export...');
  runExport();
}, 24 * 60 * 60 * 1000); // 24 hours in milliseconds

// Keep the process alive
console.log('Worker started, waiting for scheduled time...');
```

### For Scheduled Workers (With Cron)

If you're using a scheduled worker with cron, the process is simpler:

1. The worker runs at the scheduled time
2. It performs the export process
3. It exits until the next scheduled run

In both cases, the export process:
1. Fetches all vehicles from your Supabase database
2. Formats them according to CarPages requirements
3. Generates a CSV file
4. Uploads the CSV to the CarPages FTP server
5. Logs the results