/**
 * Database schema for GT Motorsports vehicle inventory
 * This file defines the structure of the database tables
 */

// Vehicle table - Core vehicle information
const vehicleSchema = {
  id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
  title: 'TEXT NOT NULL', // Full title (year make model)
  year: 'INTEGER NOT NULL',
  make: 'TEXT NOT NULL',
  model: 'TEXT NOT NULL',
  trim: 'TEXT',
  price: 'INTEGER NOT NULL',
  specialPrice: 'INTEGER',
  stockNumber: 'TEXT',
  vin: 'TEXT',
  description: 'TEXT',
  createdAt: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
  updatedAt: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
};

// Vehicle details - Technical specifications
const vehicleDetailsSchema = {
  id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
  vehicleId: 'INTEGER NOT NULL REFERENCES vehicles(id) ON DELETE CASCADE',
  bodyStyle: 'TEXT',
  doors: 'INTEGER',
  engine: 'TEXT',
  engineSize: 'TEXT',
  drivetrain: 'TEXT',
  transmission: 'TEXT',
  exteriorColor: 'TEXT',
  interiorColor: 'TEXT',
  passengers: 'INTEGER',
  fuelType: 'TEXT',
  cityFuel: 'TEXT',
  hwyFuel: 'TEXT',
  mileage: 'INTEGER NOT NULL',
  odometer: 'INTEGER', // Same as mileage but stored as original value from import
  odometerUnit: 'TEXT DEFAULT "km"' // km or miles
};

// Vehicle features - List of features for each vehicle
const vehicleFeaturesSchema = {
  id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
  vehicleId: 'INTEGER NOT NULL REFERENCES vehicles(id) ON DELETE CASCADE',
  feature: 'TEXT NOT NULL'
};

// Vehicle highlights - Key selling points
const vehicleHighlightsSchema = {
  id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
  vehicleId: 'INTEGER NOT NULL REFERENCES vehicles(id) ON DELETE CASCADE',
  highlight: 'TEXT NOT NULL'
};

// Vehicle images - All images associated with a vehicle
const vehicleImagesSchema = {
  id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
  vehicleId: 'INTEGER NOT NULL REFERENCES vehicles(id) ON DELETE CASCADE',
  url: 'TEXT NOT NULL',
  isPrimary: 'BOOLEAN DEFAULT 0',
  sortOrder: 'INTEGER DEFAULT 0'
};

// Vehicle types - For categorizing different vehicle types
const vehicleTypesSchema = {
  id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
  name: 'TEXT NOT NULL UNIQUE',
  description: 'TEXT'
};

// Vehicle type mapping - Maps vehicles to their types
const vehicleTypeMapSchema = {
  vehicleId: 'INTEGER NOT NULL REFERENCES vehicles(id) ON DELETE CASCADE',
  typeId: 'INTEGER NOT NULL REFERENCES vehicleTypes(id) ON DELETE CASCADE',
  primaryKey: '(vehicleId, typeId)'
};

// Motorcycle specific details
const motorcycleDetailsSchema = {
  vehicleId: 'INTEGER PRIMARY KEY REFERENCES vehicles(id) ON DELETE CASCADE',
  engineCC: 'REAL',
  cylinders: 'INTEGER',
  topSpeed: 'INTEGER',
  weight: 'INTEGER',
  seatHeight: 'INTEGER'
};

// Boat specific details
const boatDetailsSchema = {
  vehicleId: 'INTEGER PRIMARY KEY REFERENCES vehicles(id) ON DELETE CASCADE',
  length: 'REAL',
  beam: 'REAL',
  draft: 'REAL',
  hullMaterial: 'TEXT',
  engineHours: 'INTEGER',
  passengerCapacity: 'INTEGER'
};

// ATV specific details
const atvDetailsSchema = {
  vehicleId: 'INTEGER PRIMARY KEY REFERENCES vehicles(id) ON DELETE CASCADE',
  engineCC: 'REAL',
  wheelbase: 'INTEGER',
  groundClearance: 'INTEGER',
  fuelCapacity: 'REAL',
  rackCapacity: 'INTEGER'
};

export default {
  vehicleSchema,
  vehicleDetailsSchema,
  vehicleFeaturesSchema,
  vehicleHighlightsSchema,
  vehicleImagesSchema,
  vehicleTypesSchema,
  vehicleTypeMapSchema,
  motorcycleDetailsSchema,
  boatDetailsSchema,
  atvDetailsSchema
};