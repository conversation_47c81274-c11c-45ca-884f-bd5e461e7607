#!/usr/bin/env node

/**
 * Render.com Background Worker for CarPages Export
 *
 * This script is designed to be run as a Background Worker on Render.com.
 * It exports vehicle inventory data from Supabase to CarPages on a schedule.
 */

import { exportSupabaseToCarPages } from './export-supabase-to-carpages.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Main function to run the export
 */
async function runExport() {
  console.log('=================================================');
  console.log('STARTING CARPAGES EXPORT (RENDER WORKER)');
  console.log('=================================================');
  console.log('Date/Time:', new Date().toLocaleString());
  
  try {
    // Run the export
    const result = await exportSupabaseToCarPages();
    
    if (!result.success) {
      console.error('Failed to export to CarPages:', result.message);
      return;
    }
    
    console.log(`Successfully exported ${result.vehicleCount} vehicles to CarPages.`);
    
    // Create logs directory if it doesn't exist
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    // Save a log of the successful export
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const logFile = path.join(logDir, `render-export-${timestamp}.log`);
    
    fs.writeFileSync(logFile, `Export completed successfully at ${new Date().toLocaleString()}\n` +
      `Exported ${result.vehicleCount} vehicles\n` +
      `CSV saved to ${result.csvFilePath}\n`);
    
    console.log('Export completed successfully.');
  } catch (error) {
    console.error('Error during CarPages export:', error);
  }
}

// Run the export immediately when the worker starts
runExport();

// Check if this is running as a continuous worker (not a cron job)
// If CONTINUOUS_WORKER is set to "true", run on a timer
if (process.env.CONTINUOUS_WORKER === "true") {
  // Set up interval to run the export periodically (default: every 24 hours)
  const intervalHours = parseInt(process.env.EXPORT_INTERVAL_HOURS || "24", 10);
  const intervalMs = intervalHours * 60 * 60 * 1000;
  
  console.log(`Setting up continuous worker to run every ${intervalHours} hours`);
  
  setInterval(() => {
    console.log(`Running scheduled export (interval: ${intervalHours} hours)...`);
    runExport();
  }, intervalMs);
  
  // Keep the process alive
  console.log('Worker started in continuous mode, waiting for next scheduled run...');
} else {
  // If this is a one-time worker or cron job, exit after completion
  // If it's a scheduled worker, Render will restart it according to the cron schedule
  console.log('Worker running in one-time/cron mode, will exit after completion');
}