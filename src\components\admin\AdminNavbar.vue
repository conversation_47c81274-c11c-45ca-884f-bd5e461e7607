<script setup>
import { computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import authStore from '../../store/supabaseAuth';

const router = useRouter();
const route = useRoute();

// Check if the current route is active
const isActive = (path) => {
  return route.path.startsWith(path);
};

// User name
const userName = computed(() => authStore.user?.name || 'Admin');

// Logout function
const handleLogout = () => {
  authStore.logout();
  router.push('/admin/login');
};
</script>

<template>
  <header class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <!-- Logo and Navigation -->
        <div class="flex">
          <!-- Logo -->
          <div class="flex-shrink-0 flex items-center">
            <router-link to="/" class="text-xl font-bold text-primary">
              GT Auto Admin
            </router-link>
          </div>
          
          <!-- Navigation Links -->
          <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
            <router-link 
              to="/admin/dashboard" 
              class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
              :class="{ 'border-primary text-gray-900': isActive('/admin/dashboard') }"
            >
              Dashboard
            </router-link>
            
            <router-link 
              to="/admin/vehicles/add" 
              class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
              :class="{ 'border-primary text-gray-900': isActive('/admin/vehicles/add') }"
            >
              Add Vehicle
            </router-link>
            
            <a 
              href="/" 
              target="_blank"
              class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
            >
              View Website
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </a>
          </div>
        </div>
        
        <!-- User Menu -->
        <div class="hidden sm:ml-6 sm:flex sm:items-center">
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">
              Welcome, {{ userName }}
            </span>
            <button 
              @click="handleLogout"
              class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Logout
            </button>
          </div>
        </div>
        
        <!-- Mobile menu button -->
        <div class="flex items-center sm:hidden">
          <button 
            type="button" 
            class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
            aria-controls="mobile-menu" 
            aria-expanded="false"
            @click="$refs.mobileMenu.classList.toggle('hidden')"
          >
            <span class="sr-only">Open main menu</span>
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile menu -->
    <div class="sm:hidden hidden" ref="mobileMenu" id="mobile-menu">
      <div class="pt-2 pb-3 space-y-1">
        <router-link 
          to="/admin/dashboard" 
          class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
          :class="{ 'bg-primary-50 border-primary text-primary-700': isActive('/admin/dashboard') }"
          @click="$refs.mobileMenu.classList.add('hidden')"
        >
          Dashboard
        </router-link>
        
        <router-link 
          to="/admin/vehicles/add" 
          class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
          :class="{ 'bg-primary-50 border-primary text-primary-700': isActive('/admin/vehicles/add') }"
          @click="$refs.mobileMenu.classList.add('hidden')"
        >
          Add Vehicle
        </router-link>
        
        <a 
          href="/" 
          target="_blank"
          class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
          @click="$refs.mobileMenu.classList.add('hidden')"
        >
          View Website
        </a>
        
        <button 
          @click="handleLogout"
          class="w-full text-left border-transparent text-red-500 hover:bg-red-50 hover:border-red-300 hover:text-red-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
        >
          Logout
        </button>
      </div>
    </div>
  </header>
</template>