# Setting Up Your CarPages Export Cron Job on cron-job.org

This guide provides detailed step-by-step instructions for setting up your cron job on cron-job.org to trigger your CarPages export.

## Step 1: Log in to Your cron-job.org Account

1. Go to https://cron-job.org/en/
2. Click on "Login" in the top-right corner
3. Enter your credentials (email and password)
4. Click "Login"

You should now see the cron-job.org dashboard.

## Step 2: Create a New Cron Job

1. On the dashboard, locate and click the green "Create cronjob" button in the top-right corner.

2. You'll see a form with several sections. Fill them out as follows:

   ### Title and URL Section
   
   ![Title and URL Section](https://i.imgur.com/example1.png)
   
   - **Title**: Enter `GT Motorsports CarPages Export`
   - **URL**: Enter your Render.com app URL followed by the export endpoint:
     ```
     https://your-app-name.onrender.com/api/carpages/scheduled-export
     ```
     (Replace `your-app-name` with your actual Render app name)
   - **Authentication**: Leave set to "None" (we'll use a custom header instead)

   ### Request Details Section
   
   ![Request Details Section](https://i.imgur.com/example2.png)
   
   - **Request method**: Click the dropdown and select `POST`
   - **Notification only on error**: Check this box (recommended)
   - **Custom HTTP Headers**:
     1. Click the "Add header" button
     2. In the "Name" field, enter: `x-api-key`
     3. In the "Value" field, enter the same value you'll set for `EXPORT_API_KEY` in your Render environment variables (create a secure random string like `d8e7f6g5h4j3k2l1`)
     4. Make sure to save this value somewhere secure as you'll need it for your Render environment variables

   ### Execution Schedule Section
   
   ![Execution Schedule Section](https://i.imgur.com/example3.png)
   
   - **Execution schedule**: Select `Custom schedule`
   - Set the schedule to run daily at 2:00 AM (or your preferred time):
     - **Hours**: Enter `2`
     - **Minutes**: Enter `0`
     - **Days of month**: Enter `*` (every day)
     - **Months**: Enter `*` (every month)
     - **Days of week**: Enter `*` (every day of the week)

   ### Advanced Settings
   
   ![Advanced Settings](https://i.imgur.com/example4.png)
   
   - **Save responses**: Check this box (helps with troubleshooting)
   - **Job enabled**: Make sure this is checked

3. Review all your settings to ensure they're correct.

4. Scroll to the bottom of the form and click the green "Create" button.

## Step 3: Verify Your Cron Job

After creating the cron job, you should see it listed in your dashboard:

![Cron Job Dashboard](https://i.imgur.com/example5.png)

To verify it's set up correctly:

1. Find your "GT Motorsports CarPages Export" job in the list
2. Click on the job title to view its details
3. In the job details page, check that all settings match what you entered
4. Note: Don't test the job yet until you've set up your Render environment variables

## Step 4: Set Up Your Render Environment Variables

Before your cron job will work correctly, you need to add the API key to your Render.com environment variables:

1. Open a new browser tab and go to your Render dashboard (https://dashboard.render.com/)
2. Select your web service from the list
3. Click on the "Environment" tab in the left sidebar

   ![Render Environment Tab](https://i.imgur.com/example6.png)

4. Click the "Add Environment Variable" button
5. Add a new variable:
   - **Key**: Enter `EXPORT_API_KEY`
   - **Value**: Enter the EXACT SAME secure random string you used in the cron job's x-api-key header
   
   ![Adding Environment Variable](https://i.imgur.com/example7.png)

6. Click "Save Changes"
7. Your service will automatically redeploy with the new environment variable (this may take a few minutes)

## Step 5: Integrate the Export API Routes

Make sure you've integrated the export API routes into your main Express application:

1. Open your main Express app file (likely `backend/server.js` or similar)
2. Add the following code to import and use the CarPages export routes:

```javascript
import carpagesExportRoutes from './routes/carpagesExport.js';
// ...
app.use('/api/carpages', carpagesExportRoutes);
```

3. Deploy these changes to your Render app:
   - Commit and push your changes to your Git repository
   - Render will automatically deploy the changes (if you have auto-deploy enabled)
   - Or manually deploy from the Render dashboard

## Step 6: Test the Complete Setup

Once your Render app has finished deploying with the new environment variable:

1. Return to your cron-job.org dashboard
2. Find your "GT Motorsports CarPages Export" job in the list
3. Click on the job to view its details
4. Find and click the "Execute now" button to run the job immediately

   ![Execute Now Button](https://i.imgur.com/example8.png)

5. Wait a few moments for the job to execute
6. Check the "Last run" section to see if it executed successfully (should show a green checkmark)
7. Go to your Render dashboard and check the logs to verify the export process ran correctly:
   - In your Render dashboard, select your web service
   - Click on the "Logs" tab
   - Look for messages like "Scheduled export triggered by cron service" and "Exported X vehicles"

## Troubleshooting

If your cron job fails:

1. **Check Render Logs**:
   - Go to your Render dashboard
   - Select your web service
   - Click on the "Logs" tab
   - Look for error messages related to the export process

2. **Verify API Key**:
   - Make sure the API key in your cron job header EXACTLY matches the one in your Render environment variables
   - Even a single character difference will cause authentication to fail

3. **Check Endpoint URL**:
   - Ensure the URL is correct (check for typos)
   - Make sure your app is deployed and running
   - Try visiting your main app URL in a browser to verify it's online

4. **Test Manually**:
   - You can test the endpoint using a tool like Postman or curl:
   ```
   curl -X POST https://your-app-name.onrender.com/api/carpages/scheduled-export \
   -H "x-api-key: your-api-key-here"
   ```
   - Replace `your-app-name` with your actual Render app name
   - Replace `your-api-key-here` with your actual API key

5. **Check for Render Sleep Mode**:
   - If you're using a free Render plan, your service might be in sleep mode
   - The first request might fail but wake up your service
   - Try running the cron job again after a minute