# Setting Up Scheduled CarPages Exports on Render.com

This guide provides step-by-step instructions for setting up scheduled CarPages exports using a Background Worker on Render.com.

## Overview

Since your website is hosted on Render.com, we need to use Render's Background Worker service to handle scheduled exports instead of the Windows-based scheduler. This approach ensures that exports run reliably in the cloud environment.

## Prerequisites

- Your website is already deployed on Render.com
- You have access to the Render.com dashboard
- Your repository contains the export scripts we've created

## Step 1: Create a New Background Worker Service on Render.com

1. Log in to your Render.com dashboard
2. Click the "New +" button in the top right corner
3. Select "Background Worker" from the dropdown menu
4. Connect your repository (the same one used for your web service)
5. Configure the service with the following settings:

   - **Name**: `gt-motorsports-carpages-export` (or any name you prefer)
   - **Environment**: `Node` (select the same version as your web service)
   - **Region**: Choose the same region as your web service for best performance
   - **Branch**: `main` (or your production branch)
   - **Build Command**: `npm install` (or your standard build command)
   - **Start Command**: `bash scripts/start-render-worker.sh`

6. Under "Advanced" settings, add the following environment variables:
   
   - `VITE_SUPABASE_URL`: Your Supabase URL (copy from your web service)
   - `VITE_SUPABASE_KEY`: Your Supabase key (copy from your web service)
   - `CARPAGES_FTP_HOST`: `ftp.carpages.ca`
   - `CARPAGES_FTP_USER`: `GTMotor`
   - `CARPAGES_FTP_PASSWORD`: `kYP76iWb3AphEmbyX8GW`

## Step 2: Set Up the Cron Schedule

1. In the same Background Worker configuration page, find the "Cron Job" section
2. Check the "Run as a cron job" checkbox
3. Enter the following cron expression: `0 2 * * *` (runs at 2:00 AM UTC daily)
   - Note: Render uses UTC time, so adjust the time based on your timezone if needed
   - For example, if you want it to run at 2:00 AM Eastern Time, use `0 7 * * *` (2:00 AM ET = 7:00 AM UTC)

4. Click "Create Background Worker" to create the service

## Step 3: Verify the Setup

1. After the worker is created, Render will build and deploy it
2. Once deployed, you can view the logs to verify it's working correctly
3. The worker will run automatically according to the schedule you set

## Monitoring and Troubleshooting

- **View Logs**: Click on your worker service in the Render dashboard, then click the "Logs" tab to view execution logs
- **Manual Trigger**: You can manually trigger the worker by clicking the "Manual Deploy" button in the Render dashboard
- **Check Export Results**: The worker saves logs in the `logs` directory and exports in the `exports` directory

## Additional Notes

- The Background Worker will automatically run on the schedule you set, even if you restart your Render services
- If you need to update the export script, simply push changes to your repository and Render will automatically deploy them
- You can still use the manual export button in the admin dashboard for immediate exports when needed

## Adjusting the Schedule

If you need to change the schedule:

1. Go to your Background Worker service in the Render dashboard
2. Click "Settings"
3. Scroll down to the "Cron Job" section
4. Update the cron expression
5. Click "Save Changes"

Common cron expressions:
- `0 2 * * *`: Daily at 2:00 AM
- `0 2 * * 1`: Weekly on Monday at 2:00 AM
- `0 2 1 * *`: Monthly on the 1st at 2:00 AM