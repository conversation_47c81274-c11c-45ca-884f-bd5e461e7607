import { createRouter, createWebHistory } from 'vue-router'

// Import views directly to ensure they're loaded properly
import HomeView from '../views/HomeView.vue'
import InventoryView from '../views/InventoryView.vue'
import VehicleDetailsView from '../views/VehicleDetailsView.vue'
// TestView import removed
import FinancingView from '../views/FinancingView.vue'
import DetailingView from '../views/DetailingView.vue'
import ContactView from '../views/ContactView.vue'
import AboutPage from '../views/AboutPage.vue'
import NotFoundView from '../views/NotFoundView.vue'

// Admin views
import LoginView from '../views/admin/LoginView.vue'
import DashboardView from '../views/admin/DashboardView.vue'
import AddVehicleView from '../views/admin/AddVehicleView.vue'
import EditVehicleView from '../views/admin/EditVehicleView.vue'
import AiStudioView from '../views/admin/AiStudioView.vue'

// Auth guard for admin routes
import authStore from '../store/supabaseAuth'
const requireAuth = async (to, from, next) => {
  await authStore.initAuth()
  if (authStore.isAuthenticated.value) {
    next()
  } else {
    next('/admin/login')
  }
}
// Permission-based auth guard
const requirePermission = (resource, action) => {
  return async (to, from, next) => {
    await authStore.initAuth()
    if (!authStore.isAuthenticated.value) {
      next('/admin/login')
      return
    }
    
    if (authStore.hasPermission(resource, action)) {
      next()
    } else {
      // Redirect to dashboard with error message
      next({
        path: '/admin/dashboard',
        query: { error: 'You do not have permission to access this page' }
      })
    }
  }
}

const routes = [
  // Public routes
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: HomeView
  },
  {
    path: '/inventory',
    name: 'Inventory',
    component: InventoryView
  },
  {
    path: '/vehicle/:id',
    name: 'VehicleDetails',
    component: VehicleDetailsView
  },
  {
    path: '/financing',
    name: 'Financing',
    component: FinancingView
  },
  {
    path: '/detailing',
    name: 'Detailing',
    component: DetailingView
  },
  {
    path: '/contact',
    name: 'Contact',
    component: ContactView
  },
  {
    path: '/about',
    name: 'About',
    component: AboutPage
  },
  // TestView route removed
  
  // Admin routes
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: LoginView
  },
  {
    path: '/admin/dashboard',
    name: 'AdminDashboard',
    component: DashboardView,
    beforeEnter: requireAuth
  },
  {
    path: '/admin/vehicles/add',
    name: 'AddVehicle',
    component: AddVehicleView,
    beforeEnter: requirePermission('inventory', 'create')
  },
  {
    path: '/admin/vehicles/edit/:id',
    name: 'EditVehicle',
    component: EditVehicleView,
    beforeEnter: requirePermission('inventory', 'update')
  },
  {
    path: '/admin/vehicles/ai-studio/:id',
    name: 'AiStudio',
    component: AiStudioView,
    beforeEnter: requirePermission('inventory', 'update')
  },
  
  // Catch-all route for 404
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFoundView
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    } else {
      return { top: 0, behavior: 'smooth' }
    }
  }
})

// Global navigation guard to set the document title based on the route
router.beforeEach((to, from, next) => {
  // Set the document title to "RouteName | GT Motor Sports"
  document.title = to.name ? `${to.name} | GT Motor Sports` : 'GT Motor Sports'
  next()
})

export default router