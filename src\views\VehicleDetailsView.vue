<script setup>
import { ref, onMounted, computed, watch, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import vehicleStore from '../store/vehicles';

const route = useRoute();
const router = useRouter();
const vehicle = ref(null);
const loading = ref(true);
const error = ref(null);

// Gallery state
const currentImageIndex = ref(0);
const galleryVisible = ref(false);
const visibleThumbnailsStartIndex = ref(0);

// Preloaded images tracking
const preloadedImages = ref(new Set());

// Fetch vehicle data
const fetchVehicle = async () => {
  loading.value = true;
  error.value = null;

  try {
    const id = route.params.id;
    if (!id) {
      throw new Error("Vehicle ID is missing from the route parameters.");
    }

    console.log(`[VehicleDetailsView] Fetching vehicle with ID: ${id}`);
    const data = await vehicleStore.getVehicleById(id);

    if (!data) {
      error.value = 'Vehicle not found. It might have been removed or the link is incorrect.';
    } else {
      vehicle.value = data;
      console.log('[VehicleDetailsView] Vehicle data loaded:', vehicle.value);
    }
  } catch (err) {
    console.error('[VehicleDetailsView] Error:', err);
    error.value = `An error occurred while loading vehicle details: ${err.message}`;
  } finally {
    loading.value = false;
  }
};

// Format price
const formatPrice = (price) => {
  if (!price) return 'Call for Price';
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: 'CAD',
    maximumFractionDigits: 0
  }).format(price);
};

// Format mileage
const formatMileage = (mileage) => {
  if (!mileage) return 'N/A';
  return `${new Intl.NumberFormat('en-CA').format(mileage)} km`;
};

// Gallery functions
const galleryImages = computed(() => {
  if (!vehicle.value) return [];
  return vehicle.value.gallery && vehicle.value.gallery.length > 0
    ? vehicle.value.gallery
    : [vehicle.value.image];
});

// Visible thumbnails (sliding window of 5)
const visibleThumbnails = computed(() => {
  if (galleryImages.value.length <= 5) return galleryImages.value;

  return galleryImages.value.slice(visibleThumbnailsStartIndex.value,
    visibleThumbnailsStartIndex.value + 5);
});

// Preload images
const preloadImage = (url) => {
  if (preloadedImages.value.has(url)) return;

  const img = new Image();
  img.src = url;
  img.onload = () => {
    preloadedImages.value.add(url);
  };
};

// Preload next set of images
const preloadNextImages = () => {
  // Preload next 3 images beyond current view
  const startIdx = visibleThumbnailsStartIndex.value + 5;
  const endIdx = Math.min(startIdx + 3, galleryImages.value.length);

  for (let i = startIdx; i < endIdx; i++) {
    preloadImage(galleryImages.value[i]);
  }
};

const showImage = (index) => {
  currentImageIndex.value = index;
  updateThumbnailPosition(index);
};

// Update the thumbnail row position to keep active image centered when possible
const updateThumbnailPosition = (index) => {
  if (galleryImages.value.length <= 5) return;

  // Try to keep the selected image in the middle (position 2 of 0-4)
  let newStartIndex = Math.max(0, index - 2);

  // Ensure we don't go beyond the end
  if (newStartIndex + 5 > galleryImages.value.length) {
    newStartIndex = galleryImages.value.length - 5;
  }

  visibleThumbnailsStartIndex.value = newStartIndex;
  preloadNextImages();
};

const nextImage = () => {
  const newIndex = (currentImageIndex.value + 1) % galleryImages.value.length;
  currentImageIndex.value = newIndex;
  updateThumbnailPosition(newIndex);
};

const prevImage = () => {
  const newIndex = (currentImageIndex.value - 1 + galleryImages.value.length) % galleryImages.value.length;
  currentImageIndex.value = newIndex;
  updateThumbnailPosition(newIndex);
};

const toggleGallery = () => {
  galleryVisible.value = !galleryVisible.value;
};

// Keyboard navigation for gallery
const handleKeyDown = (e) => {
  if (galleryVisible.value) {
    if (e.key === 'ArrowRight') {
      nextImage();
    } else if (e.key === 'ArrowLeft') {
      prevImage();
    } else if (e.key === 'Escape') {
      galleryVisible.value = false;
    }
  }
};

// Group features into columns for better display
const groupedFeatures = computed(() => {
  if (!vehicle.value || !vehicle.value.features || !vehicle.value.features.length) {
    return [];
  }

  const features = [...vehicle.value.features];
  const midpoint = Math.ceil(features.length / 2);
  return [features.slice(0, midpoint), features.slice(midpoint)];
});

// Highlights for display
const highlights = computed(() => {
  if (!vehicle.value || !vehicle.value.highlights) return [];
  return vehicle.value.highlights;
});

// Format vehicle title to show only year, make, and model
const formattedVehicleTitle = computed(() => {
  if (!vehicle.value) return '';

  const { year, make, model } = vehicle.value;
  const parts = [];

  if (year) parts.push(year);
  if (make) parts.push(make);
  if (model) parts.push(model);

  return parts.join(' ');
});

// Setup event listeners
onMounted(() => {
  console.log('[VehicleDetailsView] Component mounted with route params:', route.params);
  fetchVehicle();
  window.addEventListener('keydown', handleKeyDown);
});

// Watch for vehicle data loading to preload images
watch(vehicle, (newVehicle) => {
  if (newVehicle && newVehicle.gallery && newVehicle.gallery.length > 0) {
    // Preload first 8 images (5 visible + 3 next)
    const imagesToPreload = Math.min(8, newVehicle.gallery.length);
    for (let i = 0; i < imagesToPreload; i++) {
      preloadImage(newVehicle.gallery[i]);
    }
  }
}, { immediate: true });

// Clean up event listeners
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown);
});
</script>

<template>
  <div class="bg-light min-h-screen">
    <!-- Loading State -->
    <div v-if="loading" class="min-h-[70vh] flex flex-col items-center justify-center text-center px-4">
      <div class="mb-6">
        <img src="/REDGTWHEEL.png" alt="Loading..." class="animate-spin h-24 w-24 mx-auto" />
      </div>
      <h3 class="text-xl font-semibold text-gray-700 animate-pulse">Loading Vehicle Details</h3>
      <p class="text-gray-500">Please wait...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="min-h-[70vh] flex flex-col items-center justify-center text-center px-4">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-secondary mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
        <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <h3 class="text-xl font-semibold text-secondary mb-2">Loading Failed</h3>
      <p class="text-gray-600 max-w-md">{{ error }}</p>
      <div class="mt-6">
        <router-link :to="{ name: 'Inventory' }" class="bg-accent hover:bg-accent/90 text-white px-4 py-2 rounded transition-all duration-300">
          Back to Inventory
        </router-link>
      </div>
    </div>

    <!-- Vehicle Details Content -->
    <div v-else-if="vehicle" class="container mx-auto px-4 py-8">
      <!-- Back Button -->
      <div class="mb-6">
        <router-link :to="{ name: 'Inventory' }" class="inline-flex items-center text-sm font-medium text-gray-600 hover:text-accent transition-colors duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Inventory
        </router-link>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column - Images -->
        <div class="lg:col-span-2">
          <!-- Main Image with Overlay -->
          <div class="relative bg-gray-100 rounded-lg overflow-hidden mb-4 shadow-md group">
            <!-- Fixed aspect ratio container -->
            <div class="w-full pb-[66.67%] relative">
              <img
                :src="galleryImages[currentImageIndex]"
                :alt="formattedVehicleTitle"
                class="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                @click="toggleGallery"
                onerror="this.onerror=null; this.src='/images/no-image-available.jpg'; this.classList.add('error-image');"
                @error="$event.target.src='/images/no-image-available.jpg'; $event.target.classList.add('error-image');"
              />
            </div>

            <!-- Image Navigation Overlay -->
            <div class="absolute inset-0 flex items-center justify-between px-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button
                @click.stop="prevImage"
                class="bg-primary/80 hover:bg-primary text-white rounded-full p-2 transform transition-transform duration-300 hover:scale-110"
                v-if="galleryImages.length > 1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <button
                @click.stop="nextImage"
                class="bg-primary/80 hover:bg-primary text-white rounded-full p-2 transform transition-transform duration-300 hover:scale-110"
                v-if="galleryImages.length > 1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            <!-- Expand Gallery Button -->
            <button
              @click.stop="toggleGallery"
              class="absolute bottom-4 right-4 bg-primary/80 hover:bg-primary text-white rounded-full p-2 transform transition-all duration-300 hover:scale-110"
              title="View Full Gallery"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
              </svg>
            </button>

            <!-- Image Counter -->
            <div class="absolute bottom-4 left-4 bg-primary/80 text-white text-xs px-2 py-1 rounded-full" v-if="galleryImages.length > 1">
              {{ currentImageIndex + 1 }} / {{ galleryImages.length }}
            </div>
          </div>

          <!-- Thumbnail Gallery -->
          <div v-if="galleryImages.length > 1" class="relative mt-2">
            <div class="grid grid-cols-5 gap-2 transition-all duration-300 ease-in-out">
              <div
                v-for="(image, relativeIndex) in visibleThumbnails"
                :key="visibleThumbnailsStartIndex + relativeIndex"
                class="bg-gray-100 rounded-lg overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-md"
                :class="{'ring-2 ring-accent': currentImageIndex === visibleThumbnailsStartIndex + relativeIndex}"
                @click="showImage(visibleThumbnailsStartIndex + relativeIndex)"
              >
                <!-- Fixed aspect ratio for thumbnails -->
                <div class="w-full pb-[66.67%] relative">
                  <img
                    :src="image"
                    :alt="`${formattedVehicleTitle} - Image ${visibleThumbnailsStartIndex + relativeIndex + 1}`"
                    class="absolute inset-0 w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                    onerror="this.onerror=null; this.src='/images/no-image-available.jpg'; this.classList.add('error-image');"
                    @error="$event.target.src='/images/no-image-available.jpg'; $event.target.classList.add('error-image');"
                  />
                </div>
              </div>
            </div>

            <!-- Navigation arrows for thumbnail strip -->
            <div v-if="galleryImages.length > 5" class="absolute inset-y-0 left-0 right-0 flex items-center justify-between pointer-events-none">
              <button
                @click.stop="visibleThumbnailsStartIndex = Math.max(0, visibleThumbnailsStartIndex - 1)"
                class="bg-primary/70 hover:bg-primary text-white rounded-full p-1 transform transition-all duration-300 hover:scale-110 pointer-events-auto"
                :class="{'opacity-50 cursor-not-allowed': visibleThumbnailsStartIndex === 0}"
                :disabled="visibleThumbnailsStartIndex === 0"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <button
                @click.stop="visibleThumbnailsStartIndex = Math.min(galleryImages.length - 5, visibleThumbnailsStartIndex + 1)"
                class="bg-primary/70 hover:bg-primary text-white rounded-full p-1 transform transition-all duration-300 hover:scale-110 pointer-events-auto"
                :class="{'opacity-50 cursor-not-allowed': visibleThumbnailsStartIndex >= galleryImages.length - 5}"
                :disabled="visibleThumbnailsStartIndex >= galleryImages.length - 5"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            <!-- Image counter -->
            <div class="absolute -bottom-6 right-0 text-xs text-gray-500">
              {{ currentImageIndex + 1 }} / {{ galleryImages.length }}
            </div>
          </div>

          <!-- Highlights Section -->
          <div v-if="highlights.length > 0" class="mt-8 bg-white rounded-lg shadow-md p-6 transform transition-all duration-500 hover:shadow-lg">
            <h3 class="text-xl font-bold text-primary mb-4 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
              Highlights
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div
                v-for="(highlight, index) in highlights"
                :key="index"
                class="bg-light rounded-lg p-3 flex items-center transform transition-all duration-300 hover:translate-y-[-5px] hover:shadow-md"
              >
                <div class="bg-secondary/10 text-secondary rounded-full p-2 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span class="font-medium">{{ highlight }}</span>
              </div>
            </div>
          </div>

          <!-- Description Section -->
          <div v-if="vehicle.description" class="mt-6 bg-white rounded-lg shadow-md p-6 transform transition-all duration-500 hover:shadow-lg">
            <h3 class="text-xl font-bold text-primary mb-4 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Description
            </h3>
            <p class="text-gray-700 leading-relaxed">{{ vehicle.description }}</p>
          </div>

          <!-- Features Section -->
          <div v-if="vehicle.features && vehicle.features.length > 0" class="mt-6 bg-white rounded-lg shadow-md p-6 transform transition-all duration-500 hover:shadow-lg">
            <h3 class="text-xl font-bold text-primary mb-4 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Features
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
              <ul class="space-y-2" v-for="(column, colIndex) in groupedFeatures" :key="colIndex">
                <li v-for="(feature, index) in column" :key="index" class="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="text-gray-700">{{ feature }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Right Column - Details -->
        <div class="bg-white rounded-lg shadow-lg p-6 h-fit sticky top-24">
          <h1 class="text-2xl font-bold text-primary mb-2 animate-fadeIn">{{ formattedVehicleTitle }}</h1>
          <p v-if="vehicle.trim" class="text-lg text-gray-500 mb-3 animate-fadeIn animation-delay-100">{{ vehicle.trim }}</p>

          <div class="flex items-baseline gap-2 mb-4 animate-fadeIn animation-delay-200">
            <span class="text-3xl font-bold text-accent">{{ formatPrice(vehicle.specialPrice || vehicle.price) }}</span>
            <span v-if="vehicle.specialPrice" class="text-lg text-gray-400 line-through">{{ formatPrice(vehicle.price) }}</span>
          </div>

          <!-- Quick Stats -->
          <div class="border-t border-gray-200 py-4 animate-fadeIn animation-delay-300">
            <div class="grid grid-cols-2 gap-4">
              <div class="flex items-center group">
                <div class="bg-accent/10 p-2 rounded-full mr-3 group-hover:bg-accent/20 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <span class="block text-gray-500 text-sm">Mileage</span>
                  <span class="font-semibold">{{ formatMileage(vehicle.mileage) }}</span>
                </div>
              </div>

              <div class="flex items-center group">
                <div class="bg-accent/10 p-2 rounded-full mr-3 group-hover:bg-accent/20 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <span class="block text-gray-500 text-sm">Year</span>
                  <span class="font-semibold">{{ vehicle.year || 'N/A' }}</span>
                </div>
              </div>

              <div class="flex items-center group">
                <div class="bg-accent/10 p-2 rounded-full mr-3 group-hover:bg-accent/20 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div>
                  <span class="block text-gray-500 text-sm">Transmission</span>
                  <span class="font-semibold">{{ vehicle.transmission || 'N/A' }}</span>
                </div>
              </div>

              <div class="flex items-center group">
                <div class="bg-accent/10 p-2 rounded-full mr-3 group-hover:bg-accent/20 transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <span class="block text-gray-500 text-sm">Fuel Type</span>
                  <span class="font-semibold">{{ vehicle.fuelType || 'N/A' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Additional Details -->
          <div class="border-t border-gray-200 py-4 animate-fadeIn animation-delay-400">
            <h3 class="font-semibold text-primary mb-3">Additional Details</h3>
            <div class="grid grid-cols-2 gap-x-4 gap-y-2">
              <div v-if="vehicle.make" class="flex justify-between">
                <span class="text-gray-500 text-sm">Make:</span>
                <span class="font-medium text-gray-700">{{ vehicle.make }}</span>
              </div>
              <div v-if="vehicle.model" class="flex justify-between">
                <span class="text-gray-500 text-sm">Model:</span>
                <span class="font-medium text-gray-700">{{ vehicle.model }}</span>
              </div>
              <div v-if="vehicle.bodyStyle" class="flex justify-between">
                <span class="text-gray-500 text-sm">Body Style:</span>
                <span class="font-medium text-gray-700">{{ vehicle.bodyStyle }}</span>
              </div>
              <div v-if="vehicle.exteriorColor" class="flex justify-between">
                <span class="text-gray-500 text-sm">Exterior:</span>
                <span class="font-medium text-gray-700">{{ vehicle.exteriorColor }}</span>
              </div>
              <div v-if="vehicle.interiorColor" class="flex justify-between">
                <span class="text-gray-500 text-sm">Interior:</span>
                <span class="font-medium text-gray-700">{{ vehicle.interiorColor }}</span>
              </div>
              <div v-if="vehicle.engine" class="flex justify-between">
                <span class="text-gray-500 text-sm">Engine:</span>
                <span class="font-medium text-gray-700">{{ vehicle.engine }}</span>
              </div>
              <div v-if="vehicle.engineSize" class="flex justify-between">
                <span class="text-gray-500 text-sm">Engine Size:</span>
                <span class="font-medium text-gray-700">{{ vehicle.engineSize }}</span>
              </div>
              <div v-if="vehicle.drivetrain" class="flex justify-between">
                <span class="text-gray-500 text-sm">Drivetrain:</span>
                <span class="font-medium text-gray-700">{{ vehicle.drivetrain }}</span>
              </div>
              <div v-if="vehicle.doors" class="flex justify-between">
                <span class="text-gray-500 text-sm">Doors:</span>
                <span class="font-medium text-gray-700">{{ vehicle.doors }}</span>
              </div>
              <div v-if="vehicle.passengers" class="flex justify-between">
                <span class="text-gray-500 text-sm">Passengers:</span>
                <span class="font-medium text-gray-700">{{ vehicle.passengers }}</span>
              </div>
              <div v-if="vehicle.cityFuel" class="flex justify-between">
                <span class="text-gray-500 text-sm">City Fuel:</span>
                <span class="font-medium text-gray-700">{{ vehicle.cityFuel }}</span>
              </div>
              <div v-if="vehicle.hwyFuel" class="flex justify-between">
                <span class="text-gray-500 text-sm">Hwy Fuel:</span>
                <span class="font-medium text-gray-700">{{ vehicle.hwyFuel }}</span>
              </div>
            </div>
          </div>

          <!-- VIN and Stock Number -->
          <div class="border-t border-gray-200 py-4 animate-fadeIn animation-delay-500">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
              <div class="space-y-2">
                <div v-if="vehicle.vin" class="flex items-center">
                  <span class="text-gray-500 text-sm mr-2">VIN:</span>
                  <span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">{{ vehicle.vin }}</span>
                </div>
                <div v-if="vehicle.stockNumber" class="flex items-center">
                  <span class="text-gray-500 text-sm mr-2">Stock #:</span>
                  <span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">{{ vehicle.stockNumber }}</span>
                </div>
              </div>
              
              <!-- Carfax Link Button -->
              <div class="mt-3 md:mt-0 flex flex-col items-center">
                <!-- Green "Carfax Available!" text when link exists -->
                <span v-if="vehicle.carfaxLink" class="text-green-600 text-xs font-semibold mb-1">
                  Carfax Available!
                </span>
                
                <!-- Active link when carfax exists -->
                <a
                  v-if="vehicle.carfaxLink"
                  :href="vehicle.carfaxLink"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="transition-transform hover:scale-105 duration-300"
                  title="View Carfax Report"
                >
                  <img
                    src="/CARFAXLOGO.png"
                    alt="Carfax Report Available"
                    class="h-8 object-contain"
                  />
                </a>
                
                <!-- Greyed out image when no link exists -->
                <div
                  v-else
                  class="opacity-50 cursor-not-allowed"
                  title="Carfax Report Not Available"
                >
                  <img
                    src="/CARFAXLOGO.png"
                    alt="Carfax Report Not Available"
                    class="h-8 object-contain grayscale"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Contact Buttons -->
          <div class="mt-6 space-y-3 animate-fadeIn animation-delay-600">
            <router-link :to="{ name: 'Contact' }" class="block w-full bg-secondary hover:bg-secondary/90 text-white text-center py-3 px-4 rounded-md transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Inquire Now
            </router-link>
            <router-link :to="{ name: 'Financing' }" class="block w-full bg-white hover:bg-gray-50 text-primary border border-primary text-center py-3 px-4 rounded-md transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Apply for Financing
            </router-link>
            <a href="tel:+1234567890" class="block w-full bg-white hover:bg-gray-50 text-accent border border-accent text-center py-3 px-4 rounded-md transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              Call Now
           </a>
         </div>
       </div>
     </div>
   </div>

   <!-- Fullscreen Gallery Modal -->
   <div v-if="galleryVisible" class="fixed inset-0 bg-black/90 z-50 flex items-center justify-center">
     <!-- Close Button -->
     <button
       @click="toggleGallery"
       class="absolute top-4 right-4 text-white hover:text-secondary transition-colors duration-300"
     >
       <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
       </svg>
     </button>

     <!-- Main Image -->
     <div class="relative w-full max-w-4xl">
       <!-- Fixed aspect ratio container for fullscreen view -->
       <div class="w-full max-w-4xl mx-auto relative" style="max-height: 80vh;">
         <div class="pb-[66.67%] relative">
           <img
             :src="galleryImages[currentImageIndex]"
             :alt="formattedVehicleTitle"
             class="absolute inset-0 w-full h-full object-contain"
             onerror="this.onerror=null; this.src='/images/no-image-available.jpg'; this.classList.add('error-image');"
             @error="$event.target.src='/images/no-image-available.jpg'; $event.target.classList.add('error-image');"
           />
         </div>
       </div>

       <!-- Navigation Controls -->
       <div class="absolute inset-x-0 top-1/2 transform -translate-y-1/2 flex justify-between px-4">
         <button
           @click.stop="prevImage"
           class="bg-primary/50 hover:bg-primary text-white rounded-full p-3 transform transition-transform duration-300 hover:scale-110"
           v-if="galleryImages.length > 1"
         >
           <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
           </svg>
         </button>

         <button
           @click.stop="nextImage"
           class="bg-primary/50 hover:bg-primary text-white rounded-full p-3 transform transition-transform duration-300 hover:scale-110"
           v-if="galleryImages.length > 1"
         >
           <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
           </svg>
         </button>
       </div>

       <!-- Image Counter -->
       <div class="absolute bottom-4 left-4 bg-primary/80 text-white px-3 py-1 rounded-full">
         {{ currentImageIndex + 1 }} / {{ galleryImages.length }}
       </div>
     </div>

     <!-- Thumbnail Strip -->
     <div class="absolute bottom-8 inset-x-0 flex justify-center">
       <div class="flex space-x-2 px-4 py-2 bg-black/50 rounded-lg overflow-hidden max-w-4xl relative">
         <!-- Visible area with smooth scrolling -->
         <div
           class="flex space-x-2 transition-transform duration-300 ease-in-out"
           :style="{ transform: `translateX(${-Math.max(0, currentImageIndex - 2) * 68}px)` }"
         >
           <div
             v-for="(image, index) in galleryImages"
             :key="index"
             class="w-16 h-16 flex-shrink-0 rounded overflow-hidden cursor-pointer transition-all duration-300"
             :class="{'ring-2 ring-secondary': currentImageIndex === index, 'opacity-60': currentImageIndex !== index}"
             @click="showImage(index)"
           >
             <div class="w-full h-full relative">
               <img
                 :src="image"
                 :alt="`${formattedVehicleTitle} - Image ${index + 1}`"
                 class="absolute inset-0 w-full h-full object-cover"
                 onerror="this.onerror=null; this.src='/images/no-image-available.jpg'; this.classList.add('error-image');"
                 @error="$event.target.src='/images/no-image-available.jpg'; $event.target.classList.add('error-image');"
               />
             </div>
           </div>
         </div>

         <!-- Navigation arrows for fullscreen thumbnail strip -->
         <div class="absolute inset-y-0 left-0 right-0 flex items-center justify-between pointer-events-none px-2">
           <button
             @click.stop="prevImage()"
             class="bg-primary/70 hover:bg-primary text-white rounded-full p-1 transform transition-all duration-300 hover:scale-110 pointer-events-auto"
             v-if="currentImageIndex > 0"
           >
             <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
             </svg>
           </button>

           <button
             @click.stop="nextImage()"
             class="bg-primary/70 hover:bg-primary text-white rounded-full p-1 transform transition-all duration-300 hover:scale-110 pointer-events-auto"
             v-if="currentImageIndex < galleryImages.length - 1"
           >
             <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
             </svg>
           </button>
         </div>
       </div>
     </div>
   </div>
 </div>
</template>

<style scoped>
/* Animation classes */
.animate-fadeIn {
 animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
 from { opacity: 0; transform: translateY(10px); }
 to { opacity: 1; transform: translateY(0); }
}

.animation-delay-100 {
 animation-delay: 100ms;
}

.animation-delay-200 {
 animation-delay: 200ms;
}

.animation-delay-300 {
 animation-delay: 300ms;
}

.animation-delay-400 {
 animation-delay: 400ms;
}

.animation-delay-500 {
 animation-delay: 500ms;
}

.animation-delay-600 {
 animation-delay: 600ms;
}
</style>
