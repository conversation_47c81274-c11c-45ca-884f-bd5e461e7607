-- Enable Row Level Security and create access policies for tenant-specific tables
-- These policies ensure users can only access data belonging to their own tenant
-- using the tenant_id stored in their app_metadata

-- GTINV table (inventory)
ALTER TABLE public.GTINV ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow tenant access based on metadata" ON public.GTINV
  FOR ALL
  USING (tenant_id = auth.current_tenant_id())
  WITH CHECK (tenant_id = auth.current_tenant_id());

-- settings table
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow tenant access based on metadata" ON public.settings
  FOR ALL
  USING (tenant_id = auth.current_tenant_id())
  WITH CHECK (tenant_id = auth.current_tenant_id());

-- users table (public user profiles)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow tenant access based on metadata" ON public.users
  FOR ALL
  USING (tenant_id = auth.current_tenant_id())
  WITH CHECK (tenant_id = auth.current_tenant_id());

-- user_roles table
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow tenant access based on metadata" ON public.user_roles
  FOR ALL
  USING (tenant_id = auth.current_tenant_id())
  WITH CHECK (tenant_id = auth.current_tenant_id());

-- role_permissions table
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow tenant access based on metadata" ON public.role_permissions
  FOR ALL
  USING (tenant_id = auth.current_tenant_id())
  WITH CHECK (tenant_id = auth.current_tenant_id());

-- These policies:
-- 1. Enable Row Level Security on each tenant-specific table
-- 2. Create a policy that applies to all operations (SELECT, INSERT, UPDATE, DELETE)
-- 3. Use the auth.current_tenant_id() function to restrict access to rows matching the user's tenant_id
-- 4. Apply the same restriction for both reading (USING) and writing (WITH CHECK) operations