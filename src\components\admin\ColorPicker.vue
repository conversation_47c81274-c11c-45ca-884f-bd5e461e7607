<template>
  <div class="color-picker">
    <label :for="id" class="block text-sm font-medium text-gray-800">{{ label }}</label>
    <div class="mt-1 flex items-center">
      <div 
        class="w-8 h-8 rounded-md border border-gray-300 cursor-pointer mr-2 flex-shrink-0"
        :style="{ backgroundColor: modelValue }"
        @click="openPicker"
      ></div>
      <input 
        :id="id" 
        v-model="colorValue" 
        type="text" 
        class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-300 rounded-md px-3 py-2"
        @input="updateColor"
      />
      <input 
        ref="colorInput"
        type="color" 
        :value="modelValue" 
        class="sr-only"
        @input="updateFromPicker"
      />
    </div>
    <p v-if="description" class="mt-1 text-sm text-gray-500">{{ description }}</p>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    required: true
  },
  label: {
    type: String,
    required: true
  },
  id: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

const colorInput = ref(null);
const colorValue = ref(props.modelValue);

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  colorValue.value = newValue;
});

// Open the color picker
const openPicker = () => {
  colorInput.value.click();
};

// Update color from text input
const updateColor = () => {
  // Validate if it's a valid hex color
  if (/^#([0-9A-F]{3}){1,2}$/i.test(colorValue.value)) {
    emit('update:modelValue', colorValue.value);
  }
};

// Update color from color picker
const updateFromPicker = (event) => {
  colorValue.value = event.target.value;
  emit('update:modelValue', event.target.value);
};
</script>
