# GT Motorsports CarPages Export

This tool exports your vehicle inventory to CarPages.ca using the specifications provided in the CarPages Data Provider Integration Guide.

## Overview

The CarPages export system consists of:

1. **Server-side Node.js script** - Generates the CSV file and uploads it to the CarPages FTP server
2. **Scheduling system** - Allows you to run exports automatically on a daily/weekly basis (RECOMMENDED)
3. **Batch files** - Provides easy one-click execution for Windows users
4. **Auto-export functionality** - Optionally triggers exports when inventory changes (see [AUTO_EXPORT.md](./AUTO_EXPORT.md))

## Setup Instructions

1. Install the required dependencies:
   ```
   cd scripts
   npm install
   ```

2. Run the export manually:
   - On Windows: Double-click the `export-to-carpages.bat` file in the root directory
   - On command line: 
     ```
     cd scripts
     npm run export
     ```

## Automatic Exports

## Best Practices for CarPages Integration

According to the official CarPages Data Provider Integration Guide, inventory updates are expected on a **daily or weekly basis** rather than in real-time. Based on this guidance, we recommend the following approaches (in order of preference):

1. **Scheduled Daily Exports (RECOMMENDED)**: Set up the scheduler to run exports once daily during off-hours (e.g., 2:00 AM).
2. **Manual Exports**: Trigger exports manually when you've made significant changes to your inventory.
3. **Auto-Export (Use Sparingly)**: Enable only when you need real-time updates for critical inventory changes.

### Inventory Change Triggers (Optional)

The system can optionally trigger a CarPages export whenever a vehicle is added, updated with significant changes, or deleted. However, this approach sends your entire inventory to CarPages with each change, which may be inefficient for large inventories or frequent changes.

For more details on this feature and best practices, see [AUTO_EXPORT.md](./AUTO_EXPORT.md).

### Scheduled Exports

#### Using the Built-in Scheduler (Windows Only)

The easiest way to set up scheduled exports on a Windows machine is to use the built-in scheduler:

1. On Windows: Double-click the `schedule-export.bat` file in the root directory
2. On command line:
   ```
   cd scripts
   npm run schedule
   ```

This will start a Node.js process that runs in the background and executes the export daily at 2:00 AM.

**Note:** The scheduler process must remain running for scheduled exports to work. If your server restarts, you'll need to restart the scheduler.

#### Render.com Options (For Cloud Hosting)

If your website is hosted on Render.com, you have two options:

**Option 1: Free API Endpoint + External Cron Service (Recommended)**

1. We've created an API endpoint in `backend/routes/carpagesExport.js` that can be integrated into your existing web service
2. This can be triggered by a free external cron service (like Cron-job.org)
3. Follow the detailed instructions in [FREE_SETUP.md](./FREE_SETUP.md) for this no-cost solution
4. For step-by-step Cron-job.org setup with screenshots, see [CRON_JOB_SETUP.md](./CRON_JOB_SETUP.md)

**Option 2: Dedicated Background Worker (Paid)**

1. For higher reliability, you can use a dedicated Background Worker on Render.com (additional cost applies)
2. We've created the necessary files:
   - `scripts/render-export-worker.js`: The worker script
   - `scripts/start-render-worker.sh`: The startup script for Render
3. Follow the detailed instructions in [RENDER_WORKER_SETUP.md](./RENDER_WORKER_SETUP.md) for a comprehensive step-by-step guide with screenshots
4. For a simpler overview, see [RENDER_SETUP.md](./RENDER_SETUP.md)

Both approaches ensure exports run reliably in the cloud environment and will continue to work even if your server restarts.

### Windows Task Scheduler (Recommended for Windows Servers)

For a more robust solution on Windows servers:

1. Open Task Scheduler (search for "Task Scheduler" in the Start menu)
2. Click "Create Basic Task"
3. Enter a name (e.g., "GT Motorsports CarPages Export") and description
4. Select "Daily" for the trigger
5. Choose a start time (e.g., 2:00 AM)
6. Select "Start a program" for the action
7. Browse to the location of `export-to-carpages.bat` file
8. Click "Finish"

### Linux/Mac Cron Job (Recommended for Linux/Mac Servers)

1. Open terminal
2. Edit crontab: `crontab -e`
3. Add the following line to run the export daily at 2:00 AM:
   ```
   0 2 * * * cd /path/to/your/project/scripts && npm run export
   ```
4. Save and exit

## Export Details

The export process:
1. Fetches all vehicles from your database
2. Maps your vehicle data to the CarPages format
3. Generates a CSV file
4. Uploads it to the CarPages FTP server using the provided credentials

## FTP Credentials

- Host: ftp.carpages.ca
- Username: GTMotor
- Password: kYP76iWb3AphEmbyX8GW

## Field Mapping

The following fields are mapped from your database to the CarPages format:

| CarPages Field | Source Field |
|---------------|-------------|
| Dealer ID | "GTMotor" (static) |
| Vehicle ID | id |
| Year | year |
| Make | make |
| Model | model |
| VIN | vin |
| Stock Number | stockNumber |
| Sub-model | trim |
| New Flag | Based on isNew flag |
| Transmission | Mapped from transmission |
| Drive Type | Mapped from drivetrain |
| Odometer | mileage |
| Odometer Type | "KM" (static) |
| Doors | doors |
| Body Style | Mapped from bodyStyle |
| Engine Cylinders | Extracted from engine description |
| Exterior Colour | exteriorColor |
| Interior Colour | interiorColor |
| Condition | Mapped from condition |
| Certified Flag | Based on isCertified flag |
| Fuel Type | Mapped from fuelType |
| Price | price |
| Sale Price | specialPrice |
| Features | Comma-separated features |
| Comments | description |
| Image URLs | Comma-separated gallery URLs |
| Video Code | videoUrl |
| Last Modified Date | updatedAt |

## Logs

Export logs are stored in the `scripts/logs` directory:
- `export-YYYY-MM-DDTHH-MM-SS.log` - Log of each successful export
- `export-run-YYYY-MM-DDTHH-MM-SS.log` - Detailed log of each export run
- `scheduler.log` - Log of the scheduler activity

## Troubleshooting

If you encounter issues with the export:

1. Check the logs in the `scripts/logs` directory
2. Verify your FTP credentials are correct
3. Ensure your database is accessible
4. Check that your vehicle data contains the required fields
5. Verify your internet connection

### Common Issues

**FTP Connection Errors**
- Check if your server can connect to ftp.carpages.ca
- Verify the FTP credentials are correct
- Check if your firewall is blocking FTP connections

**No Vehicles Exported**
- Check if your database contains active vehicle listings
- Verify the database connection is working

**Script Execution Errors**
- Make sure Node.js is installed (v14+ recommended)
- Check that all dependencies are installed (`npm install` in the scripts directory)

## Support

For further assistance, contact your system administrator or IT support team.