{"name": "gtrebuild", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "cd backend && node server.js", "install-backend": "cd backend && npm install", "render-build": "npm install && npm run install-backend && npm run build"}, "dependencies": {"@aws-sdk/client-s3": "^3.772.0", "@google/genai": "^0.7.0", "@google/generative-ai": "^0.24.1", "@headlessui/vue": "^1.7.23", "@lottiefiles/vue-lottie-player": "^1.1.0", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/postcss": "^4.0.14", "axios": "^1.8.4", "basic-ftp": "^5.0.5", "dotenv": "^16.5.0", "lodash-es": "^4.17.21", "postcss-import": "^16.1.0", "three": "^0.174.0", "ts-node": "^10.9.2", "tsparticles": "^3.8.1", "typescript": "^5.8.3", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue3-particles": "^2.12.0"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.21", "esbuild": "^0.25.2", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^6.2.0", "yarn": "^1.22.22"}}