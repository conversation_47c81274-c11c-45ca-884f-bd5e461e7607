<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import VehicleForm from '../../components/admin/VehicleForm.vue';
import AdminNavbar from '../../components/admin/AdminNavbar.vue';
import authStore from '../../store/supabaseAuth';
import vehicleStore from '../../store/vehicles';

const router = useRouter();
const route = useRoute();
const vehicle = ref(null);
const isLoading = ref(true);
const error = ref(null);

// Get vehicle ID from route params
const vehicleId = route.params.id;

// Check authentication and load vehicle data
onMounted(async () => {
  authStore.initAuth();
  if (!authStore.isAuthenticated.value) {
    router.push('/admin/login');
    return;
  }
  
  // Load vehicle data
  await loadVehicleData();
});

// Load vehicle data
const loadVehicleData = async () => {
  isLoading.value = true;
  error.value = null;
  
  try {
    const vehicleData = await vehicleStore.getVehicleById(vehicleId);
    
    if (vehicleData) {
      vehicle.value = vehicleData;
    } else {
      error.value = 'Vehicle not found';
      setTimeout(() => {
        router.push('/admin/dashboard');
      }, 2000);
    }
  } catch (err) {
    console.error('Error loading vehicle:', err);
    error.value = 'Error loading vehicle data';
  } finally {
    isLoading.value = false;
  }
};

// Handle form submission
const handleSubmit = (vehicleData) => {
  // Update the vehicle
  vehicleStore.updateVehicle(vehicleId, vehicleData);
  
  // Show success message and redirect to dashboard
  alert('Vehicle updated successfully!');
  router.push('/admin/dashboard');
};

// Handle cancel
const handleCancel = () => {
  router.push('/admin/dashboard');
};
</script>

<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Admin Header -->
    <AdminNavbar />
    
    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center h-64">
        <svg class="animate-spin h-10 w-10 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
      
      <!-- Error State -->
      <div v-else-if="error" class="bg-white shadow overflow-hidden sm:rounded-lg p-6 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ error }}</h3>
        <p class="text-gray-600 mb-4">Redirecting to dashboard...</p>
      </div>
      
      <!-- Vehicle Form -->
      <VehicleForm
        v-else
        v-model:vehicle="vehicle"
        :is-edit="true"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </main>
  </div>
</template>