import { ref, computed, reactive } from 'vue'
import { getBusinessInfo as fetchBusinessInfo, updateBusinessInfo } from '../database/settingsService'

// Reactive state for business information
const businessInfo = reactive({
  about: {
    title: '',
    description: '',
    mission: '',
    vision: '',
    history: '',
    slogan: '', // New field for company slogan
    logo: '',   // New field for logo URL
    timeline: [] // New field for timeline events
  },
  reviews: {
    home: [],
    financing: [],
    detailing: []
  },
  services: [],
  contact: {
    address: '',
    phone: '',
    email: '',
    hours: [
      { day: 'Monday', open: '', close: '' },
      { day: 'Tuesday', open: '', close: '' },
      { day: 'Wednesday', open: '', close: '' },
      { day: 'Thursday', open: '', close: '' },
      { day: 'Friday', open: '', close: '' },
      { day: 'Saturday', open: '', close: '' },
      { day: 'Sunday', open: '', close: '' }
    ]
  },
  social: {      // New section for social media
    facebook: '',
    instagram: '',
    twitter: '',
    linkedin: '',
    youtube: ''
  }
})

// Loading and error states
const isLoading = ref(false)
const isInitialized = ref(false)
const isSaving = ref(false)
const saveSuccess = ref(false)
const saveError = ref(false)
const errorMessage = ref('')

// Computed getters for business information
const getBusinessTitle = computed(() => businessInfo.about.title)
const getBusinessDescription = computed(() => businessInfo.about.description)
const getBusinessMission = computed(() => businessInfo.about.mission)
const getBusinessVision = computed(() => businessInfo.about.vision)
const getBusinessHistory = computed(() => businessInfo.about.history)
const getBusinessSlogan = computed(() => businessInfo.about.slogan) // New getter
const getBusinessLogo = computed(() => businessInfo.about.logo) // New getter
const getBusinessTimeline = computed(() => businessInfo.about.timeline) // New getter for timeline events
const getBusinessReviews = computed(() => businessInfo.reviews)
const getBusinessServices = computed(() => businessInfo.services)
const getBusinessAddress = computed(() => businessInfo.contact.address)
const getBusinessPhone = computed(() => businessInfo.contact.phone)
const getBusinessEmail = computed(() => businessInfo.contact.email)
const getBusinessHours = computed(() => businessInfo.contact.hours)
const getBusinessSocial = computed(() => businessInfo.social) // New getter
const getBusinessInfo = computed(() => businessInfo)

// Initialize the store by loading business information from the database
const initStore = async () => {
  if (isInitialized.value) return businessInfo
  
  try {
    isLoading.value = true
    saveError.value = false
    errorMessage.value = ''
    
    // Fetch business information from the database
    const businessInfoFromDB = await fetchBusinessInfo()
    console.log("Raw business info from DB:", businessInfoFromDB)
    
    if (businessInfoFromDB && typeof businessInfoFromDB === 'object') {
      // Update the local reactive object with fetched data
      
      // Handle about section
      if (businessInfoFromDB.about) {
        Object.keys(businessInfo.about).forEach(key => {
          if (businessInfoFromDB.about[key] !== undefined && businessInfoFromDB.about[key] !== null) {
            businessInfo.about[key] = businessInfoFromDB.about[key]
          }
        })
      }
      
      // Handle reviews
      if (businessInfoFromDB.reviews) {
        // Handle new categorized reviews structure
        if (typeof businessInfoFromDB.reviews === 'object' && !Array.isArray(businessInfoFromDB.reviews)) {
          // New structure with categories
          ['home', 'financing', 'detailing'].forEach(category => {
            if (Array.isArray(businessInfoFromDB.reviews[category])) {
              businessInfo.reviews[category] = businessInfoFromDB.reviews[category].map((review, index) => ({
                id: review.id || Date.now() + index,
                ...review
              }))
            } else {
              businessInfo.reviews[category] = []
            }
          })
        } else if (Array.isArray(businessInfoFromDB.reviews)) {
          // Handle legacy format (migrate old reviews to home category)
          businessInfo.reviews.home = businessInfoFromDB.reviews.map((review, index) => ({
            id: review.id || Date.now() + index,
            ...review
          }))
        }
      }
      
      // Handle services
      if (Array.isArray(businessInfoFromDB.services)) {
        businessInfo.services = businessInfoFromDB.services.map((service, index) => ({
          id: service.id || Date.now() + index + 1000,
          ...service
        }))
      }
      // Handle contact information
      if (businessInfoFromDB.contact) {
        // Update basic contact info
        ['address', 'phone', 'email'].forEach(key => {
          if (businessInfoFromDB.contact[key] !== undefined && businessInfoFromDB.contact[key] !== null) {
            businessInfo.contact[key] = businessInfoFromDB.contact[key]
          }
        })
        
        // Update business hours
        if (Array.isArray(businessInfoFromDB.contact.hours) && businessInfoFromDB.contact.hours.length === businessInfo.contact.hours.length) {
          businessInfoFromDB.contact.hours.forEach((hourInfo, index) => {
            businessInfo.contact.hours[index] = {
              ...businessInfo.contact.hours[index], // Keep the original day name
              open: hourInfo.open ?? '',
              close: hourInfo.close ?? ''
            }
          })
        }
      }
      
      // Handle social media information
      if (businessInfoFromDB.social) {
        Object.keys(businessInfo.social).forEach(key => {
          if (businessInfoFromDB.social[key] !== undefined && businessInfoFromDB.social[key] !== null) {
            businessInfo.social[key] = businessInfoFromDB.social[key]
          }
        })
      }
      
      console.log("Business information processed:", JSON.parse(JSON.stringify(businessInfo)))
    } else {
      console.warn("No business information found in database or invalid format returned.")
      errorMessage.value = "No business information found in settings. Using defaults."
      saveError.value = true
    }
  } catch (error) {
    console.error("Failed to fetch business information:", error)
    errorMessage.value = "Failed to load business information from the database."
    saveError.value = true
  } finally {
    isLoading.value = false
    isInitialized.value = true
    console.log("Business information fetch complete.")
  }
  
  return businessInfo
}

// Save business information to the database
const saveBusinessInfo = async () => {
  try {
    isSaving.value = true
    saveSuccess.value = false
    saveError.value = false
    errorMessage.value = ''
    console.log("Attempting to save business information:", JSON.parse(JSON.stringify(businessInfo)))
    console.log("About section:", JSON.parse(JSON.stringify(businessInfo.about)))
    console.log("Social section:", JSON.parse(JSON.stringify(businessInfo.social)))
    
    // Basic validation before sending to service
    if (!businessInfo.about?.title?.trim()) {
      throw new Error("Business title in 'About' section is required.")
    }
    if (!businessInfo.contact?.address?.trim()) {
      throw new Error("Business address in 'Contact' section is required.")
    }
    if (!businessInfo.contact?.phone?.trim()) {
      throw new Error("Business phone number in 'Contact' section is required.")
    }
    if (!businessInfo.contact?.email?.trim()) {
      throw new Error("Business email in 'Contact' section is required.")
    }
    
    // Call the service function to update the database
    console.log("Calling updateBusinessInfo...")
    const success = await updateBusinessInfo(businessInfo)
    console.log("updateBusinessInfo result:", success)
    
    if (success) {
      console.log("Business information saved successfully.")
      saveSuccess.value = true
      setTimeout(() => { saveSuccess.value = false }, 3000) // Hide success message after 3s
    } else {
      throw new Error("Failed to update business information in the database (service returned false).")
    }
  } catch (error) {
    console.error("Error saving business information:", error)
    errorMessage.value = error.message || "An error occurred while saving business information."
    saveError.value = true
  } finally {
    isSaving.value = false
  }
}

// Add a new review to a specific category
const addReview = (category = 'home') => {
  // Ensure the category exists
  if (!['home', 'financing', 'detailing'].includes(category)) {
    console.warn(`Invalid review category: ${category}. Using 'home' instead.`)
    category = 'home'
  }
  
  const newId = businessInfo.reviews[category].length > 0
    ? Math.max(0, ...businessInfo.reviews[category].map(r => r.id || 0)) + 1
    : 1
  
  businessInfo.reviews[category].push({
    id: newId,
    author: '',
    vehicle: '',
    rating: 5, // Default rating
    text: '',
    category: category // Store the category with the review
  })
}

// Remove a review from a specific category
const removeReview = (id, category = 'home') => {
  // Ensure the category exists
  if (!['home', 'financing', 'detailing'].includes(category)) {
    console.warn(`Invalid review category: ${category}. Using 'home' instead.`)
    category = 'home'
  }
  
  const index = businessInfo.reviews[category].findIndex(r => r.id === id)
  if (index !== -1) {
    businessInfo.reviews[category].splice(index, 1)
  } else {
    console.warn(`Attempted to remove review with non-existent ID: ${id} from category ${category}`)
  }
}

// Add a new service
const addService = () => {
  const newId = businessInfo.services.length > 0
    ? Math.max(0, ...businessInfo.services.map(s => s.id || 0)) + 1
    : 1
  
  businessInfo.services.push({
    id: newId,
    title: '',
    description: '',
    icon: ''
  })
}

// Remove a service
const removeService = (id) => {
  const index = businessInfo.services.findIndex(s => s.id === id)
  if (index !== -1) {
    businessInfo.services.splice(index, 1)
  } else {
    console.warn(`Attempted to remove service with non-existent ID: ${id}`)
  }
}

export default {
  businessInfo,
  isLoading,
  isInitialized,
  isSaving,
  saveSuccess,
  saveError,
  errorMessage,
  getBusinessTitle,
  getBusinessDescription,
  getBusinessMission,
  getBusinessVision,
  getBusinessHistory,
  getBusinessSlogan,
  getBusinessLogo,
  getBusinessTimeline,
  getBusinessReviews: computed(() => businessInfo.reviews),
  getBusinessServices,
  getBusinessAddress,
  getBusinessPhone,
  getBusinessEmail,
  getBusinessHours,
  getBusinessSocial,
  getBusinessInfo,
  initStore,
  saveBusinessInfo,
  addReview,
  removeReview,
  addService,
  removeService
}