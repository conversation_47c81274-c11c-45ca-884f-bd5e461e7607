<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import supabaseStorage from '../../utils/supabaseStorage';

const props = defineProps({
  images: {
    type: Array,
    default: () => []
  },
  maxImages: {
    type: Number,
    default: 10
  },
  vehicleId: {
    type: [String, Number],
    default: 'temp'
  }
});

const emit = defineEmits(['update:images']);

// Local state for images
const localImages = ref([...props.images]);
const isUploading = ref(false);
const uploadProgress = ref(0);

// Initialize storage on component mount
onMounted(async () => {
  await supabaseStorage.initStorage();
});

// Watch for external changes to images prop
watch(() => props.images, (newImages) => {
  localImages.value = [...newImages];
}, { deep: true });

// Computed properties
const canAddMoreImages = computed(() => localImages.value.length < props.maxImages);
const mainImage = computed(() => {
  const firstImage = localImages.value[0];
  if (!firstImage) return '';
  return firstImage;
});
const galleryImages = computed(() => localImages.value.slice(1));

// File input ref
const fileInput = ref(null);

// Drag state
const isDragging = ref(false);
const draggedIndex = ref(null);
const dropTargetIndex = ref(null);

// Handle file selection
const handleFileSelect = (event) => {
  const files = event.target.files;
  if (!files.length) return;
  
  processFiles(files);
  
  // Reset file input
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// Process files
const processFiles = async (files) => {
  if (!canAddMoreImages.value) {
    alert(`Maximum ${props.maxImages} images allowed.`);
    return;
  }
  
  const remainingSlots = props.maxImages - localImages.value.length;
  const filesToProcess = Array.from(files).slice(0, remainingSlots);
  
  if (filesToProcess.length === 0) return;
  
  isUploading.value = true;
  uploadProgress.value = 0;
  
  try {
    console.log(`Processing ${filesToProcess.length} files for vehicle ID: ${props.vehicleId}`);
    
    // Process files one by one
    for (let i = 0; i < filesToProcess.length; i++) {
      const file = filesToProcess[i];
      
      try {
        // Validate file
        if (!file) {
          console.error('Invalid file: File is null or undefined');
          continue;
        }
        
        if (!file.type.match('image.*')) {
          console.log(`Skipping non-image file: ${file.name}`);
          continue;
        }
        
        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          console.error(`File too large: ${file.name} (${file.size} bytes)`);
          alert(`File too large: ${file.name}. Maximum file size is 10MB.`);
          continue;
        }
        
        console.log(`Processing file ${i+1}/${filesToProcess.length}: ${file.name} (${file.size} bytes, ${file.type})`);
        
        // Update progress
        uploadProgress.value = Math.round((i / filesToProcess.length) * 100);
        
        // First, read the file as data URL for preview
        const dataUrl = await readFileAsDataURL(file);
        console.log('File read as data URL successfully');
        
        // Add to local images immediately for preview
        const tempIndex = localImages.value.length;
        localImages.value.push(dataUrl);
        emitUpdate();
        console.log('Added data URL to local images for preview');
        
        // Then upload to Supabase Storage
        const isPrimary = localImages.value.length === 1; // First image is primary
        console.log(`Uploading to Supabase Storage, isPrimary: ${isPrimary}`);
        const publicUrl = await supabaseStorage.uploadVehicleImage(file, props.vehicleId, isPrimary);
        
        if (publicUrl) {
          console.log(`Upload successful, public URL: ${publicUrl}`);
          // Replace the data URL with the public URL
          localImages.value[tempIndex] = publicUrl;
          emitUpdate();
        } else {
          console.error('Failed to get public URL from storage');
          
          // Keep the data URL for preview but show a warning
          alert(`Failed to upload image: ${file.name}. The image will be shown in preview but won't be saved to the database.`);
          
          // Add a visual indicator that this image failed to upload
          // We'll keep the data URL but mark it as failed
          localImages.value[tempIndex] = {
            url: dataUrl,
            failed: true,
            originalName: file.name
          };
          emitUpdate();
        }
      } catch (fileError) {
        console.error(`Error processing file ${file?.name || 'unknown'}:`, fileError);
        console.error('Error details:', fileError.message);
        alert(`Error processing file ${file?.name || 'unknown'}: ${fileError.message}`);
      }
    }
    
    uploadProgress.value = 100;
  } catch (error) {
    console.error('Error uploading images:', error);
    console.error('Error details:', error.message);
    alert('Failed to upload one or more images. Please try again.');
  } finally {
    isUploading.value = false;
  }
};

// Helper function to read file as data URL
const readFileAsDataURL = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (e) => reject(e);
    reader.readAsDataURL(file);
  });
};

// Handle drag and drop
const handleDragOver = (event) => {
  event.preventDefault();
  isDragging.value = true;
};

const handleDragLeave = () => {
  isDragging.value = false;
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragging.value = false;
  
  const files = event.dataTransfer.files;
  if (files.length) {
    processFiles(files);
  }
};

// Handle image removal
const removeImage = async (index) => {
  const image = localImages.value[index];
  let imageUrl;
  
  // Handle both string URLs and objects with url property
  if (typeof image === 'object' && image !== null) {
    // If it's a failed upload, just remove it from the UI
    if (image.failed) {
      console.log(`Removing failed upload: ${image.originalName || 'unknown'}`);
      localImages.value.splice(index, 1);
      emitUpdate();
      return;
    }
    imageUrl = image.url;
  } else {
    imageUrl = image;
  }
  
  // Check if this is a Supabase Storage URL
  if (imageUrl && typeof imageUrl === 'string' && imageUrl.includes('supabase.co/storage')) {
    try {
      console.log(`Deleting image from storage: ${imageUrl}`);
      // Delete from Supabase Storage
      await supabaseStorage.deleteVehicleImage(imageUrl);
      console.log('Image deleted from storage successfully');
    } catch (error) {
      console.error('Error deleting image from storage:', error);
      console.error('Error details:', error.message);
      // Continue with removal from UI even if storage deletion fails
    }
  }
  
  // Remove from local state
  localImages.value.splice(index, 1);
  emitUpdate();
};

// Start dragging an image
const startDrag = (event, index) => {
  // Don't allow dragging failed uploads
  const image = localImages.value[index];
  if (typeof image === 'object' && image.failed) {
    event.preventDefault();
    event.stopPropagation();
    console.warn('Cannot drag failed upload');
    return;
  }
  
  draggedIndex.value = index;
  event.dataTransfer.effectAllowed = 'move';
  // Add a ghost image
  const img = new Image();
  img.src = typeof image === 'object' ? image.url : image;
  event.dataTransfer.setDragImage(img, 50, 50);
};

// Handle drag over another image
const onDragOver = (event, index) => {
  event.preventDefault();
  dropTargetIndex.value = index;
};

// Handle drop on another image (reordering)
const onDrop = (event, index) => {
  event.preventDefault();
  
  if (draggedIndex.value === null) return;
  
  // Check if target is a failed upload
  const targetImage = localImages.value[index];
  if (typeof targetImage === 'object' && targetImage.failed) {
    console.warn('Cannot drop onto a failed upload');
    // Reset drag state
    draggedIndex.value = null;
    dropTargetIndex.value = null;
    return;
  }
  
  // Reorder images
  const imageToMove = localImages.value[draggedIndex.value];
  localImages.value.splice(draggedIndex.value, 1);
  localImages.value.splice(index, 0, imageToMove);
  
  // Reset drag state
  draggedIndex.value = null;
  dropTargetIndex.value = null;
  
  emitUpdate();
};

// Emit update event
const emitUpdate = () => {
  // We need to pass the raw URLs or objects with url property
  emit('update:images', [...localImages.value]);
};

// Trigger file input click
const triggerFileInput = () => {
  fileInput.value.click();
};

// Set as main image
const setAsMain = async (index, event) => {
  // Prevent default behavior and stop event propagation
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }
  
  if (index === 0) return; // Already main image
  
  const image = localImages.value[index];
  
  // Don't set failed uploads as main
  if (typeof image === 'object' && image.failed) {
    console.warn('Cannot set failed upload as main image');
    alert('Cannot set failed upload as main image');
    return;
  }
  
  localImages.value.splice(index, 1);
  localImages.value.unshift(image);
  
  // Note: In a production app, we would update the is_primary flag in Supabase Storage
  // This would typically be handled on the server side when saving the vehicle
  // For now, we just update the UI and the order of images in the array
  
  emitUpdate();
};
</script>

<template>
  <div class="image-uploader">
    <!-- Main Image Section -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-700 mb-2">Main Image</h3>
          
          <div
            v-if="mainImage"
            class="relative bg-gray-100 rounded-lg overflow-hidden border-2 border-primary max-w-md mx-auto"
            style="aspect-ratio: 16/9;"
            :class="{ 'border-red-500': typeof mainImage === 'object' && mainImage.failed }"
          >
            <img
              :src="typeof mainImage === 'object' ? mainImage.url : mainImage"
              alt="Main vehicle image"
              class="w-full h-full object-cover"
            />
            <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 hover:opacity-100">
              <button
                @click="removeImage(0)"
                class="p-2 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                title="Remove image"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
            <div class="absolute top-2 left-2 bg-primary text-white px-3 py-1.5 text-sm font-medium rounded-md shadow-sm">
              Main Image
            </div>
            <div v-if="typeof mainImage === 'object' && mainImage.failed" class="absolute bottom-2 left-2 right-2 bg-red-600 text-white px-3 py-1.5 text-sm font-medium rounded-md shadow-sm text-center">
              Upload Failed - Will Not Be Saved
            </div>
          </div>
      
      <div 
        v-else 
        class="aspect-video bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
        @click="triggerFileInput"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
        @drop="handleDrop"
        :class="{ 'border-primary bg-primary bg-opacity-5': isDragging }"
      >
        <div class="text-center p-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p class="mt-2 text-sm text-gray-600">Drag and drop your main image here or click to browse</p>
          <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
        </div>
      </div>
    </div>
    
    <!-- Gallery Images Section -->
    <div>
      <h3 class="text-lg font-medium text-gray-700 mb-2">Gallery Images</h3>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <!-- Existing Gallery Images -->
        <div
          v-for="(image, index) in galleryImages"
          :key="index"
          class="relative aspect-square bg-gray-100 rounded-lg overflow-hidden border border-gray-200"
          draggable="true"
          @dragstart="startDrag($event, index + 1)"
          @dragover="onDragOver($event, index + 1)"
          @drop="onDrop($event, index + 1)"
          :class="{
            'border-primary border-2': dropTargetIndex === index + 1,
            'border-red-500 border-2': typeof image === 'object' && image.failed
          }"
        >
          <img
            :src="typeof image === 'object' ? image.url : image"
            :alt="`Vehicle image ${index + 2}`"
            class="w-full h-full object-cover"
          />
          <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 transition-all flex flex-col items-center justify-center opacity-0 hover:opacity-100">
            <div class="flex flex-col space-y-2 w-full px-2">
              <button
                @click="setAsMain(index + 1, $event)"
                class="w-full px-2 py-1 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors flex items-center justify-center"
                title="Set as main image"
                :disabled="typeof image === 'object' && image.failed"
                :class="{ 'opacity-50 cursor-not-allowed': typeof image === 'object' && image.failed }"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5h16a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1h-16a1 1 0 0 1-1-1v-5a1 1 0 0 1 1-1z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 12v8" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16l4-4 4 4" />
                </svg>
                <span class="text-xs whitespace-nowrap">Set as main</span>
              </button>
              <button
                @click="removeImage(index + 1)"
                class="w-full px-2 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center justify-center"
                title="Remove image"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                <span class="text-xs whitespace-nowrap">Remove</span>
              </button>
            </div>
          </div>
          <div class="absolute top-2 left-2 bg-gray-800 bg-opacity-70 text-white px-2 py-1 text-xs rounded">
            {{ index + 2 }}
          </div>
          <div v-if="typeof image === 'object' && image.failed" class="absolute bottom-2 left-2 right-2 bg-red-600 text-white px-3 py-1.5 text-xs font-medium rounded-md shadow-sm text-center">
            Upload Failed - Will Not Be Saved
          </div>
        </div>
        
        <!-- Add Image Placeholder -->
        <div 
          v-if="canAddMoreImages"
          class="aspect-square bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
          @click="triggerFileInput"
          @dragover="handleDragOver"
          @dragleave="handleDragLeave"
          @drop="handleDrop"
          :class="{ 'border-primary bg-primary bg-opacity-5': isDragging }"
        >
          <div class="text-center p-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            <p class="mt-1 text-xs text-gray-500">Add Image</p>
          </div>
        </div>
      </div>
      
      <!-- Upload Progress -->
      <div v-if="isUploading" class="mt-4 bg-gray-100 rounded-lg p-3">
        <div class="flex items-center justify-between mb-1">
          <span class="text-sm font-medium text-gray-700">Uploading images...</span>
          <span class="text-sm text-gray-500">{{ uploadProgress }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2.5">
          <div class="bg-primary h-2.5 rounded-full" :style="{ width: `${uploadProgress}%` }"></div>
        </div>
      </div>
      
      <p class="mt-2 text-sm text-gray-500">
        {{ localImages.length }} of {{ maxImages }} images
        <span v-if="localImages.length > 1"> • Drag to reorder</span>
      </p>
      
      <!-- Hidden file input -->
      <input 
        ref="fileInput"
        type="file"
        accept="image/*"
        multiple
        class="hidden"
        @change="handleFileSelect"
      />
    </div>
  </div>
</template>

<style scoped>
.image-uploader [draggable] {
  cursor: move;
}
</style>