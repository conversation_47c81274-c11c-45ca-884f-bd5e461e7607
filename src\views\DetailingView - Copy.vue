<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { getDetailingPrices } from '../database/settingsService'; // Adjust path if necessary

// --- State for fetched prices ---
const fetchedDetailingPrices = ref(null); // Store raw fetched data
const isLoadingPrices = ref(true); // Loading state for fetching prices
const loadingErrorPrices = ref(null); // Error message if fetching fails

// --- Default Structure (Defines categories, packages, and services) ---
// Prices are initially null and will be filled by the computed property
const defaultVehicleCategoriesStructure = ref([
  {
    name: 'Sedans',
    packages: [
      { name: 'Express Detailing', price: null, services: ['Wipe down', 'Vacuum', 'Exterior Wash'] },
      { name: 'Tier 1', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash'] },
      { name: 'Tier 2', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash', 'Odor Removal'] },
      { name: 'Tier 3', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash', 'Pet Hair Removal'] },
      { name: 'Tier 4', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash', 'Exterior 2 STAGE Polish'] }
    ]
  },
  {
    name: 'SUVs/Small Vans',
    packages: [
      { name: 'Express Detailing', price: null, services: ['Wipe down', 'Vacuum', 'Exterior Wash'] },
      { name: 'Tier 1', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash'] },
      { name: 'Tier 2', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash', 'Odor Removal'] },
      { name: 'Tier 3', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash', 'Pet Hair Removal'] },
      { name: 'Tier 4', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash', 'Exterior 2 STAGE Polish'] }
    ]
  },
  {
    name: 'Trucks/Large Vans',
     packages: [
      { name: 'Express Detailing', price: null, services: ['Wipe down', 'Vacuum', 'Exterior Wash'] },
      { name: 'Tier 1', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash'] },
      { name: 'Tier 2', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash', 'Odor Removal'] },
      { name: 'Tier 3', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash', 'Pet Hair Removal'] },
      { name: 'Tier 4', price: null, services: ['Wipe down', 'Shampoo', 'Vacuum', 'Exterior Wash', 'Exterior 2 STAGE Polish'] }
    ]
  },
  {
    name: 'Limo, Semis, Trailers',
    specialPricing: true,
    basePrice: null, // Use null initially, will be formatted later
    callForPricing: true // You might want to make this dynamic too if needed
  }
]);

// --- Computed property to merge fetched prices with default structure ---
// This ensures the template always has the correct structure to loop through,
// even if prices haven't loaded yet or if there's an error.
const vehicleCategories = computed(() => {
  // If prices haven't loaded or there was an error, return the default structure
  // with null prices. The template will handle displaying 'N/A' or 'Contact Us'.
  if (isLoadingPrices.value || loadingErrorPrices.value || !fetchedDetailingPrices.value) {
    // Return a deep clone even in the loading/error state to ensure reactivity works correctly
    // if the structure itself needs to be modified temporarily (though unlikely here)
    return JSON.parse(JSON.stringify(defaultVehicleCategoriesStructure.value));
  }

  // Deep clone the default structure to avoid modifying the original ref
  const categoriesWithPrices = JSON.parse(JSON.stringify(defaultVehicleCategoriesStructure.value));

  categoriesWithPrices.forEach(category => {
    const fetchedCategoryPrices = fetchedDetailingPrices.value[category.name];

    if (fetchedCategoryPrices && typeof fetchedCategoryPrices === 'object') {
      if (category.specialPricing) {
        // Handle special pricing category (e.g., Limo, Semis)
        const basePriceValue = fetchedCategoryPrices['Base Price'];
        if (basePriceValue !== undefined && basePriceValue !== null && !isNaN(parseFloat(basePriceValue))) {
          // Format the base price with '+'
          category.basePrice = `${parseFloat(basePriceValue).toFixed(2)}+`;
        } else {
           category.basePrice = 'Contact Us'; // Fallback if price is missing/invalid
        }
      } else if (category.packages) {
        // Handle regular categories with multiple packages
        category.packages.forEach(pkg => {
          const packagePriceValue = fetchedCategoryPrices[pkg.name];
          if (packagePriceValue !== undefined && packagePriceValue !== null && !isNaN(parseFloat(packagePriceValue))) {
            // Ensure price is formatted as a string with two decimal places
            pkg.price = parseFloat(packagePriceValue).toFixed(2);
          } else {
             pkg.price = 'Contact Us'; // Fallback if price is missing/invalid
          }
        });
      }
    } else {
        // Handle case where a whole category might be missing prices in the fetched data
        console.warn(`No valid price data found for category: ${category.name}`);
        if (category.specialPricing) {
            category.basePrice = 'Contact Us';
        } else if (category.packages) {
            category.packages.forEach(pkg => pkg.price = 'Contact Us');
        }
    }
  });

  return categoriesWithPrices;
});


// --- Fetch detailing prices from database ---
const fetchDetailingPrices = async () => {
  isLoadingPrices.value = true;
  loadingErrorPrices.value = null;
  fetchedDetailingPrices.value = null; // Reset before fetching
  console.log('Fetching detailing prices...');
  try {
    const prices = await getDetailingPrices();
    console.log('Raw prices fetched:', prices);
    // Basic validation: Check if prices is an object and has keys
    if (prices && typeof prices === 'object' && Object.keys(prices).length > 0) {
      fetchedDetailingPrices.value = prices; // Store raw fetched data
      console.log('Detailing prices loaded successfully:', fetchedDetailingPrices.value);
    } else {
        // Use the default structure but mark prices as 'Contact Us' if fetch returns empty/invalid
      loadingErrorPrices.value = 'Detailing prices are not configured yet. Please check back later.';
      console.warn('No detailing prices found in database or database returned invalid data. Using default structure with "Contact Us".');
      // No need to set fetchedDetailingPrices.value here, the computed prop will handle it
    }
  } catch (error) {
    console.error('Error fetching detailing prices:', error);
    loadingErrorPrices.value = 'Failed to load detailing prices. Please try again.';
  } finally {
    isLoadingPrices.value = false;
    console.log('Finished fetching prices. Loading:', isLoadingPrices.value, 'Error:', loadingErrorPrices.value);
  }
};


// --- Original Setup Code (Animations, Static Data, etc.) ---
const pageTitle = ref(null);
const pricingSection = ref(null);
const featuresSection = ref(null);
const serviceCards = ref([]); // Note: Ensure this ref is used correctly if needed for animations

// Process steps data (Static)
const processSteps = ref([
  { title: 'Initial Assessment', description: 'We thoroughly inspect your vehicle to identify areas that need special attention.', image: '/ASSESSMENT.jpg', expandedDetails: [ 'Comprehensive 27-point inspection of your vehicle', 'Documentation of existing conditions and problem areas', 'Personalized consultation to discuss findings', 'Custom detailing package recommendations', 'Transparent pricing with no hidden fees' ] },
  { title: 'Exterior Cleaning', description: 'Using premium products, we wash, clay, and polish the exterior to remove contaminants and restore shine.', image: '/EXTERIORDETAIL.jpg', expandedDetails: [ 'Foam pre-soak to loosen dirt and grime', 'Two-bucket hand wash with pH-balanced soap', 'Clay bar treatment to remove embedded contaminants', 'Machine polishing to remove swirl marks and scratches', 'Premium wax or ceramic coating application', 'Wheel, tire, and trim detailing' ] },
  { title: 'Interior Detailing', description: 'We meticulously clean all interior surfaces, including hard-to-reach areas, to remove dirt and stains.', image: '/INTERIORDETAIL.jpg', expandedDetails: [ 'Deep vacuum of all surfaces including seats and carpets', 'Material-specific cleaning for leather, vinyl, and fabric', 'Stubborn stain treatment with specialized products', 'Steam cleaning for carpets and upholstery', 'Streak-free glass cleaning inside and out', 'UV protectant application for dashboard and trim' ] },
  { title: 'Final Touches', description: 'We apply protective coatings and perform a final inspection to ensure everything meets our high standards.', image: '/FINALTOUCH.jpg', expandedDetails: [ 'Tire dressing and trim restoration', 'Final wipe-down with specialized microfiber towels', 'Quality control inspection using detailed checklist', 'Personalized walkthrough of completed services', 'Maintenance tips to preserve the detailed finish', 'Follow-up scheduling for ongoing care' ] }
]);
const hoveredStep = ref(null);

// Features of our detailing service (Static)
const features = ref([
  { title: 'Premium Products', description: 'We use only the highest quality detailing products to ensure the best results for your vehicle.', icon: 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z' },
  { title: 'Experienced Staff', description: 'Our detailing technicians are highly trained professionals with years of experience.', icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z' },
  { title: 'Attention to Detail', description: 'We meticulously clean every nook and cranny of your vehicle for a truly comprehensive detailing.', icon: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z' },
  { title: 'Convenient Service', description: 'Drop off your vehicle or wait in our comfortable lounge while we transform your car.', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' }
]);

// Testimonials (Static)
const testimonials = ref([
  { name: 'Sarah Johnson', vehicle: 'Luxury SUV', quote: 'The detailing service at GT Motorsports is exceptional. My SUV looks better than when I first bought it!', rating: 5 },
  { name: 'Michael Chen', vehicle: 'Luxury Sedan', quote: 'I was amazed at how they were able to remove stains I thought were permanent. Highly recommended!', rating: 5 },
  { name: 'Jessica Williams', vehicle: 'Electric Sedan', quote: 'The attention to detail is impressive. They took care of every spot and the car looks immaculate.', rating: 5 },
  { name: 'Robert Garcia', vehicle: 'Pickup Truck', quote: 'These guys know trucks! They got my truck looking showroom new despite all the mud and dirt I put it through.', rating: 5 },
  { name: 'Emily Thompson', vehicle: 'Luxury Crossover', quote: 'I\'ve tried several detailing services in the area, and GT Motorsports is by far the best. Worth every penny!', rating: 5 },
  { name: 'David Wilson', vehicle: 'Luxury Sports Car', quote: 'As a car enthusiast, I\'m very particular about who touches my vehicle. These guys are true professionals.', rating: 5 },
  { name: 'Amanda Lee', vehicle: 'Compact Sedan', quote: 'Affordable and high-quality service. They treated my car with the same care as the luxury vehicles.', rating: 5 },
  { name: 'James Rodriguez', vehicle: 'Premium SUV', quote: 'Excellent service from start to finish. My SUV hasn\'t looked this good since I drove it off the lot.', rating: 5 }
]);
const testimonialContainer = ref(null);
const isTestimonialAnimationPaused = ref(false);
const pauseTestimonialAnimation = () => { isTestimonialAnimationPaused.value = true; };
const resumeTestimonialAnimation = () => { isTestimonialAnimationPaused.value = false; };

// Active category for mobile/desktop view
const activeCategory = ref(0);
const setActiveCategory = (index) => {
  // Add validation to prevent index out of bounds if needed
  if (index >= 0 && index < vehicleCategories.value.length) {
      activeCategory.value = index;
  }
};

// Lifecycle Hooks
onMounted(async () => {
  // Fetch detailing prices FIRST
  await fetchDetailingPrices();

  // Initialize animations etc. AFTER data might be needed for layout calculations
  if (pageTitle.value) { setTimeout(() => { pageTitle.value.style.opacity = '1'; pageTitle.value.classList.add('animate-fade-in-down'); }, 100); }

  // Delay pricing section fade-in slightly more to allow computed prop to potentially settle
  if (pricingSection.value) { setTimeout(() => { pricingSection.value.style.opacity = '1'; pricingSection.value.classList.add('animate-fade-in-up'); }, 400); } // Increased delay slightly

  // Set up Intersection Observer for features section
  if (featuresSection.value) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Ensure animations run only once
          if (!entry.target.dataset.animated) {
              entry.target.dataset.animated = 'true'; // Mark as animated

              const videoContainer = featuresSection.value.querySelector('.video-container');
              if (videoContainer) { videoContainer.style.opacity = '1'; videoContainer.classList.add('animate-fade-in-left'); }
              const video = document.getElementById('detailingVideo');
              if (video) { video.play().catch(e => console.log('Auto-play was prevented:', e)); }

              const featuresContent = featuresSection.value.querySelector('.features-content');
              if (featuresContent) { featuresContent.style.opacity = '1'; featuresContent.classList.add('animate-fade-in-right'); }

              // Animate the feature cards within the features section
              const featureCards = featuresSection.value.querySelectorAll('.service-card'); // Use the correct selector
              featureCards.forEach((card, index) => {
                // Apply animation classes directly, relying on CSS animation delays if needed,
                // or keep setTimeout if preferred for staggering. Staggering looks better.
                setTimeout(() => {
                  card.style.opacity = '1';
                  card.classList.add('animate-fade-in-up');
                }, 200 + (index * 150)); // Added a small base delay
              });

             observer.unobserve(entry.target); // Stop observing after animation triggered
          }
        }
      });
    }, { threshold: 0.1 }); // Lower threshold slightly
    observer.observe(featuresSection.value);
  }
});

// onUnmounted(() => { /* Add cleanup if needed */ });

</script>

<template>
  <!-- Hero Section -->
  <section class="relative bg-primary overflow-hidden">
    <div class="absolute inset-0 opacity-10"> <div class="absolute inset-0" style="background-image: url('https://images.unsplash.com/photo-1610647752706-3bb12232b3ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'); background-size: cover; background-position: center;"></div> </div>
    <div class="container-custom relative z-10 py-16 md:py-20 lg:py-32">
      <div class="text-center text-white">
        <h1 ref="pageTitle" class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 md:mb-6 leading-tight px-2" style="opacity: 0;"> Professional <span class="text-secondary">Car Detailing</span> Services </h1>
        <p class="text-base sm:text-lg md:text-xl opacity-90 max-w-3xl mx-auto mb-8 md:mb-10 px-4"> Restore your vehicle's showroom shine with our premium detailing packages tailored to your specific needs. </p>
        <div class="flex flex-wrap justify-center gap-3 md:gap-4 px-4">
          <a href="#pricing" class="btn btn-secondary text-sm md:text-base py-2 px-4 md:py-3 md:px-6"> View Pricing </a>
          <router-link to="/contact" class="btn btn-outline border-white text-white hover:bg-white hover:text-primary text-sm md:text-base py-2 px-4 md:py-3 md:px-6"> Book Now </router-link>
        </div>
      </div>
    </div>
    <div class="absolute bottom-0 left-0 right-0 h-16 sm:h-24 md:h-32 lg:h-40 overflow-hidden"> <svg class="absolute bottom-0 w-full h-full" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none" viewBox="0 0 1440 320"> <path fill="#F8FAFC" fill-opacity="1" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,250.7C960,235,1056,181,1152,165.3C1248,149,1344,171,1392,181.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path> </svg> </div>
  </section>

  <!-- Features Section -->
  <section class="section bg-light" ref="featuresSection">
    <div class="container-custom">
      <div class="text-center lg:hidden mb-8">
        <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 md:mb-4">Why Choose Our Detailing Service</h2>
        <p class="text-base sm:text-lg text-gray-600 px-4"> We provide exceptional car detailing services with attention to every detail, ensuring your vehicle looks its absolute best. </p>
      </div>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center">
        <!-- Video Showcase -->
        <div class="relative rounded-xl overflow-hidden shadow-custom video-container opacity-0 mx-auto w-full max-w-lg">
          <video id="detailingVideo" class="w-full" poster="/GTWHEEL.png" preload="metadata" muted loop playsinline> <source src="/DETAIL.mp4" type="video/mp4"> Your browser does not support the video tag. </video>
        </div>
        <!-- Features Content -->
        <div class="features-content opacity-0">
          <div class="mb-6 md:mb-10 hidden lg:block"> <h2 class="text-3xl md:text-4xl font-bold mb-4">Why Choose Our Detailing Service</h2> <p class="text-lg text-gray-600"> We provide exceptional car detailing services with attention to every detail, ensuring your vehicle looks its absolute best. </p> </div>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
            <!-- Feature Cards -->
            <div
              v-for="(feature, index) in features"
              :key="index"
              class="bg-white rounded-lg p-4 md:p-6 shadow-custom transition-all duration-300 hover:shadow-lg hover:-translate-y-1 active:shadow-lg active:-translate-y-1 group service-card opacity-0"
            >
              <div class="bg-primary/10 p-2 md:p-3 rounded-full w-12 h-12 md:w-14 md:h-14 flex items-center justify-center mb-3 md:mb-4 group-hover:bg-secondary/20 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 md:h-8 md:w-8 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="feature.icon" /> </svg>
              </div>
              <h3 class="text-lg md:text-xl font-bold mb-2 md:mb-3">{{ feature.title }}</h3>
              <p class="text-sm md:text-base text-gray-600">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Pricing Section -->
  <section id="pricing" ref="pricingSection" class="section bg-white relative" style="opacity: 0;">
    <!-- Add min-height for loading state to reduce layout shift -->
    <div class="absolute inset-0 flex items-center justify-center" v-if="isLoadingPrices">
        <div class="text-center py-10 text-gray-600">
          <svg class="animate-spin h-8 w-8 text-primary mx-auto mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"> <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle> <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path> </svg>
          Loading Pricing...
        </div>
    </div>
    <!-- Error State -->
    <div class="absolute inset-0 flex items-center justify-center px-4" v-else-if="loadingErrorPrices">
      <div class="text-center py-6 text-red-700 bg-red-50 p-4 rounded-md max-w-xl mx-auto border border-red-200">
          {{ loadingErrorPrices }}
      </div>
    </div>

    <!-- Pricing Content (only rendered when not loading and no error) -->
    <div class="container-custom transition-opacity duration-300" :class="{ 'opacity-0': isLoadingPrices || loadingErrorPrices, 'opacity-100': !isLoadingPrices && !loadingErrorPrices }">
      <div class="text-center mb-10 md:mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 md:mb-6">Our Detailing Packages</h2>
        <p class="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto px-4">
          Choose from our range of detailing packages designed to meet your specific needs and budget.
        </p>
      </div>

      <!-- Content that replaces spinner/error -->
       <div v-if="!isLoadingPrices && !loadingErrorPrices">
          <!-- Mobile Category Selector -->
          <div class="lg:hidden mb-6 md:mb-8 px-4">
              <div class="flex justify-start md:justify-center overflow-x-auto pb-2 gap-2 md:gap-3 scrollbar-hide">
                <button
                  v-for="(category, index) in vehicleCategories"
                  :key="`mobile-cat-${index}`"
                  @click="setActiveCategory(index)"
                  class="px-4 py-2 md:px-5 md:py-3 rounded-full whitespace-nowrap transition-all duration-300 shadow-sm font-medium text-sm md:text-base flex-shrink-0"
                  :class="activeCategory === index ? 'bg-secondary text-white shadow-md scale-105' : 'bg-gray-200 text-gray-700 hover:bg-gray-300 active:bg-gray-300 hover:scale-105 active:scale-105'"
                >
                  {{ category.name }}
                </button>
              </div>
          </div>

          <!-- Desktop Tabs -->
          <div class="hidden lg:block">
              <div class="flex justify-center border-b border-gray-200 mb-8">
                <button
                  v-for="(category, index) in vehicleCategories"
                  :key="`desktop-cat-${index}`"
                  @click="setActiveCategory(index)"
                  class="px-8 py-4 font-medium text-lg transition-all duration-300 border-b-3 -mb-px mx-2"
                  :class="activeCategory === index ? 'border-secondary text-secondary font-bold' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                >
                  {{ category.name }}
                </button>
              </div>
          </div>

          <!-- Current Category Indicator -->
          <div class="mb-3 text-center">
              <span class="text-xs md:text-sm text-gray-500">Currently viewing:</span>
              <span class="ml-1 text-xs md:text-sm font-medium text-secondary">{{ vehicleCategories[activeCategory]?.name || '...' }}</span>
          </div>

          <!-- Pricing Content Loop - Use v-show for better tab switching performance after initial load -->
          <div v-for="(category, categoryIndex) in vehicleCategories" :key="categoryIndex" v-show="activeCategory === categoryIndex">
            <!-- Special pricing -->
            <div v-if="category.specialPricing" class="bg-gradient-to-r from-primary to-primary/90 rounded-xl p-6 md:p-8 text-white text-center mx-4 sm:mx-0">
              <h3 class="text-xl sm:text-2xl md:text-3xl font-bold mb-3 md:mb-4">{{ category.name }}</h3>
               <!-- Display basePrice safely -->
              <p class="text-lg sm:text-xl md:text-2xl font-bold mb-2">
                ${{ category.basePrice || 'Contact Us' }}
              </p>
              <p class="text-base sm:text-lg opacity-90 mb-4 md:mb-6">{{ category.callForPricing ? 'Call for Pricing!' : '' }}</p>
              <router-link to="/contact" class="btn btn-secondary text-sm md:text-base py-2 px-4 md:py-3 md:px-6">
                Contact Us for Quote
              </router-link>
            </div>

            <!-- Regular pricing packages -->
            <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 md:gap-6 px-4 sm:px-0">
              <div
                v-for="(pkg, pkgIndex) in category.packages"
                :key="pkgIndex"
                class="bg-white rounded-lg overflow-hidden shadow-custom transition-all duration-300 hover:shadow-lg hover:-translate-y-1 active:shadow-lg active:-translate-y-1 border border-gray-100 flex flex-col"
              >
                <div class="bg-primary/5 p-4 md:p-6 text-center border-b border-gray-100">
                  <h3 class="text-lg md:text-xl font-bold mb-1">{{ pkg.name }}</h3>
                  <!-- Safely display price or fallback -->
                  <div v-if="pkg.price && pkg.price !== 'Contact Us'" class="flex items-baseline justify-center">
                    <span class="text-xs md:text-sm mr-1">$</span>
                    <span class="text-2xl md:text-3xl font-bold text-secondary">{{ pkg.price }}</span>
                    <span class="text-xs md:text-sm ml-1 text-gray-500">CAD</span>
                  </div>
                   <div v-else class="text-lg md:text-xl font-medium text-gray-500 mt-2 h-[36px] flex items-center justify-center"> <!-- Match height approx -->
                     {{ pkg.price || 'Contact Us' }}
                   </div>
                </div>

                <div class="p-4 md:p-6 flex-grow">
                  <ul class="space-y-2 md:space-y-3">
                    <li v-for="(service, serviceIndex) in pkg.services" :key="serviceIndex" class="flex items-start">
                      <svg class="h-4 w-4 md:h-5 md:w-5 text-secondary mt-0.5 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /> </svg>
                      <span class="text-sm md:text-base">{{ service }}</span>
                    </li>
                  </ul>
                </div>

                <div class="p-4 md:p-6 pt-0 mt-auto"> <!-- Ensure button is at the bottom -->
                  <router-link to="/contact" class="w-full btn btn-outline text-center text-sm md:text-base py-2 md:py-3">
                    Book Now
                  </router-link>
                </div>
              </div>
            </div>
          </div>
       </div>
    </div> <!-- End container-custom -->
  </section>

  <!-- Process Section -->
  <section class="section bg-light">
    <div class="container-custom">
      <div class="text-center mb-12 md:mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 md:mb-6">Our Detailing Process</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto"> We follow a meticulous process to ensure your vehicle receives the best possible care. <span class="block mt-2 text-sm text-secondary font-medium"> <span class="hidden md:inline">Hover over</span> <span class="md:hidden">Tap on</span> each step to learn more </span> </p>
      </div>
      <div class="relative">
        <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-secondary/20 hidden md:block"></div>
        <div class="space-y-0 md:space-y-12 relative">
          <div v-for="(step, index) in processSteps" :key="index" class="flex flex-col md:flex-row items-center mb-12 md:mb-0" :class="index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'">
            <div class="absolute left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full bg-secondary flex items-center justify-center text-white font-bold z-20 hidden md:flex"> {{ index + 1 }} </div>
            <div class="bg-white rounded-lg shadow-custom p-5 md:p-6 w-full md:w-5/12 relative process-step-card" :class="[ index % 2 === 0 ? 'md:mr-auto' : 'md:ml-auto', { 'expanded': hoveredStep === index } ]" @mouseenter="hoveredStep = index" @mouseleave="hoveredStep = null" @click="hoveredStep = hoveredStep === index ? null : index">
              <div class="w-8 h-8 md:w-10 md:h-10 rounded-full bg-secondary flex items-center justify-center text-white font-bold absolute -top-4 md:-top-5 left-4 md:left-6 md:hidden"> {{ index + 1 }} </div>
              <div class="flex flex-col md:flex-row items-start gap-5 md:gap-6">
                <div class="w-full md:w-1/3 mb-4 md:mb-0"> <div class="process-image-container" :class="{ 'expanded': hoveredStep === index }"> <img :src="step.image" :alt="step.title" class="rounded-lg w-full h-auto object-cover shadow-md" /> </div> </div>
                <div class="w-full md:w-2/3">
                  <h3 class="text-lg md:text-xl font-bold mb-3 mt-0">{{ step.title }}</h3>
                  <p class="text-sm md:text-base text-gray-600">{{ step.description }}</p>
                  <div class="expanded-details mt-4 md:mt-4 overflow-hidden" :class="{ 'show': hoveredStep === index }">
                    <div class="h-px w-full bg-gray-200 mb-4 md:mb-4"></div>
                    <ul class="space-y-3 text-gray-700 text-sm md:text-base">
                      <li v-for="(detail, detailIndex) in step.expandedDetails" :key="detailIndex" class="flex items-start">
                        <svg class="h-4 w-4 md:h-5 md:w-5 text-secondary mt-0.5 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /> </svg>
                        <span>{{ detail }}</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section class="section bg-white overflow-hidden">
    <div class="container-custom">
      <div class="text-center mb-10 md:mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 md:mb-6">What Our Customers Say</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto"> Don't just take our word for it. Here's what our satisfied customers have to say about our detailing services. </p>
      </div>
      <div class="relative">
        <div class="relative overflow-hidden testimonial-container">
          <div class="absolute left-0 top-0 bottom-0 w-12 md:w-24 z-10 bg-gradient-to-r from-white to-transparent pointer-events-none"></div>
          <div class="absolute right-0 top-0 bottom-0 w-12 md:w-24 z-10 bg-gradient-to-l from-white to-transparent pointer-events-none"></div>
          <div ref="testimonialContainer" class="testimonial-scroll" :class="{ 'pause-animation': isTestimonialAnimationPaused }" @mouseenter="pauseTestimonialAnimation" @mouseleave="resumeTestimonialAnimation" @touchstart="pauseTestimonialAnimation" @touchend="resumeTestimonialAnimation">
            <div v-for="(testimonial, index) in testimonials" :key="index" class="bg-light rounded-lg p-4 md:p-6 shadow-custom relative flex-shrink-0 w-[85vw] sm:w-[320px] md:w-80 mx-2 md:mx-3 transition-all duration-300 hover:shadow-lg active:shadow-lg">
              <div class="absolute top-4 right-4 md:top-6 md:right-6 text-secondary/20"> <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 md:h-12 md:w-12" fill="currentColor" viewBox="0 0 24 24"> <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" /> </svg> </div>
              <p class="text-sm md:text-base text-gray-600 mb-4 md:mb-6 relative z-10">{{ testimonial.quote }}</p>
              <div class="flex items-center">
                <div class="mr-3 md:mr-4"> <div class="w-10 h-10 md:w-12 md:h-12 bg-secondary/20 rounded-full flex items-center justify-center text-secondary font-bold"> {{ testimonial.name.charAt(0) }} </div> </div>
                <div> <h4 class="font-bold text-sm md:text-base">{{ testimonial.name }}</h4> <p class="text-xs md:text-sm text-gray-500">{{ testimonial.vehicle }}</p> </div>
              </div>
              <div class="mt-3 md:mt-4 flex"> <svg v-for="star in testimonial.rating" :key="star" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor"> <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" /> </svg> </div>
            </div>
            <!-- Clone the first few testimonials -->
            <div v-for="i in 3" :key="`clone-${i}`" class="bg-light rounded-lg p-4 md:p-6 shadow-custom relative flex-shrink-0 w-[85vw] sm:w-[320px] md:w-80 mx-2 md:mx-3 transition-all duration-300 hover:shadow-lg active:shadow-lg" aria-hidden="true">
              <div class="absolute top-4 right-4 md:top-6 md:right-6 text-secondary/20"> <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 md:h-12 md:w-12" fill="currentColor" viewBox="0 0 24 24"> <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" /> </svg> </div>
              <p class="text-sm md:text-base text-gray-600 mb-4 md:mb-6 relative z-10">{{ testimonials[i - 1].quote }}</p>
              <div class="flex items-center">
                <div class="mr-3 md:mr-4"> <div class="w-10 h-10 md:w-12 md:h-12 bg-secondary/20 rounded-full flex items-center justify-center text-secondary font-bold"> {{ testimonials[i - 1].name.charAt(0) }} </div> </div>
                <div> <h4 class="font-bold text-sm md:text-base">{{ testimonials[i - 1].name }}</h4> <p class="text-xs md:text-sm text-gray-500">{{ testimonials[i - 1].vehicle }}</p> </div>
              </div>
              <div class="mt-3 md:mt-4 flex"> <svg v-for="star in testimonials[i - 1].rating" :key="star" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor"> <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" /> </svg> </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="section bg-light">
    <div class="container-custom px-4 sm:px-6">
      <div class="bg-gradient-to-r from-primary to-primary/90 rounded-xl p-6 sm:p-8 md:p-12 text-white text-center">
        <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 md:mb-4">Ready to Restore Your Vehicle's Shine?</h2>
        <p class="text-base sm:text-lg opacity-90 max-w-2xl mx-auto mb-6 md:mb-8"> Book your detailing appointment today and experience the GT Motorsports difference. </p>
        <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
          <router-link to="/contact" class="btn btn-secondary text-sm md:text-base py-3 px-6"> Book Now </router-link>
          <a href="tel:+14034022015" class="btn btn-outline border-white text-white hover:bg-white hover:text-primary active:bg-white active:text-primary flex items-center justify-center text-sm md:text-base py-3 px-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" /> </svg>
            Call (*************
          </a>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
/* Keep existing styles */
@keyframes float { 0% { transform: translateY(0px); } 50% { transform: translateY(-20px); } 100% { transform: translateY(0px); } }
@keyframes pulse { 0% { opacity: 0.6; } 50% { opacity: 0.8; } 100% { opacity: 0.6; } }
@keyframes fadeInDown { from { opacity: 0; transform: translateY(-25px); } to { opacity: 1; transform: translateY(0); } } /* Smoother animation */
@keyframes fadeInUp { from { opacity: 0; transform: translateY(25px); } to { opacity: 1; transform: translateY(0); } } /* Smoother animation */
@keyframes fadeInLeft { from { opacity: 0; transform: translateX(-30px); } to { opacity: 1; transform: translateX(0); } } /* Smoother animation */
@keyframes fadeInRight { from { opacity: 0; transform: translateX(30px); } to { opacity: 1; transform: translateX(0); } } /* Smoother animation */
.animate-fade-in-down { animation: fadeInDown 0.8s ease-out forwards; } /* Adjusted timing */
.animate-fade-in-up { animation: fadeInUp 0.8s ease-out forwards; } /* Adjusted timing */
.animate-fade-in-left { animation: fadeInLeft 0.8s ease-out forwards; } /* Adjusted timing */
.animate-fade-in-right { animation: fadeInRight 0.8s ease-out forwards; } /* Adjusted timing */

/* Ensure initial state for animation targets */
.video-container { opacity: 0; }
.features-content { opacity: 0; }
.service-card { opacity: 0; }
/* No need for .service-card.animate-fade-in-up here, applied dynamically */

/* Process Steps Styling */
.process-step-card { transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); overflow: hidden; cursor: pointer; } /* Smoother transition */
.process-step-card.expanded { transform: scale(1.02); box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1); z-index: 10; background-color: #ffffff; border: 1px solid rgba(var(--color-secondary-rgb, 56, 189, 248), 0.3); }
@media (min-width: 768px) { .process-step-card.expanded { transform: scale(1.03); box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.15), 0 10px 15px -5px rgba(0, 0, 0, 0.1); } }
.process-image-container { position: relative; overflow: hidden; border-radius: 0.5rem; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); transform-origin: center; height: 160px; }
@media (min-width: 768px) { .process-image-container { height: 160px; } }
.process-image-container.expanded { height: 220px; transform: scale(1.02); box-shadow: 0 8px 15px -5px rgba(0, 0, 0, 0.15), 0 4px 6px -4px rgba(0, 0, 0, 0.1); }
@media (min-width: 768px) { .process-image-container.expanded { height: 280px; transform: scale(1.05); box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1); } } /* Adjusted height */
.process-image-container img { width: 100%; height: 100%; object-fit: cover; transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1); transform-origin: center; }
.process-image-container.expanded img { transform: scale(1.05); }
@media (min-width: 768px) { .process-image-container.expanded img { transform: scale(1.1); } }
.expanded-details { max-height: 0; opacity: 0; transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease-out 0.1s, transform 0.5s cubic-bezier(0.4, 0, 0.2, 1); transform-origin: top; transform: scaleY(0.95); margin-top: 0; overflow: hidden; } /* Smoother animation */
.expanded-details.show { max-height: 800px; opacity: 1; transform: scaleY(1); margin-top: 1rem; transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease-in, transform 0.5s cubic-bezier(0.4, 0, 0.2, 1); }
@media (min-width: 768px) { .expanded-details.show { margin-top: 1rem; } }
.expanded-details li { opacity: 0; transform: translateX(10px); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); }
@media (min-width: 768px) { .expanded-details li { transform: translateX(15px); } }
.expanded-details.show li { opacity: 1; transform: translateX(0); }
/* Staggering expanded details */
.expanded-details.show li:nth-child(1) { transition-delay: 0.1s; }
.expanded-details.show li:nth-child(2) { transition-delay: 0.15s; }
.expanded-details.show li:nth-child(3) { transition-delay: 0.2s; }
.expanded-details.show li:nth-child(4) { transition-delay: 0.25s; }
.expanded-details.show li:nth-child(5) { transition-delay: 0.3s; }
.expanded-details.show li:nth-child(6) { transition-delay: 0.35s; }

/* Scrollbar Hiding */
.scrollbar-hide::-webkit-scrollbar { display: none; }
.scrollbar-hide { -ms-overflow-style: none; scrollbar-width: none; }

/* Testimonial Scrolling */
/* Calculate width: (card_width * number_of_cards) + (gap * (number_of_cards - 1)) */
/* Mobile: (85vw * 11) is complex. Approximation used. */
/* Desktop: (320px * 11) + (1rem * 10) = 3520px + 160px = 3680px */
@keyframes scroll { 0% { transform: translateX(0); } 100% { transform: translateX(calc(-85vw * 8 - 0.5rem * 7)); } } /* Adjusted for 8 originals */
@keyframes scrollDesktop { 0% { transform: translateX(0); } 100% { transform: translateX(calc(-320px * 8 - 1rem * 7)); } } /* Adjusted for 8 originals, matches 320px width + 1rem gap */

.testimonial-container { width: 100%; overflow: hidden; position: relative; padding: 0.5rem 0; }
.testimonial-scroll { display: flex; flex-wrap: nowrap; gap: 0.5rem; padding: 0.5rem 0; margin: 0; animation: scroll 45s linear infinite; width: fit-content; touch-action: pan-x; } /* Adjusted duration */
@media (min-width: 640px) { .testimonial-scroll { gap: 1rem; padding: 1rem 0; animation: scrollDesktop 60s linear infinite; } } /* Adjusted duration */
.testimonial-scroll.pause-animation { animation-play-state: paused; }
.testimonial-scroll > div { flex: 0 0 auto; width: 85vw; transition: transform 0.3s ease, box-shadow 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
@media (min-width: 640px) { .testimonial-scroll > div { width: 320px; } }
.testimonial-scroll > div:hover, .testimonial-scroll > div:active { transform: translateY(-5px); box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Focus states (unchanged) */
input:focus, select:focus, button:focus, a:focus { outline: 2px solid transparent; outline-offset: 2px; box-shadow: 0 0 0 2px var(--color-primary, #1d4ed8), 0 0 0 4px rgba(59, 130, 246, 0.4); }
.btn:focus { outline: 2px solid transparent; outline-offset: 2px; box-shadow: 0 0 0 2px var(--color-primary, #1d4ed8), 0 0 0 4px rgba(59, 130, 246, 0.4); }
.btn-secondary:focus { box-shadow: 0 0 0 2px var(--color-secondary, #38bdf8), 0 0 0 4px rgba(56, 189, 248, 0.4); }
.btn-outline:focus { box-shadow: 0 0 0 2px var(--color-primary, #1d4ed8), 0 0 0 4px rgba(59, 130, 246, 0.4); }
.btn-outline.border-white:focus { box-shadow: 0 0 0 2px white, 0 0 0 4px rgba(255, 255, 255, 0.4); }
button.border-transparent:focus { /* For desktop tabs */
    box-shadow: none; /* Remove default focus */
    outline: 2px solid var(--color-secondary, #38bdf8); /* Add custom outline */
    outline-offset: -2px; /* Adjust offset */
}
button.bg-gray-200:focus { /* For mobile tabs */
     box-shadow: 0 0 0 2px var(--color-secondary, #38bdf8), 0 0 0 4px rgba(56, 189, 248, 0.4);
}

/* Pricing Section Loading/Error Handling Styles */
#pricing {
  min-height: 300px; /* Example: Give the section a minimum height */
}
#pricing .container-custom {
  min-height: inherit; /* Allow container to fill the min-height if needed */
}
</style>