/**
 * CarPages Export API Routes
 * 
 * This file defines API routes for triggering CarPages exports.
 * It includes a secured endpoint that can be called by an external cron service.
 */

import express from 'express';
import { exportSupabaseToCarPages } from '../../scripts/export-supabase-to-carpages.js';

const router = express.Router();

// Secret key for securing the scheduled export endpoint
// This should match the EXPORT_API_KEY environment variable
const API_KEY = process.env.EXPORT_API_KEY || 'your-secret-key-here';

/**
 * @route   POST /api/carpages/export
 * @desc    Trigger a sample export to CarPages
 * @access  Admin
 */
router.post('/export', async (req, res) => {
  try {
    // This endpoint is for manual exports from the admin dashboard
    // It should be protected by your existing authentication middleware
    
    console.log('Manual export triggered from admin dashboard');
    
    // Call the export function with sample data
    // Implementation depends on your existing code
    res.json({ success: true, message: 'Export triggered successfully' });
  } catch (error) {
    console.error('Error triggering export:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

/**
 * @route   POST /api/carpages/export-supabase
 * @desc    Trigger a Supabase export to CarPages
 * @access  Admin
 */
router.post('/export-supabase', async (req, res) => {
  try {
    // This endpoint is for manual exports from the admin dashboard
    // It should be protected by your existing authentication middleware
    
    console.log('Supabase export triggered from admin dashboard');
    
    // Call the export function with Supabase data
    const result = await exportSupabaseToCarPages();
    res.json(result);
  } catch (error) {
    console.error('Error triggering Supabase export:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

/**
 * @route   POST /api/carpages/scheduled-export
 * @desc    Trigger a scheduled export to CarPages (called by cron service)
 * @access  Protected by API key
 */
router.post('/scheduled-export', async (req, res) => {
  try {
    // Verify the API key from request headers
    const providedKey = req.headers['x-api-key'];
    
    if (providedKey !== API_KEY) {
      console.warn('Unauthorized attempt to trigger scheduled export');
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }
    
    console.log('Scheduled export triggered by cron service');
    
    // Call the export function
    const result = await exportSupabaseToCarPages();
    
    // Log the result
    console.log(`Scheduled export completed: ${result.success ? 'Success' : 'Failed'}`);
    if (result.vehicleCount) {
      console.log(`Exported ${result.vehicleCount} vehicles`);
    }
    
    res.json(result);
  } catch (error) {
    console.error('Error in scheduled export:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

export default router;