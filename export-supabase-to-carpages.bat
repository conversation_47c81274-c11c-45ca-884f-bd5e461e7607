@echo off
echo ===================================================
echo GT Motorsports Supabase to CarPages Export
echo ===================================================
echo.
echo Starting Supabase to CarPages export...
echo Timestamp: %date% %time%
echo.

cd scripts

echo Installing dependencies...
call npm install
if %ERRORLEVEL% NEQ 0 (
  echo Failed to install dependencies.
  goto error
)

echo.
echo Running Supabase to CarPages export...
node export-supabase-to-carpages.js
if %ERRORLEVEL% EQU 0 (
  echo.
  echo ===================================================
  echo Export completed successfully!
  echo ===================================================
) else (
  echo.
  echo ===================================================
  echo Export failed with error code %ERRORLEVEL%.
  echo Check the logs for more details.
  echo ===================================================
  goto error
)

goto end

:error
echo.
echo Press any key to exit...
pause > nul
exit /b 1

:end
echo.
echo Press any key to exit...
pause > nul
exit /b 0