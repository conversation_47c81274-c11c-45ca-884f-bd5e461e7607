#!/usr/bin/env node

console.log('<PERSON>ript starting...');
console.log('Current directory:', process.cwd());
console.log('Node version:', process.version);
console.log('Arguments:', process.argv);

/**
 * Supabase to CarPages Export Script
 *
 * This script exports vehicle inventory data from Supabase to CarPages.
 * It fetches all vehicles from the Supabase database, formats them as a CSV,
 * and uploads the CSV to the CarPages FTP server.
 */

import fs from 'fs';
import path from 'path';
import * as ftp from 'basic-ftp';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { createClient } from '@supabase/supabase-js';
import { promises as fsPromises } from 'fs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env file in the root directory
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// CarPages FTP credentials from environment variables
const FTP_CONFIG = {
  host: process.env.CARPAGES_FTP_HOST || 'ftp.carpages.ca',
  user: process.env.CARPAGES_FTP_USER || 'GTMotor',
  password: process.env.CARPAGES_FTP_PASSWORD || 'kYP76iWb3AphEmbyX8GW',
  secure: process.env.CARPAGES_FTP_SECURE === 'true' // Server doesn't support FTPS
};

// Supabase credentials from environment variables
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_KEY = process.env.VITE_SUPABASE_KEY || process.env.VITE_SUPABASE_SERVICE_KEY;

// Field mapping from our database to CarPages format
const FIELD_MAPPING = {
  // Required fields
  'Dealer ID': 'GTMotor', // Static dealer ID - this is a literal value, not a field name
  'Vehicle ID': 'id', // Our vehicle ID
  'Year': 'year',
  'Make': 'make',
  'Model': 'model',
  
  // Optional fields
  'VIN': 'vin',
  'Stock Number': 'stockNumber',
  'Sub-model': 'trim',
  'New Flag': (vehicle) => vehicle.isNew ? 'New' : 'Used', // Default to Used if not specified
  'Transmission': (vehicle) => mapTransmission(vehicle.transmission),
  'Drive Type': (vehicle) => mapDriveType(vehicle.drivetrain || vehicle.driveTrain),
  'Odometer': 'mileage',
  'Odometer Type': 'KM', // Default to KM
  'Doors': 'doors',
  'Body Style': (vehicle) => mapBodyStyle(vehicle.bodyStyle),
  'Engine Cylinders': (vehicle) => extractCylinders(vehicle.engine),
  'Exterior Colour': 'exteriorColor',
  'Interior Colour': 'interiorColor',
  'Condition': (vehicle) => mapCondition(vehicle.condition),
  'Certified Flag': (vehicle) => vehicle.isCertified ? 'True' : 'False',
  'Fuel Type': (vehicle) => mapFuelType(vehicle.fuelType),
  'Price': 'price',
  'Sale Price': 'specialPrice',
  'Features': (vehicle) => {
    if (Array.isArray(vehicle.features)) {
      return vehicle.features.join(', ');
    } else if (typeof vehicle.features === 'string') {
      return vehicle.features;
    }
    return '';
  },
  'Comments': 'description',
  'Image URLs': (vehicle) => vehicle._imageUrls || '',
  'Video Code': (vehicle) => vehicle.videoUrl || '',
  'Last Modified Date': (vehicle) => vehicle.updatedAt || new Date().toISOString()
};

/**
 * Map transmission type to CarPages format
 * @param {string} transmission - The transmission type
 * @returns {string} - The mapped transmission code
 */
function mapTransmission(transmission) {
  if (!transmission) return 'O'; // Default to Other/Unknown
  
  const transmissionLower = transmission.toLowerCase();
  if (transmissionLower.includes('auto')) return 'A';
  if (transmissionLower.includes('manual')) return 'M';
  if (transmissionLower.includes('semi') || 
      transmissionLower.includes('tiptronic') || 
      transmissionLower.includes('dct') || 
      transmissionLower.includes('dsg')) return 'S';
  if (transmissionLower.includes('cvt')) return 'C';
  
  return 'O'; // Other/Unknown
}

/**
 * Map drive type to CarPages format
 * @param {string} driveType - The drive type
 * @returns {string} - The mapped drive type code
 */
function mapDriveType(driveType) {
  if (!driveType) return 'O'; // Default to Other/Unknown
  
  const driveLower = driveType.toLowerCase();
  if (driveLower.includes('fwd') || driveLower.includes('front')) return 'FWD';
  if (driveLower.includes('rwd') || driveLower.includes('rear')) return 'RWD';
  if (driveLower.includes('4x4') || driveLower.includes('4wd')) return '4X4';
  if (driveLower.includes('awd') || driveLower.includes('all')) return 'AWD';
  
  return 'O'; // Other/Unknown
}

/**
 * Map body style to CarPages format
 * @param {string} bodyStyle - The body style
 * @returns {string} - The mapped body style code
 */
function mapBodyStyle(bodyStyle) {
  if (!bodyStyle) return 'O'; // Default to Other/Unknown
  
  const bodyLower = bodyStyle.toLowerCase();
  if (bodyLower.includes('convertible')) return 'CV';
  if (bodyLower.includes('coupe')) return 'CO';
  if (bodyLower.includes('hatchback')) return 'HB';
  if (bodyLower.includes('van') || bodyLower.includes('minivan')) return 'VN';
  if (bodyLower.includes('truck') || bodyLower.includes('pickup')) return 'TR';
  if (bodyLower.includes('wagon')) return 'WA';
  if (bodyLower.includes('motorcycle')) return 'MC';
  if (bodyLower.includes('sedan')) return 'SD'; // Not in the guide but common
  if (bodyLower.includes('suv')) return 'SV'; // Not in the guide but common
  
  return 'O'; // Other/Unknown
}

/**
 * Extract number of cylinders from engine description
 * @param {string} engine - The engine description
 * @returns {string} - The number of cylinders
 */
function extractCylinders(engine) {
  if (!engine) return '';
  
  // Try to extract cylinder count from engine description
  const cylinderMatch = engine.match(/(\d+)[\s-]?cyl/i);
  if (cylinderMatch) return cylinderMatch[1];
  
  // Check for common engine configurations
  const engineLower = engine.toLowerCase();
  if (engineLower.includes('v8')) return '8';
  if (engineLower.includes('v6')) return '6';
  if (engineLower.includes('v4') || engineLower.includes('inline 4') || engineLower.includes('i4')) return '4';
  if (engineLower.includes('v12')) return '12';
  if (engineLower.includes('v10')) return '10';
  if (engineLower.includes('v5')) return '5';
  if (engineLower.includes('v3') || engineLower.includes('inline 3') || engineLower.includes('i3')) return '3';
  
  return ''; // Unknown
}

/**
 * Map condition to CarPages format
 * @param {string} condition - The vehicle condition
 * @returns {string} - The mapped condition code
 */
function mapCondition(condition) {
  if (!condition) return 'O'; // Default to Other/Unknown
  
  const conditionLower = condition.toLowerCase();
  if (conditionLower.includes('excellent')) return 'E';
  if (conditionLower.includes('good')) return 'G';
  if (conditionLower.includes('fair')) return 'F';
  if (conditionLower.includes('poor')) return 'P';
  
  return 'O'; // Other/Unknown
}

/**
 * Map fuel type to CarPages format
 * @param {string} fuelType - The fuel type
 * @returns {string} - The mapped fuel type code
 */
function mapFuelType(fuelType) {
  if (!fuelType) return 'O'; // Default to Other/Unknown
  
  const fuelLower = fuelType ? fuelType.toLowerCase() : '';
  if (fuelLower.includes('gas') || fuelLower.includes('petrol')) return 'G';
  if (fuelLower.includes('diesel')) return 'D';
  if (fuelLower.includes('hybrid')) return 'H';
  if (fuelLower.includes('propane')) return 'P';
  if (fuelLower.includes('flex') || fuelLower.includes('e85')) return 'F';
  if (fuelLower.includes('electric')) return 'E';
  
  return 'O'; // Other/Unknown
}

/**
 * Get the public URL for a Supabase storage object
 * @param {string} path - The path to the storage object
 * @param {string} bucket - The bucket name (default: 'car-images')
 * @returns {string} - The public URL
 */
function getPublicUrl(path, bucket = 'car-images') {
  if (!path) return '';
  
  // If the path is already a URL, return it
  if (path.startsWith('http')) {
    return path;
  }
  
  // Extract the project ID from the Supabase URL
  const projectId = SUPABASE_URL.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1];
  if (!projectId) {
    console.warn('Could not extract project ID from Supabase URL');
    return '';
  }
  
  // Construct the public URL
  // Format: https://<project-id>.supabase.co/storage/v1/object/public/<bucket>/<path>
  return `https://${projectId}.supabase.co/storage/v1/object/public/${bucket}/${path}`;
}

/**
 * List all files in a Supabase storage folder
 * @param {Object} supabase - Supabase client
 * @param {string} folderPath - The folder path in storage
 * @param {string} bucket - The bucket name (default: 'car-images')
 * @returns {Promise<Array<string>>} - Array of file paths
 */
async function listFolderFiles(supabase, folderPath, bucket = 'car-images') {
  if (!folderPath) return [];
  
  try {
    // List all files in the folder
    const { data, error } = await supabase
      .storage
      .from(bucket)
      .list(folderPath);
    
    if (error) {
      console.error(`Error listing files in folder ${folderPath}:`, error);
      return [];
    }
    
    if (!data || data.length === 0) {
      return [];
    }
    
    // Filter out folders and get only files
    const files = data.filter(item => !item.id.endsWith('/'));
    
    // Return full paths to the files
    return files.map(file => `${folderPath}/${file.name}`);
  } catch (error) {
    console.error(`Error in listFolderFiles for ${folderPath}:`, error);
    return [];
  }
}

/**
 * Get image URLs for a vehicle
 * @param {Object} vehicle - The vehicle object
 * @param {Object} supabase - Supabase client
 * @returns {Promise<string>} - Comma-separated list of image URLs
 */
async function getImageUrls(vehicle, supabase) {
  try {
    // Check if the vehicle has an image_path (folder in car-images bucket)
    if (vehicle.image_path) {
      // List all files in the folder
      const imagePaths = await listFolderFiles(supabase, vehicle.image_path);
      
      if (imagePaths.length > 0) {
        // Convert each image path to a public URL
        const publicUrls = imagePaths.map(path => getPublicUrl(path));
        return publicUrls.join(', ');
      }
    }
    
    // Fallback to gallery if available
    if (vehicle.gallery && Array.isArray(vehicle.gallery) && vehicle.gallery.length > 0) {
      // Convert each gallery item to a public URL if needed
      const publicUrls = vehicle.gallery.map(imagePath => getPublicUrl(imagePath));
      return publicUrls.join(', ');
    }
    
    // Fallback to single image if available
    if (vehicle.image) {
      return getPublicUrl(vehicle.image);
    }
    
    return '';
  } catch (error) {
    console.error('Error in getImageUrls:', error);
    return '';
  }
}

/**
 * Generate CSV content from vehicle data
 * @param {Array} vehicles - Array of vehicle objects
 * @returns {string} - CSV content
 */
function generateCsv(vehicles) {
  // Get field names from mapping
  const fieldNames = Object.keys(FIELD_MAPPING);
  
  // Create CSV header
  const header = fieldNames.join(',');
  
  // Create CSV rows
  const rows = vehicles.map(vehicle => {
    return fieldNames.map(field => {
      const mapping = FIELD_MAPPING[field];
      let value;
      
      if (typeof mapping === 'function') {
        // If mapping is a function, call it with the vehicle
        value = mapping(vehicle);
      } else if (typeof mapping === 'string') {
        // Check if this is a static value (like 'GTMotor' for Dealer ID)
        if (field === 'Dealer ID') {
          // For Dealer ID, use the mapping directly as a static value
          value = mapping;
        } else if (mapping.includes('.')) {
          // Handle nested properties
          const props = mapping.split('.');
          let nestedValue = vehicle;
          for (const prop of props) {
            nestedValue = nestedValue ? nestedValue[prop] : undefined;
          }
          value = nestedValue;
        } else if (mapping === '') {
          // If mapping is empty string, use empty value
          value = '';
        } else if (vehicle[mapping] !== undefined) {
          // If mapping is a property name, get the property value
          value = vehicle[mapping];
        } else {
          // If property doesn't exist, use empty value
          value = '';
        }
      } else {
        // If mapping is a static value, use it
        value = mapping;
      }
      
      // Format value for CSV
      if (value === undefined || value === null) {
        return '';
      } else if (typeof value === 'string') {
        // Escape quotes and wrap in quotes if contains comma or quote
        if (value.includes(',') || value.includes('"')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      } else {
        return String(value);
      }
    }).join(',');
  });
  
  // Combine header and rows
  return [header, ...rows].join('\n');
}

/**
 * Upload CSV file to CarPages FTP server
 * @param {string} csvContent - The CSV content to upload
 * @returns {Promise<boolean>} - True if upload was successful
 */
async function uploadToFtp(csvContent) {
  const client = new ftp.Client();
  client.ftp.verbose = true; // Enable verbose logging for debugging
  
  try {
    console.log('Connecting to CarPages FTP server...');
    await client.access({
      host: FTP_CONFIG.host,
      user: FTP_CONFIG.user,
      password: FTP_CONFIG.password,
      secure: FTP_CONFIG.secure
    });
    
    console.log('Connected to FTP server. Uploading inventory file...');
    
    // Create temp directory if it doesn't exist
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // Create a temporary file with the CSV content
    const tempFilePath = path.join(tempDir, 'gtmotorsports_inventory.csv');
    fs.writeFileSync(tempFilePath, csvContent);
    
    // Upload the file
    await client.uploadFrom(tempFilePath, 'gtmotorsports_inventory.csv');
    
    console.log('Inventory file uploaded successfully.');
    return true;
  } catch (error) {
    console.error('Error uploading to FTP server:', error);
    return false;
  } finally {
    client.close();
  }
}

/**
 * Export vehicles from Supabase to CarPages
 * @returns {Promise<Object>} - Result object with success status and message
 */
export async function exportSupabaseToCarPages() {
  try {
    console.log('Starting Supabase to CarPages export...');
    
    // Validate Supabase credentials
    if (!SUPABASE_URL || !SUPABASE_KEY) {
      console.error('Supabase credentials not found in environment variables');
      console.error('SUPABASE_URL:', SUPABASE_URL ? 'Found' : 'Not found');
      console.error('SUPABASE_KEY:', SUPABASE_KEY ? 'Found' : 'Not found');
      return {
        success: false,
        message: 'Supabase credentials not found in environment variables',
        csvContent: null
      };
    }
    
    console.log('Supabase credentials found:');
    console.log('SUPABASE_URL:', SUPABASE_URL);
    console.log('SUPABASE_KEY:', SUPABASE_KEY.substring(0, 10) + '...');
    
    // Initialize Supabase client
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
    console.log('Supabase client initialized');
    
    // Fetch all vehicles from Supabase
    console.log('Fetching vehicles from Supabase...');
    const { data: vehicles, error } = await supabase
      .from('GTINV')
      .select('*')
      .order('id', { ascending: true });
    
    if (error) {
      console.error('Error fetching vehicles from Supabase:', error);
      return {
        success: false,
        message: `Error fetching vehicles from Supabase: ${error.message}`,
        csvContent: null
      };
    }
    
    console.log(`Fetched ${vehicles?.length || 0} vehicles from Supabase`);
    
    if (!vehicles || vehicles.length === 0) {
      console.warn('No vehicles found to export.');
      return {
        success: false,
        message: 'No vehicles found to export.',
        csvContent: null
      };
    }
    
    // Generate image URLs for each vehicle
    console.log('Generating image URLs for vehicles...');
    for (const vehicle of vehicles) {
      const imageUrls = await getImageUrls(vehicle, supabase);
      vehicle._imageUrls = imageUrls; // Store in a temporary property
    }
    console.log('Image URLs generated.');
    
    // Generate CSV content
    const csvContent = generateCsv(vehicles);
    console.log('Generated CSV content.');
    
    // Save a local copy of the CSV
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const csvDir = path.join(process.cwd(), 'exports');
    if (!fs.existsSync(csvDir)) {
      fs.mkdirSync(csvDir, { recursive: true });
    }
    const csvFilePath = path.join(csvDir, `carpages-export-${timestamp}.csv`);
    fs.writeFileSync(csvFilePath, csvContent);
    console.log(`Saved local copy of CSV to ${csvFilePath}`);
    
    // Upload to FTP server
    const uploadSuccess = await uploadToFtp(csvContent);
    
    if (uploadSuccess) {
      console.log('Supabase to CarPages export completed successfully.');
      return {
        success: true,
        message: `Successfully exported ${vehicles.length} vehicles to CarPages.`,
        csvContent,
        vehicleCount: vehicles.length,
        csvFilePath
      };
    } else {
      console.error('Supabase to CarPages export failed during FTP upload.');
      return {
        success: false,
        message: 'Failed to upload CSV to CarPages FTP server.',
        csvContent,
        vehicleCount: vehicles.length,
        csvFilePath
      };
    }
  } catch (error) {
    console.error('Error exporting to CarPages:', error);
    return {
      success: false,
      message: `Error exporting to CarPages: ${error.message}`,
      csvContent: null
    };
  }
}

/**
 * Main export function
 */
async function runExport() {
  console.log('=================================================');
  console.log('STARTING SUPABASE TO CARPAGES EXPORT');
  console.log('=================================================');
  console.log('Starting Supabase to CarPages export...');
  console.log('Date/Time:', new Date().toLocaleString());
  
  try {
    // Generate the CSV content and upload to FTP
    const result = await exportSupabaseToCarPages();
    
    if (!result.success) {
      console.error('Failed to export to CarPages:', result.message);
      process.exit(1);
    }
    
    console.log(`Successfully exported ${result.vehicleCount} vehicles to CarPages.`);
    
    // Save a log of the successful export
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const logFile = path.join(logDir, `supabase-export-${timestamp}.log`);
    
    fs.writeFileSync(logFile, `Export completed successfully at ${new Date().toLocaleString()}\n` +
      `Exported ${result.vehicleCount} vehicles\n` +
      `CSV saved to ${result.csvFilePath}\n`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error during Supabase to CarPages export:', error);
    process.exit(1);
  }
}

// Run the export if this script is executed directly
console.log('Checking if script is executed directly:');
console.log('import.meta.url:', import.meta.url);
console.log('process.argv[1]:', process.argv[1]);
console.log('file://${process.argv[1]}:', `file://${process.argv[1]}`);

// Always run the export for now
console.log('Running export function...');
runExport();

export default {
  exportSupabaseToCarPages,
  generateCsv
};