// One-time script to update app_metadata for a pre-existing admin user
// This script sets the tenant_id in the user's app_metadata
// Run this in a secure environment with access to the Supabase service role key

import { createClient } from '@supabase/supabase-js';

// Replace these placeholder values with the actual User UID and Tenant ID
const userIdToUpdate = '64dbf91c-d906-4156-b8d4-bea2e3e69739'; // e.g., '550e8400-e29b-41d4-a716-************'
const tenantIdToSet = 'b316264d-e086-42c8-b50f-1ce5134514ee'; // e.g., '7c9e6679-7425-40de-944b-e07fc1f90ae7'

// Environment variables for Supabase connection
// These should be set in the environment where this script runs
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.error('Error: Missing required environment variables SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

async function updateUserMetadata() {
  console.log(`Updating app_metadata for user: ${userIdToUpdate}`);
  console.log(`Setting tenant_id to: ${tenantIdToSet}`);
  
  try {
    // Initialize Supabase Admin Client
    const supabase = createClient(supabaseUrl as string, supabaseServiceRoleKey as string, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Update the user's app_metadata
    const { data, error } = await supabase.auth.admin.updateUserById(
      userIdToUpdate,
      {
        app_metadata: { tenant_id: tenantIdToSet }
      }
    );
    
    if (error) {
      console.error('Error updating user metadata:', error.message);
      process.exit(1);
    }
    
    console.log('Success! User metadata updated:');
    console.log(data);
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
    process.exit(1);
  }
}

// Execute the function
updateUserMetadata();

// Usage instructions:
// 1. Install dependencies: npm install @supabase/supabase-js
// 2. Replace the placeholder values with actual User ID and Tenant ID
// 3. Set environment variables:
//    - SUPABASE_URL (e.g., https://your-project.supabase.co)
//    - SUPABASE_SERVICE_ROLE_KEY (from your Supabase project settings)
// 4. Run the script: ts-node update_admin_metadata.ts