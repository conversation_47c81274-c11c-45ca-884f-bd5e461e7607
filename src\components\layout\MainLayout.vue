<script setup>
import { ref, onMounted } from 'vue';
import TheHeader from './TheHeader.vue';
import TheFooter from './TheFooter.vue';
import ChatBot from '../ui/ChatBot.vue';

const isLoading = ref(true);

onMounted(() => {
  // Simulate loading time
  setTimeout(() => {
    isLoading.value = false;
  }, 1000);
});
</script>

<template>
  <!-- Loading screen -->
  <div v-if="isLoading" class="fixed inset-0 bg-primary flex items-center justify-center z-50">
    <div class="text-center flex flex-col items-center justify-center">
      <img src="/GTWHEEL.png" alt="GT Wheel" class="animate-spin h-16 w-16 mb-4 mx-auto" />
      <h2 class="text-xl font-heading font-bold text-white">GT Motor Sports</h2>
    </div>
  </div>

  <!-- Main application -->
  <div class="flex flex-col min-h-screen" v-else>
    <TheHeader />
    
    <main class="flex-grow">
      <!-- Slot for route component -->
      <slot></slot>
    </main>
    
    <TheFooter />
    
    <!-- Chat Bot Component -->
    <ChatBot />
  </div>
</template>

<style>
.page-enter-active,
.page-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>