/* Global styles for input fields */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="date"],
input[type="time"],
input[type="search"],
textarea,
select {
  @apply shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border border-gray-400 rounded-md px-3 py-2 bg-white hover:bg-gray-50 transition-colors duration-200 appearance-none;
}

/* Input fields with prefix ($) */
.relative.rounded-md.shadow-sm input {
  @apply pl-8;
}

/* Enhanced focus state */
input:focus,
textarea:focus,
select:focus {
  @apply outline-none ring-2 ring-primary ring-opacity-50;
}

/* Consistent placeholder styling */
::placeholder {
  @apply text-gray-400;
}

/* Disabled state */
input:disabled,
textarea:disabled,
select:disabled {
  @apply bg-gray-100 cursor-not-allowed opacity-75;
}

/* Error state */
.input-error {
  @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

/* Success state */
.input-success {
  @apply border-green-500 focus:ring-green-500 focus:border-green-500;
}
